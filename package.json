{"name": "detections-management-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "System Two Security", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "[ -n \"$HUSKY\" ] || husky", "generate:mock-rules": "ts-node src/scripts/generate-mock-rules.ts", "index:mock-rules": "ts-node src/scripts/index-mock-rules.ts", "update:mock-rules": "ts-node src/scripts/update-mock-rules.ts", "migrate:dynamodb": "ts-node src/scripts/run-dynamodb-migrations.ts", "migrate:rules": "ts-node src/scripts/migration-runner.ts", "enable-streams": "ts-node src/scripts/enable-streams.ts", "test:streams": "ts-node src/scripts/test-streams.ts", "load:mitre-data": "ts-node src/scripts/load-mitre-data.ts"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.758.0", "@aws-sdk/client-dynamodb-streams": "^3.758.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@aws-sdk/util-dynamodb": "^3.758.0", "@golevelup/ts-jest": "^0.7.0", "@google-cloud/vertexai": "^1.10.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.12", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.0.6", "@nestjs/typeorm": "^11.0.0", "@openfga/sdk": "^0.8.0", "@opensearch-project/opensearch": "^2.13.0", "@sentry/nestjs": "^9.15.0", "@systemtwosecurity/agent-kit-node": "^0.2.0", "@types/adm-zip": "^0.5.7", "@types/js-yaml": "^4.0.9", "adm-zip": "^0.5.16", "async-retry": "^1.3.3", "cache-manager": "^6.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "joi": "^17.13.3", "jwks-rsa": "^3.1.0", "keyv": "^4.5.4", "lodash": "^4.17.21", "luxon": "^3.5.0", "multer": "^2.0.0", "nest-winston": "^1.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.34.2", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@faker-js/faker": "^9.6.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "axios": "^1.8.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "js-yaml": "^4.1.0", "lint-staged": "^15.4.3", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/", "src/scripts/"]}, "lint-staged": {"*.ts": ["prettier --write"], "*.js": ["prettier --write"]}}