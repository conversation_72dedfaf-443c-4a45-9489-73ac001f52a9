import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import {
  RuleCategory,
  RulePlatform,
  RuleProductService,
} from '../models/rule-metadata.model';
import {
  RULE_CATEGORY_DISPLAY_NAMES,
  RULE_PLATFORM_DISPLAY_NAMES,
  RULE_PRODUCT_SERVICE_DISPLAY_NAMES,
} from '../constants/rule-metadata-display-names';
import { ValueDisplayNameDto } from '../../common/dto/value-display-name.dto';
import {
  ResolveSigmaLogsourceRequestDto,
  ResolveSigmaLogsourceResponseDto,
} from '../dto/resolve-sigma-logsource.dto';
import {
  UpdateYamlFromLogsourceRequestDto,
  UpdateYamlFromLogsourceResponseDto,
} from '../dto/update-yaml-from-logsource.dto';
import {
  sigmaCategoryToRuleCategoryMap,
  sigmaProductToProductServiceMap,
  sigmaServiceToProductServiceMap,
  sigmaProductToRulePlatformMap,
} from '../constants/sigma-logsource-mapping';
import { load as yamlLoad, dump as yamlDump } from 'js-yaml';

@Injectable()
export class RuleMetadataService {
  private readonly logger = new Logger(RuleMetadataService.name);

  private generateOptions<T extends string>(
    enumValues: T[],
    displayNamesMap: Record<T, string>,
    searchTerm?: string,
  ): ValueDisplayNameDto[] {
    let options = enumValues.map((enumValue) => ({
      value: enumValue,
      displayName: displayNamesMap[enumValue] || enumValue,
    }));

    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      options = options.filter(
        (option) =>
          option.value.toLowerCase().includes(lowerSearchTerm) ||
          option.displayName.toLowerCase().includes(lowerSearchTerm),
      );
    }
    return options.sort((a, b) => a.displayName.localeCompare(b.displayName));
  }

  private createMetadataOption<T extends string>(
    enumValue: T | undefined,
    displayNamesMap: Record<T, string>,
  ): ValueDisplayNameDto | undefined {
    if (!enumValue) {
      return undefined;
    }
    return {
      value: enumValue,
      displayName: displayNamesMap[enumValue] || enumValue,
    };
  }

  getCategories(searchTerm?: string): ValueDisplayNameDto[] {
    return this.generateOptions(
      Object.values(RuleCategory),
      RULE_CATEGORY_DISPLAY_NAMES,
      searchTerm,
    );
  }

  getPlatforms(searchTerm?: string): ValueDisplayNameDto[] {
    return this.generateOptions(
      Object.values(RulePlatform),
      RULE_PLATFORM_DISPLAY_NAMES,
      searchTerm,
    );
  }

  getProductServices(searchTerm?: string): ValueDisplayNameDto[] {
    return this.generateOptions(
      Object.values(RuleProductService),
      RULE_PRODUCT_SERVICE_DISPLAY_NAMES,
      searchTerm,
    );
  }

  resolveSigmaLogsource(
    dto: ResolveSigmaLogsourceRequestDto,
  ): ResolveSigmaLogsourceResponseDto {
    const resolvedCategoriesSet = new Set<RuleCategory>();
    const resolvedPlatformsSet = new Set<RulePlatform>();
    const resolvedProductServicesSet = new Set<RuleProductService>();

    if (dto.category) {
      const mappedCategory =
        sigmaCategoryToRuleCategoryMap[dto.category.toLowerCase()];
      if (mappedCategory) {
        resolvedCategoriesSet.add(mappedCategory);
      }
    }

    if (dto.product) {
      const lowerProduct = dto.product.toLowerCase();
      const mappedPlatform = sigmaProductToRulePlatformMap[lowerProduct];
      if (mappedPlatform) {
        resolvedPlatformsSet.add(mappedPlatform);
      }
      const mappedProductService =
        sigmaProductToProductServiceMap[lowerProduct];
      if (mappedProductService) {
        resolvedProductServicesSet.add(mappedProductService);
      }
    }

    if (dto.service) {
      const mappedService =
        sigmaServiceToProductServiceMap[dto.service.toLowerCase()];
      if (mappedService) {
        resolvedProductServicesSet.add(mappedService);
      }
    }

    const resolvedCategories = Array.from(resolvedCategoriesSet)
      .map((cat) => this.createMetadataOption(cat, RULE_CATEGORY_DISPLAY_NAMES))
      .filter((opt): opt is ValueDisplayNameDto => opt !== undefined);

    const resolvedPlatforms = Array.from(resolvedPlatformsSet)
      .map((plat) =>
        this.createMetadataOption(plat, RULE_PLATFORM_DISPLAY_NAMES),
      )
      .filter((opt): opt is ValueDisplayNameDto => opt !== undefined);

    const resolvedProductServices = Array.from(resolvedProductServicesSet)
      .map((ps) =>
        this.createMetadataOption(ps, RULE_PRODUCT_SERVICE_DISPLAY_NAMES),
      )
      .filter((opt): opt is ValueDisplayNameDto => opt !== undefined);

    return {
      resolvedCategories: resolvedCategories.sort((a, b) =>
        a.displayName.localeCompare(b.displayName),
      ),
      resolvedPlatforms: resolvedPlatforms.sort((a, b) =>
        a.displayName.localeCompare(b.displayName),
      ),
      resolvedProductServices: resolvedProductServices.sort((a, b) =>
        a.displayName.localeCompare(b.displayName),
      ),
    };
  }

  private findSigmaKey(
    targetValue: string | undefined,
    mappingObject: Record<string, string | undefined>,
  ): string | undefined {
    if (!targetValue) return undefined;
    for (const key in mappingObject) {
      if (mappingObject[key] === targetValue) {
        return key;
      }
    }
    return undefined;
  }

  /**
   * Insert logsource field at the correct position in the YAML object
   * @param parsedYaml The parsed YAML object
   * @param logsource The logsource object to insert
   * @param logsourceExists Whether logsource already existed in the original YAML
   * @returns New YAML object with logsource in the correct position
   */
  private insertLogsourceAtCorrectPosition(
    parsedYaml: Record<string, any>,
    logsource: Record<string, any>,
    logsourceExists: boolean,
  ): Record<string, any> {
    if (logsourceExists) {
      // If logsource already existed, just update it in place
      parsedYaml['logsource'] = logsource;
      return parsedYaml;
    }

    // If logsource didn't exist, insert it before 'detection' field
    const newYaml: Record<string, any> = {};
    const keys = Object.keys(parsedYaml);

    for (const key of keys) {
      if (key === 'detection') {
        // Insert logsource before detection
        newYaml['logsource'] = logsource;
      }
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      newYaml[key] = parsedYaml[key];
    }

    // If detection field doesn't exist, just add logsource at the end
    if (!('detection' in parsedYaml)) {
      newYaml['logsource'] = logsource;
    }

    return newYaml;
  }

  updateYamlFromLogsource(
    dto: UpdateYamlFromLogsourceRequestDto,
  ): UpdateYamlFromLogsourceResponseDto {
    let parsedYaml: Record<string, any>;
    try {
      const loadedYaml = yamlLoad(dto.currentYaml);
      if (typeof loadedYaml !== 'object' || loadedYaml === null) {
        this.logger.error(
          'Failed to parse YAML or YAML is not an object type.',
        );
        throw new BadRequestException('Invalid YAML structure: not an object.');
      }
      parsedYaml = loadedYaml as Record<string, any>;
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      this.logger.error('Error parsing YAML: ' + errorMessage);
      throw new BadRequestException('Invalid YAML: ' + errorMessage);
    }

    const rawLogsource = parsedYaml['logsource'] as unknown;
    const logsourceExists = 'logsource' in parsedYaml;
    let logsource: Record<string, any>;

    if (typeof rawLogsource === 'object' && rawLogsource !== null) {
      logsource = { ...rawLogsource } as Record<string, any>; // Create a copy to preserve existing fields
    } else {
      logsource = {};
    }

    // Handle category updates
    if (dto.categories.length === 0) {
      // User wants to remove category
      delete logsource['category'];
    } else {
      // User wants to set category
      const categoryDto = dto.categories[0];
      const sigmaCategoryKey = this.findSigmaKey(
        categoryDto.value,
        sigmaCategoryToRuleCategoryMap,
      );
      if (sigmaCategoryKey) {
        logsource['category'] = sigmaCategoryKey;
      }
      // If no mapping found, preserve existing category field
    }

    // Handle product and service updates
    let productFromPlatform: string | undefined;
    if (dto.platforms.length > 0) {
      const platformDto = dto.platforms[0];
      productFromPlatform = this.findSigmaKey(
        platformDto.value,
        sigmaProductToRulePlatformMap,
      );
    }

    let productFromService: string | undefined;
    let serviceFromProductService: string | undefined;

    // Handle service updates
    if (dto.productServices.length === 0) {
      // User wants to remove service, but we need to check if product is being set by platform
      // Only remove service if we're not setting product from productServices
      delete logsource['service'];
    } else {
      const psDto = dto.productServices[0];
      serviceFromProductService = this.findSigmaKey(
        psDto.value,
        sigmaServiceToProductServiceMap,
      );
      if (!serviceFromProductService) {
        productFromService = this.findSigmaKey(
          psDto.value,
          sigmaProductToProductServiceMap,
        );
      }

      // Update service if we found a mapping
      if (serviceFromProductService) {
        logsource['service'] = serviceFromProductService;
      }
      // If no mapping found, preserve existing service field
    }

    // Handle product updates
    const shouldUpdateProduct =
      dto.platforms.length > 0 || dto.productServices.length > 0;
    const shouldRemoveProduct =
      dto.platforms.length === 0 && dto.productServices.length === 0;

    if (shouldRemoveProduct) {
      delete logsource['product'];
    } else if (shouldUpdateProduct) {
      if (productFromService) {
        logsource['product'] = productFromService;
      } else if (productFromPlatform) {
        logsource['product'] = productFromPlatform;
      }
      // If no mapping found, preserve existing product field
    }

    // Handle logsource field placement and removal
    if (Object.keys(logsource).length === 0) {
      // Remove logsource section if it's completely empty
      delete parsedYaml['logsource'];
    } else {
      // Place logsource in the correct position
      parsedYaml = this.insertLogsourceAtCorrectPosition(
        parsedYaml,
        logsource,
        logsourceExists,
      );
    }

    try {
      const updatedYaml = yamlDump(parsedYaml, { skipInvalid: true });
      return { updatedYaml };
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      this.logger.error('Error dumping YAML: ' + errorMessage);
      throw new InternalServerErrorException(
        'Error generating YAML: ' + errorMessage,
      );
    }
  }
}
