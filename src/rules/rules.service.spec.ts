import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { CounterChange, RulesService } from './rules.service';
import { cleanMitreAttackMetadata } from '../mitre/utils/mitre-metadata.utils';
import { RuleRepository } from './repositories/rule.repository';
import { S3Service } from '../s3/s3.service';
import { ConfigService } from '@nestjs/config';
import {
  Rule,
  RuleType,
  CreateRuleDto,
  UpdateRuleDto,
  RuleStatus,
} from './models/rule.model';
import { RulePlatform, RuleCategory } from './models/rule-metadata.model';
import {
  MitreAttackObjectType,
  MitreAttackObject,
  RuleMetadata,
} from './models/rule-metadata.model';
import { S3SignedUrlOperation } from '../s3/s3.types';
import * as AdmZip from 'adm-zip';
import { StoredRule } from './schemas/rule.schema';
import { v4 as uuidv4 } from 'uuid';
import { RuleInteractionType } from '../rule-interactions/models/rule-interaction.model';
import { OpenSearchService } from '../opensearch/opensearch.service';
import { OpenSearchConfigService } from '../opensearch/config/opensearch.config';

// Mock the uuid module
const mockUuidv4 = jest.fn(() => 'mocked-uuid');
jest.mock('uuid', () => ({ v4: () => mockUuidv4() }));

describe('RulesService', () => {
  let service: RulesService;
  let ruleRepository: jest.Mocked<RuleRepository>;
  let s3Service: jest.Mocked<S3Service>;
  let configService: jest.Mocked<ConfigService>;
  let cacheManager: jest.Mocked<any>;
  let mockOpenSearchService: jest.Mocked<OpenSearchService>;
  let mockOpenSearchConfigService: jest.Mocked<OpenSearchConfigService>;

  const mockRule: Rule = {
    id: 'rule-id-1',
    title: { value: 'Test Rule' },
    description: 'A test rule for unit tests',
    rule_type: RuleType.SIGMA,
    content: 'rule content',
    tags: ['test', 'security'],
    metadata: {
      severity: 'high',
      tags: ['test', 'security'],
    },
    test_cases: [],
    created_by: 'author-id-1',
    status: RuleStatus.DRAFT,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    version: 1,
    group_id: undefined,
    group_name: undefined,
    published_at: undefined,
    contributor: 'author-username-1',
    likes: 0,
    downloads: 0,
    dislikes: 0,
    bookmarks: 0,
    ai_generated: {
      description: false,
      title: false,
      content: false,
      tags: false,
    },
  };

  const mockStoredRule: StoredRule = {
    id: 'rule-id-1',
    title: 'Test Rule',
    description: 'A test rule for unit tests',
    ai_generated: {
      description: false,
      title: false,
      content: false,
      tags: false,
    },
    rule_type: RuleType.SIGMA,
    content: 'rule content',
    tags: ['test', 'security'],
    metadata: {
      author: undefined,
      data_sources: undefined,
      date: undefined,
      false_positives: undefined,
      mitre_attack: undefined,
      mitre_tactics: undefined,
      mitre_techniques: undefined,
      modified: undefined,
      platforms: undefined,
      references: undefined,
      severity: 'high',
      tags: ['test', 'security'],
    },
    test_cases: [],
    created_by: 'author-id-1',
    owner_id: 'author-id-1',
    group_name: null,
    created_at: new Date(mockRule.created_at).getTime(),
    updated_at: new Date(mockRule.updated_at).getTime(),
    published_at: new Date(mockRule.created_at).getTime(),
    status: RuleStatus.DRAFT,
    version: 1,
    contributor: 'author-username-1',
    likes: 0,
    downloads: 0,
    dislikes: 0,
    bookmarks: 0,
  };

  const mockRules: Rule[] = [
    mockRule,
    {
      ...mockRule,
      id: 'rule-id-2',
      title: { value: 'Another Test Rule' },
      rule_type: RuleType.KQL,
      tags: ['test', 'performance'],
    },
  ];

  const mockStoredRules: StoredRule[] = mockRules.map((rule) => ({
    ...rule,
    owner_id: rule.created_by,
    created_at: new Date(rule.created_at).getTime(),
    updated_at: new Date(rule.updated_at).getTime(),
    published_at: new Date(rule.created_at).getTime(),
    group_id: undefined,
    group_name: null,
    title: rule.title ? rule.title.value : null,
    description: rule.description || '',
    ai_generated: {
      description: false,
      title: false,
      content: false,
      tags: false,
    },
    metadata: rule.metadata || {
      tags: [],
      severity: undefined,
      author: undefined,
      data_sources: undefined,
      date: undefined,
      false_positives: undefined,
      mitre_attack: undefined,
      mitre_tactics: undefined,
      mitre_techniques: undefined,
      modified: undefined,
      platforms: undefined,
      references: undefined,
      product_services: undefined,
      categories: undefined,
      associated_cves: undefined,
    },
  }));

  // Base metadata objects for testing
  const baseMitreObject = {
    id: 'attack-pattern--123',
    mitre_id: 'T1234',
    name: 'Test Technique',
    parent_name: 'Parent Technique',
    type: MitreAttackObjectType.TECHNIQUE,
  };

  const baseMetadataWithMitre = {
    severity: 'high' as const,
    mitre_attack: [baseMitreObject],
    tags: ['test'],
  };

  const baseMetadataWithoutMitre = {
    severity: 'medium' as const,
    tags: ['test'],
  };

  const mockOpenSearch = {
    connect: jest.fn(),
    disconnect: jest.fn(),
    getClient: jest.fn(),
    indexExists: jest.fn(),
    createIndex: jest.fn(),
    updateMappings: jest.fn(),
    search: jest.fn(),
    getAliasTarget: jest.fn(),
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset uuid mock
    jest.spyOn(require('uuid'), 'v4').mockReturnValue('mocked-uuid');

    // Create mock implementations
    const ruleRepositoryMock = {
      create: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      batchGet: jest.fn(),
      findByIds: jest.fn(),
      incrementCounter: jest.fn(),
      decrementCounter: jest.fn(),
      countRulesByUserOrGroup: jest.fn(),
      findFollowedRules: jest.fn(),
      findRulesByGroupId: jest.fn(),
      resetUserPublishedRulesToDraft: jest.fn(),
      findAllRulesCreatedByUser: jest.fn(),
      updateMyRulesUsername: jest.fn(),
    };

    const s3ServiceMock = {
      getSignedUrl: jest.fn(),
      uploadFile: jest.fn(),
    };

    const configServiceMock = {
      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
        if (key === 'RULES_DOWNLOAD_S3_BUCKET_NAME')
          return 'test-download-bucket';
        if (key === 'RULES_UPLOAD_S3_BUCKET_NAME') return 'test-upload-bucket';
        if (key === 'RULES_DOWNLOAD_URL_EXPIRY') return '3600';
        return defaultValue;
      }),
    };

    const cacheManagerMock = {
      get: jest.fn(),
      del: jest.fn(),
      set: jest.fn(),
      clear: jest.fn(),
    };

    const openSearchConfigMock = {
      getIndexName: jest.fn().mockReturnValue('mocked_index_name'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RulesService,
        {
          provide: RuleRepository,
          useValue: ruleRepositoryMock,
        },
        {
          provide: S3Service,
          useValue: s3ServiceMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: OpenSearchService,
          useValue: mockOpenSearch,
        },
        {
          provide: OpenSearchConfigService,
          useValue: openSearchConfigMock,
        },
      ],
    }).compile();

    service = module.get<RulesService>(RulesService);
    ruleRepository = module.get(RuleRepository);
    s3Service = module.get(S3Service);
    configService = module.get(ConfigService);
    mockOpenSearchService = module.get(OpenSearchService);
    mockOpenSearchConfigService = module.get(OpenSearchConfigService);

    // Mock Date.now() to return a fixed timestamp
    jest
      .spyOn(Date, 'now')
      .mockReturnValue(new Date('2023-01-01T00:00:00Z').getTime());

    // Mock Date.prototype.toISOString() to return a fixed timestamp
    jest
      .spyOn(Date.prototype, 'toISOString')
      .mockReturnValue('2023-01-01T00:00:00Z');
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('create', () => {
    it('should create a draft rule with all required fields', async () => {
      const createRuleDto: CreateRuleDto = {
        title: 'New Rule',
        description: 'A new rule description',
        rule_type: RuleType.SIGMA,
        content: 'rule content',
      };

      const expectedStoredRule: StoredRule = {
        id: 'mocked-uuid',
        title: 'New Rule',
        description: 'A new rule description',
        ai_generated: {
          description: false,
          title: false,
          content: false,
          tags: false,
        },
        rule_type: RuleType.SIGMA,
        status: RuleStatus.DRAFT,
        content: 'rule content',
        tags: [],
        metadata: {
          author: undefined,
          data_sources: undefined,
          date: '2023-01-01T00:00:00Z',
          false_positives: undefined,
          mitre_attack: undefined,
          mitre_tactics: undefined,
          mitre_techniques: undefined,
          modified: '2023-01-01T00:00:00Z',
          platforms: undefined,
          references: undefined,
          severity: undefined,
          tags: [],
          categories: undefined,
          product_services: undefined,
          associated_cves: undefined,
        },
        test_cases: [],
        created_by: 'author-id-1',
        owner_id: 'author-id-1',
        group_name: null,
        created_at: new Date('2023-01-01T00:00:00Z').getTime(),
        updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
        published_at: undefined,
        version: 1,
        contributor: 'author-username-1',
        likes: 0,
        downloads: 0,
        dislikes: 0,
        bookmarks: 0,
      };

      const expectedReturnedRule: Rule = {
        ...createRuleDto,
        title: createRuleDto.title ? { value: createRuleDto.title } : undefined,
        id: 'mocked-uuid',
        created_by: 'author-id-1',
        contributor: 'author-username-1',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        published_at: undefined,
        version: 1,
        status: RuleStatus.DRAFT,
        tags: [],
        metadata: {
          tags: [],
          author: undefined,
          data_sources: undefined,
          date: '2023-01-01T00:00:00Z',
          false_positives: undefined,
          mitre_attack: undefined,
          mitre_tactics: undefined,
          mitre_techniques: undefined,
          modified: '2023-01-01T00:00:00Z',
          platforms: undefined,
          references: undefined,
          severity: undefined,
          categories: undefined,
          product_services: undefined,
          associated_cves: undefined,
        },
        test_cases: [],
        group_id: undefined,
        group_name: undefined,
        likes: 0,
        downloads: 0,
        dislikes: 0,
        bookmarks: 0,
        ai_generated: {
          description: false,
          title: false,
          content: false,
          tags: false,
        },
      };

      ruleRepository.create.mockResolvedValue(expectedStoredRule);

      const result = await service.create(
        createRuleDto,
        'author-id-1',
        'author-username-1',
      );

      expect(ruleRepository.create).toHaveBeenCalledWith(expectedStoredRule);
      expect(result).toEqual(expectedReturnedRule);
    });

    it('should create a published new rule with all required fields', async () => {
      const createRuleDto: CreateRuleDto = {
        title: 'New Rule',
        description: 'A new rule description',
        rule_type: RuleType.SIGMA,
        content: 'rule content',
        status: RuleStatus.PUBLISHED,
        group_id: 'group-id-1',
      };

      const expectedStoredRule: StoredRule = {
        id: 'mocked-uuid',
        title: 'New Rule',
        description: 'A new rule description',
        ai_generated: {
          description: false,
          title: false,
          content: false,
          tags: false,
        },
        rule_type: RuleType.SIGMA,
        status: RuleStatus.PUBLISHED,
        content: 'rule content',
        tags: [],
        metadata: {
          tags: [],
          author: undefined,
          severity: undefined,
          platforms: undefined,
          data_sources: undefined,
          mitre_tactics: undefined,
          mitre_techniques: undefined,
          date: '2023-01-01T00:00:00Z',
          modified: '2023-01-01T00:00:00Z',
          false_positives: undefined,
          mitre_attack: undefined,
          references: undefined,
          categories: undefined,
          product_services: undefined,
          associated_cves: undefined,
        },
        test_cases: [],
        created_by: 'user123',
        owner_id: 'group-id-1',
        created_at: new Date('2023-01-01T00:00:00Z').getTime(),
        updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
        likes: 0,
        downloads: 0,
        dislikes: 0,
        bookmarks: 0,
        published_at: new Date('2023-01-01T00:00:00Z').getTime(),
        version: 1,
        contributor: 'author-username-1',
        group_name: 'group-name-1',
      };

      const expectedReturnedRule: Rule = {
        ...createRuleDto,
        title: createRuleDto.title ? { value: createRuleDto.title } : undefined,
        id: 'mocked-uuid',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        published_at: '2023-01-01T00:00:00Z',
        version: 1,
        status: RuleStatus.PUBLISHED,
        tags: [],
        metadata: {
          tags: [],
          author: undefined,
          severity: undefined,
          platforms: undefined,
          data_sources: undefined,
          mitre_tactics: undefined,
          mitre_techniques: undefined,
          date: '2023-01-01T00:00:00Z',
          modified: '2023-01-01T00:00:00Z',
          false_positives: undefined,
          mitre_attack: undefined,
          references: undefined,
          categories: undefined,
          product_services: undefined,
          associated_cves: undefined,
        },
        test_cases: [],
        group_id: 'group-id-1',
        group_name: 'group-name-1',
        contributor: 'author-username-1',
        likes: 0,
        downloads: 0,
        dislikes: 0,
        bookmarks: 0,
        ai_generated: {
          description: false,
          title: false,
          content: false,
          tags: false,
        },
      };

      ruleRepository.create.mockResolvedValue(expectedStoredRule);

      const result = await service.create(
        createRuleDto,
        'user123',
        'author-username-1',
        'group-name-1',
      );

      expect(ruleRepository.create).toHaveBeenCalledWith(expectedStoredRule);
      expect(result).toEqual(expectedReturnedRule);
    });
  });

  describe('findOne', () => {
    it('should return a rule when it exists', async () => {
      // Arrange
      ruleRepository.findOne.mockResolvedValue(mockStoredRule);

      // Act
      const result = await service.findOne('rule-id-1');

      // Assert
      expect(ruleRepository.findOne).toHaveBeenCalledWith({ id: 'rule-id-1' });
      expect(result).toEqual(mockRule);
    });

    it('should throw NotFoundException when rule is not found', async () => {
      // Arrange
      ruleRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne('non-existent-id')).rejects.toThrow(
        new NotFoundException('Rule with ID non-existent-id not found'),
      );
    });
  });

  describe('update', () => {
    const mockTimestamp = new Date('2023-01-01T00:00:00Z').getTime();

    beforeEach(() => {
      // Mock Date.now() in beforeEach to ensure it's set up before each test
      jest.spyOn(Date, 'now').mockReturnValue(mockTimestamp);
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should update a rule when it exists', async () => {
      // Arrange
      const updateRuleDto: UpdateRuleDto = {
        title: 'Updated Rule Title',
      };

      // This is the mock object that ruleRepository.update.mockResolvedValue() will return.
      // It needs to accurately reflect the state AFTER the intended update.
      const updatedRule: StoredRule = {
        ...mockStoredRule, // mockStoredRule has status: DRAFT and published_at: some_timestamp
        title: 'Updated Rule Title',
        updated_at: mockTimestamp,
        published_at: undefined, // Corrected: DRAFT rules should have published_at as undefined or null for StoredRule
        metadata: {
          ...mockStoredRule.metadata,
          modified: '2023-01-01T00:00:00Z', // Corrected: Ensure 'modified' is present
        },
      };

      ruleRepository.findOne.mockResolvedValue(mockStoredRule);
      ruleRepository.update.mockResolvedValue(updatedRule);

      // Act
      const result = await service.update('rule-id-1', updateRuleDto);

      // Assert
      expect(ruleRepository.findOne).toHaveBeenCalledWith({ id: 'rule-id-1' });
      expect(ruleRepository.update).toHaveBeenCalledWith(
        { id: 'rule-id-1' },
        {
          ...mockStoredRule,
          title: 'Updated Rule Title',
          updated_at: mockTimestamp,
          published_at: undefined,
          metadata: {
            ...mockStoredRule.metadata,
            modified: '2023-01-01T00:00:00Z',
          },
        },
      );
      // Corrected assertion for the 'update' test (around original line 589)
      expect(result).toEqual({
        ...mockRule,
        title: { value: 'Updated Rule Title' }, // Corrected to RuleTitleObject
        updated_at: '2023-01-01T00:00:00Z',
        metadata: {
          // Start with all fields from mockStoredRule.metadata
          ...mockStoredRule.metadata,
          // Override severity and tags from mockRule.metadata if they exist (they do in this case)
          severity: mockRule.metadata?.severity,
          tags: mockRule.metadata?.tags,
          // Ensure 'modified' is set to the expected timestamp
          modified: '2023-01-01T00:00:00Z',
        },
      });
    });

    it('should throw NotFoundException when rule is not found during find', async () => {
      // Arrange
      ruleRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.update('non-existent-id', { title: 'Updated Title' }),
      ).rejects.toThrow(
        new NotFoundException('Rule with ID non-existent-id not found'),
      );
    });

    it('should throw NotFoundException when update fails', async () => {
      // Arrange
      ruleRepository.findOne.mockResolvedValue(mockStoredRule);
      ruleRepository.update.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.update('rule-id-1', { title: 'Updated Title' }),
      ).rejects.toThrow(
        new NotFoundException('Rule with ID rule-id-1 not found'),
      );
    });

    it('should clear group_id, group_name, and set owner_id to created_by when status changes from PUBLISHED to DRAFT', async () => {
      const existingRuleId = 'published-rule-id';
      const creatorId = 'user-abc-123';
      const groupId = 'group-xyz-789';
      const groupName = 'Test Group Name';

      const mockExistingPublishedStoredRule: StoredRule = {
        ...mockStoredRule,
        id: existingRuleId,
        created_by: creatorId,
        status: RuleStatus.PUBLISHED,
        owner_id: groupId, // Initially owned by the group
        group_name: groupName,
        published_at: new Date('2023-01-01T00:00:00Z').getTime(),
      };

      // The service's findOne maps StoredRule to Rule DTO
      const mockExistingPublishedRuleDto: Rule = {
        ...mockRule, // Use mockRule as a base
        id: existingRuleId,
        created_by: creatorId,
        contributor: 'test-contributor',
        status: RuleStatus.PUBLISHED,
        group_id: groupId,
        group_name: groupName,
        published_at: '2023-01-01T00:00:00Z', // Published_at should be defined
        updated_at: '2023-01-01T00:00:00Z',
        created_at: '2023-01-01T00:00:00Z',
      };

      ruleRepository.findOne.mockResolvedValue(mockExistingPublishedStoredRule);

      const updateRuleDto: UpdateRuleDto = {
        status: RuleStatus.DRAFT,
        title: 'Updated Title for Draft Transition',
      };

      const ruleObjectForMapping: Rule = {
        ...mockExistingPublishedRuleDto, // base: title is RuleTitleObject
        ...updateRuleDto, // override: title becomes string here temporarily
        status: RuleStatus.DRAFT, // specific overrides
        group_id: undefined,
        group_name: undefined,
        updated_at: '2023-01-01T00:00:00Z',
        published_at: '2023-01-01T00:00:00Z',
      } as any; // Cast to any to temporarily bypass type error for title

      // Now, explicitly set the title to the correct type
      if (updateRuleDto.title && typeof updateRuleDto.title === 'string') {
        ruleObjectForMapping.title = { value: updateRuleDto.title };
      } else {
        // if updateRuleDto.title is undefined (or not a string, though DTO implies string or undefined)
        ruleObjectForMapping.title = mockExistingPublishedRuleDto.title;
      }

      const expectedUpdatedStoredRule: StoredRule = {
        ...mockExistingPublishedStoredRule,
        ...service['mapRuleToStoredRule'](ruleObjectForMapping), // Now cast back to Rule
        owner_id: creatorId,
        group_name: null, // StoredRule maps undefined group_name to null
        status: RuleStatus.DRAFT,
        updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
        // published_at remains as is in StoredRule, but DTO will clear it
      };
      // Adjust the expected StoredRule after mapping group_name specifically
      expectedUpdatedStoredRule.group_name = null;

      ruleRepository.update.mockResolvedValue(expectedUpdatedStoredRule);

      const result = await service.update(existingRuleId, updateRuleDto);

      expect(ruleRepository.findOne).toHaveBeenCalledWith({
        id: existingRuleId,
      });
      expect(ruleRepository.update).toHaveBeenCalledTimes(1);

      const updateCallArgs = ruleRepository.update.mock.calls[0];
      expect(updateCallArgs[0]).toEqual({ id: existingRuleId }); // Query condition

      const updatedStoredRulePayload = updateCallArgs[1] as StoredRule;

      expect(updatedStoredRulePayload.group_name).toBeNull();
      expect(updatedStoredRulePayload.owner_id).toEqual(creatorId);
      expect(updatedStoredRulePayload.status).toEqual(RuleStatus.DRAFT);
      // Check that published_at is not changed in the stored rule by this specific logic path
      // but its DTO representation will be undefined.
      expect(updatedStoredRulePayload.published_at).toEqual(
        new Date('2023-01-01T00:00:00Z').getTime(),
      );

      expect(result.group_id).toBeUndefined();
      expect(result.group_name).toBeUndefined();
      expect(result.status).toEqual(RuleStatus.DRAFT);
      expect(result.published_at).toBeUndefined(); // Key DTO assertion
    });

    describe('metadata handling', () => {
      const mockCurrentTime = '2023-06-01T12:00:00.000Z';

      beforeEach(() => {
        // Mock Date.now() and Date.prototype.toISOString() to return consistent timestamp
        jest
          .spyOn(Date, 'now')
          .mockReturnValue(new Date(mockCurrentTime).getTime());
        jest
          .spyOn(Date.prototype, 'toISOString')
          .mockReturnValue(mockCurrentTime);
      });

      afterEach(() => {
        jest.restoreAllMocks();
      });

      it('should set modified to current date when no metadata changes are provided', async () => {
        // Arrange
        const existingRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'medium',
            author: 'original-author',
          },
        };

        const updateRuleDto: UpdateRuleDto = {
          title: 'Updated Title Only',
        };

        const expectedUpdatedRule: StoredRule = {
          ...existingRule,
          title: 'Updated Title Only',
          updated_at: new Date(mockCurrentTime).getTime(),
          metadata: {
            ...existingRule.metadata,
            modified: mockCurrentTime,
          },
        };

        ruleRepository.findOne.mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(expectedUpdatedRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(ruleRepository.update).toHaveBeenCalledWith(
          { id: 'rule-id-1' },
          expect.objectContaining({
            title: 'Updated Title Only',
            metadata: expect.objectContaining({
              severity: 'medium',
              author: 'original-author',
              modified: mockCurrentTime,
            }),
          }),
        );

        expect(result.metadata).toEqual(
          expect.objectContaining({
            severity: 'medium',
            author: 'original-author',
            modified: mockCurrentTime,
          }),
        );
      });

      it('should merge metadata and set modified to current date when metadata changes but no modified field provided', async () => {
        // Arrange
        const existingRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'low',
            author: 'original-author',
            platforms: [RulePlatform.WINDOWS],
          },
        };

        const updateRuleDto: UpdateRuleDto = {
          metadata: {
            severity: 'high',
            platforms: [RulePlatform.LINUX, RulePlatform.MACOS],
          },
        };

        const expectedUpdatedRule: StoredRule = {
          ...existingRule,
          updated_at: new Date(mockCurrentTime).getTime(),
          metadata: {
            ...existingRule.metadata,
            severity: 'high',
            platforms: [RulePlatform.LINUX, RulePlatform.MACOS],
            modified: mockCurrentTime, // auto-set
          },
        };

        ruleRepository.findOne.mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(expectedUpdatedRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(ruleRepository.update).toHaveBeenCalledWith(
          { id: 'rule-id-1' },
          expect.objectContaining({
            metadata: expect.objectContaining({
              severity: 'high',
              author: 'original-author', // preserved
              platforms: [RulePlatform.LINUX, RulePlatform.MACOS], // updated
              modified: mockCurrentTime, // auto-set
            }),
          }),
        );

        expect(result.metadata).toEqual(
          expect.objectContaining({
            severity: 'high',
            author: 'original-author',
            platforms: [RulePlatform.LINUX, RulePlatform.MACOS],
            modified: mockCurrentTime,
          }),
        );
      });

      it('should use provided modified value when explicitly set in metadata', async () => {
        // Arrange
        const customModifiedTime = '2023-05-15T10:30:00.000Z';
        const existingRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'medium',
            modified: '2023-04-01T08:00:00.000Z',
          },
        };

        const updateRuleDto: UpdateRuleDto = {
          metadata: {
            severity: 'high',
            modified: customModifiedTime,
          },
        };

        const expectedUpdatedRule: StoredRule = {
          ...existingRule,
          updated_at: new Date(mockCurrentTime).getTime(),
          metadata: {
            ...existingRule.metadata,
            severity: 'high',
            modified: customModifiedTime,
          },
        };

        ruleRepository.findOne.mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(expectedUpdatedRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(ruleRepository.update).toHaveBeenCalledWith(
          { id: 'rule-id-1' },
          expect.objectContaining({
            metadata: expect.objectContaining({
              severity: 'high',
              modified: customModifiedTime, // uses provided value
            }),
          }),
        );

        expect(result.metadata).toEqual(
          expect.objectContaining({
            severity: 'high',
            modified: customModifiedTime,
          }),
        );
      });

      it('should preserve existing metadata fields while adding new ones and setting modified', async () => {
        // Arrange
        const existingRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'medium',
            author: 'john-doe',
            platforms: [RulePlatform.WINDOWS],
            references: ['https://example.com'],
          },
        };

        const updateRuleDto: UpdateRuleDto = {
          metadata: {
            // Update existing field
            severity: 'critical',
            // Add new fields
            mitre_tactics: ['defense-evasion'],
            categories: [RuleCategory.MALWARE_DETECTED],
          },
        };

        const expectedUpdatedRule: StoredRule = {
          ...existingRule,
          updated_at: new Date(mockCurrentTime).getTime(),
          metadata: {
            ...existingRule.metadata,
            severity: 'critical', // updated
            author: 'john-doe', // preserved
            platforms: [RulePlatform.WINDOWS], // preserved
            references: ['https://example.com'], // preserved
            mitre_tactics: ['defense-evasion'], // added
            categories: [RuleCategory.MALWARE_DETECTED], // added
            modified: mockCurrentTime, // auto-set
          },
        };

        ruleRepository.findOne.mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(expectedUpdatedRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(ruleRepository.update).toHaveBeenCalledWith(
          { id: 'rule-id-1' },
          expect.objectContaining({
            metadata: expect.objectContaining({
              severity: 'critical',
              author: 'john-doe',
              platforms: [RulePlatform.WINDOWS],
              references: ['https://example.com'],
              mitre_tactics: ['defense-evasion'],
              categories: [RuleCategory.MALWARE_DETECTED],
              modified: mockCurrentTime,
            }),
          }),
        );

        expect(result.metadata).toEqual(
          expect.objectContaining({
            severity: 'critical',
            author: 'john-doe',
            platforms: [RulePlatform.WINDOWS],
            references: ['https://example.com'],
            mitre_tactics: ['defense-evasion'],
            categories: [RuleCategory.MALWARE_DETECTED],
            modified: mockCurrentTime,
          }),
        );
      });

      it('should set modified to current date when empty metadata object is provided', async () => {
        // Arrange
        const existingRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'low',
            author: 'test-author',
          },
        };

        const updateRuleDto: UpdateRuleDto = {
          title: 'New Title',
          metadata: {}, // empty metadata object
        };

        const expectedUpdatedRule: StoredRule = {
          ...existingRule,
          title: 'New Title',
          updated_at: new Date(mockCurrentTime).getTime(),
          metadata: {
            ...existingRule.metadata,
            modified: mockCurrentTime,
          },
        };

        ruleRepository.findOne.mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(expectedUpdatedRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(ruleRepository.update).toHaveBeenCalledWith(
          { id: 'rule-id-1' },
          expect.objectContaining({
            title: 'New Title',
            metadata: expect.objectContaining({
              severity: 'low', // preserved
              author: 'test-author', // preserved
              modified: mockCurrentTime, // auto-set
            }),
          }),
        );

        expect(result.metadata).toEqual(
          expect.objectContaining({
            severity: 'low',
            author: 'test-author',
            modified: mockCurrentTime,
          }),
        );
      });

      it('should handle undefined values in updateRuleDto.metadata properly', async () => {
        // Arrange
        const existingRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'medium',
            author: 'original-author',
            platforms: [RulePlatform.WINDOWS],
          },
        };

        const updateRuleDto: UpdateRuleDto = {
          metadata: {
            severity: 'high',
            platforms: undefined, // explicitly setting to undefined
          },
        };

        const expectedUpdatedRule: StoredRule = {
          ...existingRule,
          updated_at: new Date(mockCurrentTime).getTime(),
          metadata: {
            ...existingRule.metadata,
            severity: 'high',
            platforms: undefined,
            modified: mockCurrentTime,
          },
        };

        ruleRepository.findOne.mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(expectedUpdatedRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(ruleRepository.update).toHaveBeenCalledWith(
          { id: 'rule-id-1' },
          expect.objectContaining({
            metadata: expect.objectContaining({
              severity: 'high',
              platforms: undefined,
              modified: mockCurrentTime,
            }),
          }),
        );

        expect(result.metadata).toEqual(
          expect.objectContaining({
            severity: 'high',
            platforms: undefined,
            modified: mockCurrentTime,
          }),
        );
      });
    });
  });

  describe('remove', () => {
    it('should remove a rule when it exists', async () => {
      // Arrange
      ruleRepository.delete.mockResolvedValue(mockStoredRule);

      // Act
      const result = await service.remove('rule-id-1');

      // Assert
      expect(result).toEqual(mockRule);
      expect(ruleRepository.delete).toHaveBeenCalledWith({ id: 'rule-id-1' });
    });

    it('should throw NotFoundException when rule is not found', async () => {
      // Arrange
      ruleRepository.delete.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove('non-existent-id')).rejects.toThrow(
        new NotFoundException('Rule with ID non-existent-id not found'),
      );
    });
  });

  describe('bulkDownload', () => {
    it('should generate a zip file and return a signed URL', async () => {
      // Arrange
      const ruleIds = ['rule-id-1', 'rule-id-2'];

      ruleRepository.batchGet.mockResolvedValue({
        successful: mockStoredRules,
        failed: [],
      });

      s3Service.uploadFile.mockResolvedValue({
        key: 'downloads/rules-mocked-uuid.zip',
        bucket: 'test-download-bucket',
      });

      s3Service.getSignedUrl.mockResolvedValue(
        'https://test-url.com/download.zip',
      );

      // Act
      const result = await service.bulkDownload(ruleIds);

      // Assert
      expect(uuidv4).toHaveBeenCalled();
      expect(ruleRepository.batchGet).toHaveBeenCalledWith(
        ruleIds.map((id) => ({ id })),
      );

      const expectedKey = 'downloads/rules-mocked-uuid.zip';
      expect(s3Service.uploadFile).toHaveBeenCalledWith(
        'test-download-bucket',
        expect.stringContaining('downloads/rules-'),
        expect.any(Buffer),
        'application/zip',
      );

      expect(s3Service.getSignedUrl).toHaveBeenCalledWith(
        'test-download-bucket',
        expect.stringContaining('downloads/rules-'),
        S3SignedUrlOperation.GET,
        3600,
      );

      expect(result).toEqual({
        downloadUrl: 'https://test-url.com/download.zip',
        expiresIn: 3600,
      });
    });

    it('should throw an error when no rule IDs are provided', async () => {
      // Act & Assert
      await expect(service.bulkDownload([])).rejects.toThrow(
        'No rule IDs provided for bulk download',
      );
      expect(ruleRepository.batchGet).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when no rules are found', async () => {
      // Arrange
      ruleRepository.batchGet.mockResolvedValue({
        successful: [],
        failed: [{ item: { id: 'rule-id-1' }, reason: 'Not found' }],
      });

      // Act & Assert
      await expect(service.bulkDownload(['rule-id-1'])).rejects.toThrow(
        new NotFoundException('No rules found with the provided IDs'),
      );
    });

    it('should handle partial failures in batch get', async () => {
      // Arrange
      ruleRepository.batchGet.mockResolvedValue({
        successful: [mockStoredRules[0]],
        failed: [{ item: { id: 'rule-id-2' }, reason: 'Not found' }],
      });

      const expectedKey = 'downloads/rules-mocked-uuid.zip';
      s3Service.uploadFile.mockResolvedValue({
        key: expectedKey,
        bucket: 'test-download-bucket',
      });

      s3Service.getSignedUrl.mockResolvedValue(
        'https://test-url.com/download.zip',
      );

      // Act
      const result = await service.bulkDownload(['rule-id-1', 'rule-id-2']);

      // Assert
      expect(s3Service.uploadFile).toHaveBeenCalledWith(
        'test-download-bucket',
        expectedKey,
        expect.any(Buffer),
        'application/zip',
      );

      expect(s3Service.getSignedUrl).toHaveBeenCalledWith(
        'test-download-bucket',
        expect.stringContaining('downloads/rules-'),
        S3SignedUrlOperation.GET,
        3600,
      );

      expect(result).toEqual({
        downloadUrl: 'https://test-url.com/download.zip',
        expiresIn: 3600,
      });
    });
  });

  describe('singleRuleDownload', () => {
    it('should return a download URL for a single rule', async () => {
      // Arrange
      const ruleId = 'rule-id-1';

      // Mock findOne to return a rule
      service.findOne = jest.fn().mockResolvedValue(mockRule);

      s3Service.uploadFile.mockResolvedValue({
        key: 'downloads/single/rule-id-1-Test-Rule.json',
        bucket: 'test-download-bucket',
      });

      s3Service.getSignedUrl.mockResolvedValue(
        'https://test-url.com/rule.json',
      );

      // Act
      const result = await service.singleRuleDownload(ruleId);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(ruleId);

      // Check S3 upload was called with the correct parameters
      expect(s3Service.uploadFile).toHaveBeenCalledWith(
        'test-download-bucket',
        expect.stringContaining('downloads/single/'),
        expect.any(Buffer),
        'application/json',
      );

      // Check getSignedUrl was called with the correct parameters
      expect(s3Service.getSignedUrl).toHaveBeenCalledWith(
        'test-download-bucket',
        expect.stringContaining('downloads/single/'),
        S3SignedUrlOperation.GET,
        3600,
      );

      expect(result).toEqual({
        downloadUrl: 'https://test-url.com/rule.json',
        expiresIn: 3600,
      });
    });

    it('should propagate errors when finding the rule fails', async () => {
      // Arrange
      const ruleId = 'non-existent-rule';

      service.findOne = jest
        .fn()
        .mockRejectedValue(
          new NotFoundException(`Rule with ID ${ruleId} not found`),
        );

      // Act & Assert
      await expect(service.singleRuleDownload(ruleId)).rejects.toThrow(
        new NotFoundException(`Rule with ID ${ruleId} not found`),
      );
      expect(service.findOne).toHaveBeenCalledWith(ruleId);
      expect(s3Service.uploadFile).not.toHaveBeenCalled();
      expect(s3Service.getSignedUrl).not.toHaveBeenCalled();
    });
  });

  describe('sanitizeFileName', () => {
    it('should sanitize file names correctly', () => {
      // Access private method
      const service_any = service as any;

      expect(service_any.sanitizeFileName('test file')).toBe('test_file');
      expect(service_any.sanitizeFileName('file/with/slashes')).toBe(
        'file_with_slashes',
      );
      expect(service_any.sanitizeFileName('file<>:"|?*name')).toBe(
        'file_______name',
      );
      expect(service_any.sanitizeFileName('file_name_with_spaces')).toBe(
        'file_name_with_spaces',
      );
      expect(service_any.sanitizeFileName('file$%@#name')).toBe('file____name');
      expect(service_any.sanitizeFileName('a'.repeat(200))).toBe(
        'a'.repeat(100),
      );
    });
  });

  describe('createRulesZip', () => {
    it('should create a zip file containing rules and metadata', async () => {
      // We'll test the private method by accessing it using the any type cast
      const service_any = service as any;

      // Act
      const zipBuffer = await service_any.createRulesZip(mockRules);

      // Assert
      expect(zipBuffer).toBeInstanceOf(Buffer);

      // Verify zip contents
      const zip = new AdmZip(zipBuffer);
      const zipEntries = zip.getEntries();

      // Should have 3 entries: 2 rules and 1 metadata file
      expect(zipEntries.length).toBe(3);

      // Verify rule files
      const entryNames = zipEntries.map((entry) => entry.name);
      expect(entryNames).toContain('metadata.json');
      expect(entryNames).toContain('rule-id-1-Test_Rule.yml');
      expect(entryNames).toContain('rule-id-2-Another_Test_Rule.txt');

      // Verify metadata file exists
      const metadataEntry = zipEntries.find(
        (entry) => entry.name === 'metadata.json',
      );
      expect(metadataEntry).toBeDefined();

      if (metadataEntry) {
        // Check content of metadata file
        const metadataContent = JSON.parse(zip.readAsText(metadataEntry.name));
        expect(metadataContent.totalRules).toBe(2);
        expect(metadataContent.ruleIds).toContain('rule-id-1');
        expect(metadataContent.ruleIds).toContain('rule-id-2');
      }

      // Check content of first rule file
      const firstRuleEntry = zipEntries.find((entry) =>
        entry.name.includes('rule-id-1'),
      );
      expect(firstRuleEntry).toBeDefined();

      if (firstRuleEntry) {
        const firstRuleContent = JSON.parse(
          zip.readAsText(firstRuleEntry.name),
        );
        expect(firstRuleContent.id).toBe('rule-id-1');
        // Corrected assertion for the 'createRulesZip' test
        expect(firstRuleContent.title).toEqual({ value: 'Test Rule' });
      }
    });

    it('should handle special characters in file names', async () => {
      // We'll test the private method by accessing it using the any type cast
      const service_any = service as any;

      // Create a rule with special characters in its title
      const specialRule: Rule = {
        ...mockRule,
        title: { value: 'Special @#$% Characters & Spaces' },
      };

      // Act
      const zipBuffer = await service_any.createRulesZip([specialRule]);

      // Assert
      const zip = new AdmZip(zipBuffer);
      const zipEntries = zip.getEntries();

      // Check that the filename was sanitized
      const ruleEntry = zipEntries.find((entry) =>
        entry.name.includes('rule-id-1'),
      );
      expect(ruleEntry).toBeDefined();

      if (ruleEntry) {
        expect(ruleEntry.name).not.toContain('@#$%');
        expect(ruleEntry.name).not.toContain('&');
        expect(ruleEntry.name).not.toContain(' ');
      }
    });
  });

  describe('findFollowedRules', () => {
    it('should return rules from followed users and groups', async () => {
      // Arrange
      const userIds = ['user-id-1', 'user-id-2'];
      const groupIds = ['group-id-1'];
      const ownerIdFilter = ['owner-id-1'];
      const followedRules = {
        items: [mockStoredRule],
        total: 1,
      };

      ruleRepository.findFollowedRules = jest
        .fn()
        .mockResolvedValue(followedRules);

      // Act
      const result = await service.findFollowedRules(
        userIds,
        groupIds,
        ownerIdFilter,
      );

      // Assert
      expect(ruleRepository.findFollowedRules).toHaveBeenCalledWith(
        groupIds,
        userIds,
        ownerIdFilter,
        1,
        100,
      );
      expect(result).toEqual({
        items: [mockRule],
        total: 1,
      });
    });

    it('should handle pagination parameters', async () => {
      // Arrange
      const userIds = ['user-id-1'];
      const groupIds = ['group-id-1'];
      const ownerIdFilter = ['owner-id-1'];
      const page = 2;
      const size = 20;
      const followedRules = {
        items: [mockStoredRule],
        total: 25,
      };

      ruleRepository.findFollowedRules = jest
        .fn()
        .mockResolvedValue(followedRules);

      // Act
      const result = await service.findFollowedRules(
        userIds,
        groupIds,
        ownerIdFilter,
        page,
        size,
      );

      // Assert
      expect(ruleRepository.findFollowedRules).toHaveBeenCalledWith(
        groupIds,
        userIds,
        ownerIdFilter,
        2,
        20,
      );
      expect(result.total).toBe(25);
    });

    it('should handle repository errors', async () => {
      // Arrange
      const userIds = ['user-id-1'];
      const groupIds = ['group-id-1'];
      const ownerIdFilter = ['owner-id-1'];
      const error = new Error('Repository error');

      ruleRepository.findFollowedRules = jest.fn().mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.findFollowedRules(userIds, groupIds, ownerIdFilter),
      ).rejects.toThrow(error);
    });
  });

  describe('updateCounts', () => {
    it('should increment a counter for a rule', async () => {
      // Arrange
      const ruleId = 'rule-id-1';
      const type = RuleInteractionType.LIKE;
      const change = CounterChange.INCREMENT;

      const updatedRule = {
        ...mockStoredRule,
        likes: 1,
      };

      ruleRepository.incrementCounter.mockResolvedValue(updatedRule);

      // Act
      await service.updateCounts(ruleId, type, change);

      // Assert
      expect(ruleRepository.incrementCounter).toHaveBeenCalledWith(
        ruleId,
        'likes',
      );
      expect(ruleRepository.decrementCounter).not.toHaveBeenCalled();
    });

    it('should decrement a counter for a rule', async () => {
      // Arrange
      const ruleId = 'rule-id-1';
      const type = RuleInteractionType.DOWNLOAD;
      const change = CounterChange.DECREMENT;

      const updatedRule = {
        ...mockStoredRule,
        downloads: 0,
      };

      ruleRepository.decrementCounter.mockResolvedValue(updatedRule);

      // Act
      await service.updateCounts(ruleId, type, change);

      // Assert
      expect(ruleRepository.decrementCounter).toHaveBeenCalledWith(
        ruleId,
        'downloads',
      );
      expect(ruleRepository.incrementCounter).not.toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      // Arrange
      const ruleId = 'rule-id-1';
      const type = RuleInteractionType.DISLIKE;
      const change = CounterChange.INCREMENT;
      const error = new Error('Repository error');

      ruleRepository.incrementCounter.mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateCounts(ruleId, type, change)).rejects.toThrow(
        error,
      );
    });

    it('should invalidate cache when updating rule interaction counts', async () => {
      // Reset mocks to ensure we have clean state
      jest.clearAllMocks();

      const ruleId = 'rule-id-1';
      const updatedRule = {
        ...mockStoredRule,
        owner_id: 'group-id-1', // Group owned rule
        created_by: 'user-id-1',
        likes: 10,
      };

      ruleRepository.incrementCounter.mockResolvedValue(updatedRule);

      await service.updateCounts(
        ruleId,
        RuleInteractionType.LIKE,
        CounterChange.INCREMENT,
      );
    });

    it('should handle interaction types that do not affect rule counts', async () => {
      const ruleId = 'rule123';
      const type = 'INVALID_TYPE' as RuleInteractionType; // Invalid type that has no column mapping
      const change = CounterChange.INCREMENT;

      // Mock getColumnToUpdate to return null for this invalid type
      jest.spyOn(service as any, 'getColumnToUpdate').mockReturnValue(null);

      // Act & Assert - should complete successfully without throwing
      await expect(
        service.updateCounts(ruleId, type, change),
      ).resolves.toBeUndefined();

      // Verify that repository methods are not called when there's no column to update
      expect(ruleRepository.incrementCounter).not.toHaveBeenCalled();
      expect(ruleRepository.decrementCounter).not.toHaveBeenCalled();
    });
  });

  describe('findByIds', () => {
    it('should return rules by IDs in the same order', async () => {
      // Arrange
      const ruleIds = ['rule-id-2', 'rule-id-1']; // Note the order
      const storedRules = [mockStoredRules[1], mockStoredRules[0]]; // Same order as ruleIds

      ruleRepository.findByIds.mockResolvedValue(storedRules);

      // Act
      const result = await service.findByIds(ruleIds);

      // Assert
      expect(ruleRepository.findByIds).toHaveBeenCalledWith(ruleIds);
      expect(result.length).toBe(2);
      expect(result[0].id).toBe('rule-id-2'); // Check order is preserved
      expect(result[1].id).toBe('rule-id-1');
    });

    it('should return empty array when no IDs are provided', async () => {
      // Act
      const result = await service.findByIds([]);

      // Assert
      expect(ruleRepository.findByIds).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it('should handle repository errors', async () => {
      // Arrange
      const error = new Error('Repository error');
      ruleRepository.findByIds.mockRejectedValue(error);

      // Act & Assert
      await expect(service.findByIds(['rule-id-1'])).rejects.toThrow(error);
      expect(ruleRepository.findByIds).toHaveBeenCalledWith(['rule-id-1']);
    });

    it('should map stored rules to DTOs', async () => {
      // Arrange
      const ruleIds = ['rule-id-1'];
      const storedRules = [
        {
          ...mockStoredRules[0],
          status: RuleStatus.PUBLISHED,
          owner_id: 'group-id-1',
          published_at: 1672531200000, // 2023-01-01T00:00:00Z
        },
      ];

      ruleRepository.findByIds.mockResolvedValue(storedRules);

      // Act
      const result = await service.findByIds(ruleIds);

      // Assert
      expect(result.length).toBe(1);
      expect(result[0]).toEqual({
        ...mockRules[0],
        status: RuleStatus.PUBLISHED,
        group_id: 'group-id-1',
        published_at: '2023-01-01T00:00:00Z',
      });
    });
  });

  describe('resetGroupRulesToDraft', () => {
    it('should reset all published rules in a group to draft status', async () => {
      // Arrange
      const groupId = 'group-id-1';
      const publishedRules: StoredRule[] = [
        {
          ...mockStoredRule,
          id: 'published-rule-1',
          owner_id: groupId, // The rule is owned by the group (published)
          status: RuleStatus.PUBLISHED,
          created_by: 'user-id-1', // Original creator
          group_name: 'Test Group',
        },
        {
          ...mockStoredRule,
          id: 'published-rule-2',
          owner_id: groupId, // The rule is owned by the group (published)
          status: RuleStatus.PUBLISHED,
          created_by: 'user-id-2', // Different creator
          group_name: 'Test Group',
        },
      ];

      ruleRepository.findRulesByGroupId.mockResolvedValue(publishedRules);
      ruleRepository.update.mockImplementation((key, entity) =>
        Promise.resolve(entity as StoredRule),
      );

      // Act
      const result = await service.resetGroupRulesToDraft(groupId);

      // Assert
      expect(result).toBe(2); // Two rules should be updated successfully
      expect(ruleRepository.findRulesByGroupId).toHaveBeenCalledWith(groupId);

      // Verify update was called for each rule with correct parameters
      expect(ruleRepository.update).toHaveBeenCalledTimes(2);

      // Check first rule update
      expect(ruleRepository.update).toHaveBeenCalledWith(
        { id: 'published-rule-1' },
        expect.objectContaining({
          status: RuleStatus.DRAFT,
          owner_id: 'user-id-1', // Should be set to the original creator
          group_name: null, // Group name should be removed
        }),
      );

      // Check second rule update
      expect(ruleRepository.update).toHaveBeenCalledWith(
        { id: 'published-rule-2' },
        expect.objectContaining({
          status: RuleStatus.DRAFT,
          owner_id: 'user-id-2', // Should be set to the original creator
          group_name: null, // Group name should be removed
        }),
      );
    });

    it('should return 0 if no rules are found for the group', async () => {
      // Arrange
      const groupId = 'non-existent-group';
      ruleRepository.findRulesByGroupId.mockResolvedValue([]);

      // Act
      const result = await service.resetGroupRulesToDraft(groupId);

      // Assert
      expect(result).toBe(0);
      expect(ruleRepository.findRulesByGroupId).toHaveBeenCalledWith(groupId);
      expect(ruleRepository.update).not.toHaveBeenCalled();
    });

    it('should handle errors from the repository', async () => {
      // Arrange
      const groupId = 'group-id-1';
      const error = new Error('Database error');
      ruleRepository.findRulesByGroupId.mockRejectedValue(error);

      // Act & Assert
      await expect(service.resetGroupRulesToDraft(groupId)).rejects.toThrow(
        error,
      );
      expect(ruleRepository.findRulesByGroupId).toHaveBeenCalledWith(groupId);
    });

    it('should count only successful updates', async () => {
      // Arrange
      const groupId = 'group-id-1';
      const publishedRules: StoredRule[] = [
        {
          ...mockStoredRule,
          id: 'published-rule-1',
          owner_id: groupId,
          status: RuleStatus.PUBLISHED,
          created_by: 'user-id-1',
          group_name: 'Test Group',
        },
        {
          ...mockStoredRule,
          id: 'published-rule-2',
          owner_id: groupId,
          status: RuleStatus.PUBLISHED,
          created_by: 'user-id-2',
          group_name: 'Test Group',
        },
      ];

      ruleRepository.findRulesByGroupId.mockResolvedValue(publishedRules);

      // Mock first update to succeed, second to fail
      ruleRepository.update
        .mockImplementationOnce((key, entity) =>
          Promise.resolve(entity as StoredRule),
        )
        .mockImplementationOnce(() =>
          Promise.reject(new Error('Update failed')),
        );

      // Act
      const result = await service.resetGroupRulesToDraft(groupId);

      // Assert
      expect(result).toBe(1); // Only one update should succeed
      expect(ruleRepository.update).toHaveBeenCalledTimes(2);
    });
  });

  describe('resetUserPublishedRulesToDraft', () => {
    it('should reset all published rules created by a user but owned by groups to draft status', async () => {
      // Arrange
      const userId = 'test-user';
      const publishedRules: StoredRule[] = [
        {
          ...mockStoredRule,
          id: 'published-rule-1',
          created_by: userId, // Created by the test user
          owner_id: 'group1', // But owned by a group (published)
          status: RuleStatus.PUBLISHED,
          group_name: 'Test Group',
        },
        {
          ...mockStoredRule,
          id: 'published-rule-2',
          created_by: userId, // Created by the test user
          owner_id: 'group2', // But owned by another group (published)
          status: RuleStatus.PUBLISHED,
          group_name: 'Another Group',
        },
      ];

      // Mock the findAllRulesCreatedByUser to return the published rules
      ruleRepository.findAllRulesCreatedByUser.mockResolvedValue(
        publishedRules,
      );

      // Mock the private resetRulesToDraft method to return the count of updated rules
      const resetRulesToDraftSpy = jest
        .spyOn(service as any, 'resetRulesToDraft')
        .mockResolvedValue(2);

      // Act
      const result = await service.resetUserPublishedRulesToDraft(userId);

      // Assert
      expect(ruleRepository.findAllRulesCreatedByUser).toHaveBeenCalledWith(
        userId,
      );
      expect(resetRulesToDraftSpy).toHaveBeenCalledWith(publishedRules);
      expect(result).toBe(2);
    });

    it('should return 0 if no rules are found for the user', async () => {
      // Arrange
      const userId = 'user-with-no-rules';
      ruleRepository.findAllRulesCreatedByUser.mockResolvedValue([]);

      // Act
      const result = await service.resetUserPublishedRulesToDraft(userId);

      // Assert
      expect(result).toBe(0);
      expect(ruleRepository.findAllRulesCreatedByUser).toHaveBeenCalledWith(
        userId,
      );
    });

    it('should propagate errors from the repository', async () => {
      // Arrange
      const userId = 'test-user';
      const error = new Error('Repository error');
      ruleRepository.findAllRulesCreatedByUser.mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.resetUserPublishedRulesToDraft(userId),
      ).rejects.toThrow(error);
      expect(ruleRepository.findAllRulesCreatedByUser).toHaveBeenCalledWith(
        userId,
      );
    });
  });

  describe('resetRulesToDraft', () => {
    it('should update rules to draft status', async () => {
      // Arrange
      const rules: StoredRule[] = [
        {
          ...mockStoredRule,
          id: 'rule-1',
          created_by: 'user-1',
          owner_id: 'group-1',
          status: RuleStatus.PUBLISHED,
          group_name: 'Group 1',
        },
        {
          ...mockStoredRule,
          id: 'rule-2',
          created_by: 'user-2',
          owner_id: 'group-1',
          status: RuleStatus.PUBLISHED,
          group_name: 'Group 1',
        },
      ];

      // Mock the repository update method
      ruleRepository.update.mockImplementation((key, entity) =>
        Promise.resolve(entity as StoredRule),
      );

      // Act
      const result = await (service as any).resetRulesToDraft(rules);

      // Assert
      expect(result).toBe(2); // Two rules successfully updated
      expect(ruleRepository.update).toHaveBeenCalledTimes(2);

      // Check updates for each rule
      expect(ruleRepository.update).toHaveBeenCalledWith(
        { id: 'rule-1' },
        expect.objectContaining({
          status: RuleStatus.DRAFT,
          owner_id: 'user-1', // Should be set to the original creator
          group_name: null, // Group name should be removed
        }),
      );

      expect(ruleRepository.update).toHaveBeenCalledWith(
        { id: 'rule-2' },
        expect.objectContaining({
          status: RuleStatus.DRAFT,
          owner_id: 'user-2', // Should be set to the original creator
          group_name: null, // Group name should be removed
        }),
      );
    });

    it('should only update rules that are not already in draft status', async () => {
      // Arrange
      const rules: StoredRule[] = [
        {
          ...mockStoredRule,
          id: 'rule-1',
          created_by: 'user-1',
          owner_id: 'group-1',
          status: RuleStatus.PUBLISHED,
          group_name: 'Group 1',
        },
        {
          ...mockStoredRule,
          id: 'rule-2',
          created_by: 'user-2',
          owner_id: 'user-2',
          status: RuleStatus.DRAFT, // Already draft
          group_name: null,
        },
      ];

      // Mock the repository update method
      ruleRepository.update.mockImplementation((key, entity) =>
        Promise.resolve(entity as StoredRule),
      );

      // Act
      const result = await (service as any).resetRulesToDraft(rules);

      // Assert
      expect(ruleRepository.update).toHaveBeenCalledTimes(1); // Only one rule should be updated
      expect(ruleRepository.update).toHaveBeenCalledWith(
        { id: 'rule-1' },
        expect.objectContaining({
          status: RuleStatus.DRAFT,
          owner_id: 'user-1',
          group_name: null,
        }),
      );
      expect(result).toBe(1); // Only one rule updated
    });

    it('should return 0 if no rules are provided', async () => {
      // Act
      const result = await (service as any).resetRulesToDraft([]);

      // Assert
      expect(result).toBe(0);
      expect(ruleRepository.update).not.toHaveBeenCalled();
    });

    it('should count only successful updates', async () => {
      // Arrange
      const rules: StoredRule[] = [
        {
          ...mockStoredRule,
          id: 'rule-1',
          created_by: 'user-1',
          owner_id: 'group-1',
          status: RuleStatus.PUBLISHED,
        },
        {
          ...mockStoredRule,
          id: 'rule-2',
          created_by: 'user-2',
          owner_id: 'group-1',
          status: RuleStatus.PUBLISHED,
        },
      ];

      // Mock first update to succeed, second to fail with type-safe mock implementations
      ruleRepository.update
        .mockImplementationOnce((key, entity) => {
          return Promise.resolve(entity as unknown as StoredRule);
        })
        .mockImplementationOnce(() =>
          Promise.reject(new Error('Update failed')),
        );

      // Act
      const result = await (service as any).resetRulesToDraft(rules);

      // Assert
      expect(result).toBe(1); // Only one update should succeed
      expect(ruleRepository.update).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateMyRulesUsername', () => {
    it('should call repository method with correct parameters', async () => {
      // Arrange
      const userId = 'test-user';
      const username = 'new-username';
      const ruleIds = ['rule1', 'rule2', 'rule3'];

      // Mock the repository method
      ruleRepository.updateMyRulesUsername.mockResolvedValue(undefined);

      // Act
      await service.updateMyRulesUsername(userId, username, ruleIds);

      // Assert
      expect(ruleRepository.updateMyRulesUsername).toHaveBeenCalledWith(
        userId,
        username,
        ruleIds,
      );
    });

    it('should propagate errors from the repository', async () => {
      // Arrange
      const userId = 'test-user';
      const username = 'new-username';
      const ruleIds = ['rule1', 'rule2'];
      const error = new Error('Repository error');

      // Mock the repository method to throw an error
      ruleRepository.updateMyRulesUsername.mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.updateMyRulesUsername(userId, username, ruleIds),
      ).rejects.toThrow(error);
    });
  });

  describe('getCountsByUser', () => {
    it('should return counts for specified user IDs (no groupId)', async () => {
      const userIds = ['user1', 'user2'];
      const mockResponse = {
        body: {
          datarows: [
            ['user1', 10, 5, 20, 2], // rule_count, like_count, download_count, dislike_count
            ['user2', 15, 8, 25, 3],
          ],
        },
      };
      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockResolvedValue(mockResponse),
        },
      });

      const result = await service.getCountsByUser(userIds); // Implicitly groupId is undefined

      expect(mockOpenSearchConfigService.getIndexName).toHaveBeenCalledWith(
        'detection_rules',
      );

      const normalizeQuery = (query: string) =>
        query
          .split('\n')
          .map((line) => line.trim())
          .filter((line) => line.length > 0)
          .join('\n');

      const expectedRawQuery = `
              SELECT created_by, count(id) as rule_count, sum(likes) as like_count,
                      sum(downloads) as download_count, sum(dislikes) as dislike_count
              FROM mocked_index_name 
              WHERE status <> "DRAFT"
              AND created_by IN (${userIds.map((id) => `'${id}'`).join(',')})
              
              GROUP BY created_by;
            `; // No groupId filter

      const requestCall = (
        mockOpenSearchService.getClient().transport.request as jest.Mock
      ).mock.calls[0][0];

      expect(requestCall.method).toBe('POST');
      expect(requestCall.path).toBe('_plugins/_sql');
      expect(normalizeQuery(requestCall.body.query)).toEqual(
        normalizeQuery(expectedRawQuery),
      );

      expect(result).toEqual({
        user1: {
          id: 'user1',
          detections_count: 10,
          likes_count: 5,
          downloads_count: 20,
          dislikes_count: 2,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
        user2: {
          id: 'user2',
          detections_count: 15,
          likes_count: 8,
          downloads_count: 25,
          dislikes_count: 3,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      });
    });

    it('should return counts for specified user IDs filtered by groupId when groupId is provided', async () => {
      const userIds = ['user1', 'user2'];
      const groupId = 'groupA';
      const mockResponse = {
        body: {
          datarows: [
            ['user1', 7, 3, 10, 1], // user1's counts for groupA
          ],
        },
      };
      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockResolvedValue(mockResponse),
        },
      });

      const result = await service.getCountsByUser(userIds, groupId);

      expect(mockOpenSearchConfigService.getIndexName).toHaveBeenCalledWith(
        'detection_rules',
      );

      const normalizeQuery = (query: string) =>
        query
          .split('\n')
          .map((s) => s.trim())
          .filter((s) => s)
          .join('\n');

      const expectedQuery = `
        SELECT created_by, count(id) as rule_count, sum(likes) as like_count,
               sum(downloads) as download_count, sum(dislikes) as dislike_count
        FROM mocked_index_name
        WHERE status <> "DRAFT"
        AND created_by IN ('user1','user2')
        AND owner_id = 'groupA'
        GROUP BY created_by;
      `;

      const requestCallArgs = (
        mockOpenSearchService.getClient().transport.request as jest.Mock
      ).mock.calls[0][0];
      expect(normalizeQuery(requestCallArgs.body.query)).toEqual(
        normalizeQuery(expectedQuery),
      );

      expect(result).toEqual({
        user1: {
          id: 'user1',
          detections_count: 7,
          likes_count: 3,
          downloads_count: 10,
          dislikes_count: 1,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
        user2: {
          // user2 had no rules in groupA, so default counts
          id: 'user2',
          detections_count: 0,
          likes_count: 0,
          downloads_count: 0,
          dislikes_count: 0,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      });
    });

    it('should return counts filtered by groupId when only groupId is provided (empty userIds)', async () => {
      const userIds: string[] = [];
      const groupId = 'groupB';
      const mockResponse = {
        body: {
          datarows: [
            // Assuming the query groups by created_by, it would return counts for users within that group
            ['userX', 5, 2, 8, 0], // userX's counts for groupB
            ['userY', 3, 1, 4, 1], // userY's counts for groupB
          ],
        },
      };
      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockResolvedValue(mockResponse),
        },
      });

      const result = await service.getCountsByUser(userIds, groupId);

      const normalizeQuery = (query: string) =>
        query
          .split('\n')
          .map((s) => s.trim())
          .filter((s) => s)
          .join('\n');

      const expectedQuery = `
        SELECT created_by, count(id) as rule_count, sum(likes) as like_count,
               sum(downloads) as download_count, sum(dislikes) as dislike_count
        FROM mocked_index_name
        WHERE status <> "DRAFT"
        
        AND owner_id = 'groupB'
        GROUP BY created_by;
      `; // No created_by IN (...) clause

      const requestCallArgs = (
        mockOpenSearchService.getClient().transport.request as jest.Mock
      ).mock.calls[0][0];
      expect(normalizeQuery(requestCallArgs.body.query)).toEqual(
        normalizeQuery(expectedQuery),
      );

      // Since userIds is empty, the result map is initialized as empty and then populated by OpenSearch results.
      expect(result).toEqual({
        userX: {
          id: 'userX',
          detections_count: 5,
          likes_count: 2,
          downloads_count: 8,
          dislikes_count: 0,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
        userY: {
          id: 'userY',
          detections_count: 3,
          likes_count: 1,
          downloads_count: 4,
          dislikes_count: 1,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      });
    });

    it('should return default counts for user IDs not in OpenSearch response (no groupId)', async () => {
      const userIds = ['user1', 'user3']; // user3 is not in mockResponse
      const mockResponse = {
        body: {
          datarows: [
            ['user1', 10, 5, 20, 2],
            // user2 is intentionally omitted here to test default counts for user3 later, if user2 was in userIds
          ],
        },
      };
      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockResolvedValue(mockResponse),
        },
      });

      const result = await service.getCountsByUser(userIds); // No groupId

      const normalizeQuery = (query: string) =>
        query
          .split('\n')
          .map((s) => s.trim())
          .filter((s) => s)
          .join('\n');

      const expectedQuery = `
        SELECT created_by, count(id) as rule_count, sum(likes) as like_count,
               sum(downloads) as download_count, sum(dislikes) as dislike_count
        FROM mocked_index_name
        WHERE status <> "DRAFT"
        AND created_by IN (${userIds.map((id) => `'${id}'`).join(',')})
        GROUP BY created_by;
      `;

      const requestCallArgs = (
        mockOpenSearchService.getClient().transport.request as jest.Mock
      ).mock.calls[0][0];
      expect(normalizeQuery(requestCallArgs.body.query)).toEqual(
        normalizeQuery(expectedQuery),
      );

      expect(result).toEqual({
        user1: {
          id: 'user1',
          detections_count: 10,
          likes_count: 5,
          downloads_count: 20,
          dislikes_count: 2,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
        user3: {
          // Default values for user3 as it's in userIds but not in OpenSearch response
          id: 'user3',
          detections_count: 0,
          likes_count: 0,
          downloads_count: 0,
          dislikes_count: 0,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      });
    });

    it('should handle empty userIds array (no groupId)', async () => {
      const userIds: string[] = [];
      const mockResponse = {
        body: {
          datarows: [
            // Assuming the query groups by created_by, it would return counts for users within that group
            ['userA', 5, 2, 10, 1], // userA's counts for groupB
            ['userB', 8, 3, 12, 0], // userB's counts for groupB
          ],
        },
      };
      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockResolvedValue(mockResponse),
        },
      });

      const result = await service.getCountsByUser(userIds); // No groupId

      const normalizeQuery = (query: string) =>
        query
          .split('\n')
          .map((s) => s.trim())
          .filter((s) => s)
          .join('\n');

      const expectedQuery = `
        SELECT created_by, count(id) as rule_count, sum(likes) as like_count,
               sum(downloads) as download_count, sum(dislikes) as dislike_count
        FROM mocked_index_name
        WHERE status <> "DRAFT"
        
        GROUP BY created_by;
      `; // No created_by IN clause when userIds is empty

      const requestCallArgs = (
        mockOpenSearchService.getClient().transport.request as jest.Mock
      ).mock.calls[0][0];
      expect(normalizeQuery(requestCallArgs.body.query)).toEqual(
        normalizeQuery(expectedQuery),
      );

      expect(result).toEqual({
        userA: {
          id: 'userA',
          detections_count: 5,
          likes_count: 2,
          downloads_count: 10,
          dislikes_count: 1,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
        userB: {
          id: 'userB',
          detections_count: 8,
          likes_count: 3,
          downloads_count: 12,
          dislikes_count: 0,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      });
    });

    it('should throw InternalServerErrorException on OpenSearch error', async () => {
      const userIds = ['user1'];
      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockRejectedValue(new Error('OpenSearch error')),
        },
      });

      await expect(service.getCountsByUser(userIds)).rejects.toThrow(
        'Failed to get rule counts by user',
      );
    });
  });

  describe('getCountsByGroup', () => {
    it('should return counts for specified groups', async () => {
      // Arrange
      const groupIds = ['group-id-1', 'group-id-2'];
      const mockResponse = {
        body: {
          datarows: [
            ['group-id-1', 5, 10, 20, 2], // rule_count, like_count, download_count, dislike_count
            ['group-id-2', 3, 8, 15, 1],
          ],
        },
      };
      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockResolvedValue(mockResponse),
        },
      });

      // Act
      const result = await service.getCountsByGroup(groupIds);

      // Assert
      expect(result).toEqual({
        'group-id-1': {
          id: 'group-id-1',
          detections_count: 5,
          likes_count: 10,
          downloads_count: 20,
          dislikes_count: 2,
        },
        'group-id-2': {
          id: 'group-id-2',
          detections_count: 3,
          likes_count: 8,
          downloads_count: 15,
          dislikes_count: 1,
        },
      });
    });

    it('should return zero counts for groups with no data', async () => {
      // Arrange
      const groupIds = ['group-id-1', 'group-id-2'];
      const mockResponse = {
        body: {
          datarows: [
            ['group-id-1', 0, 0, 0, 0], // rule_count, like_count, download_count, dislike_count
            ['group-id-2', 0, 0, 0, 0],
          ],
        },
      };

      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockResolvedValue(mockResponse),
        },
      });

      // Act
      const result = await service.getCountsByGroup(groupIds);

      // Assert
      expect(result).toEqual({
        'group-id-1': {
          id: 'group-id-1',
          detections_count: 0,
          likes_count: 0,
          downloads_count: 0,
          dislikes_count: 0,
        },
        'group-id-2': {
          id: 'group-id-2',
          detections_count: 0,
          likes_count: 0,
          downloads_count: 0,
          dislikes_count: 0,
        },
      });
    });

    it('should handle OpenSearch errors', async () => {
      // Arrange
      const groupIds = ['group-id-1'];
      const mockError = new Error('OpenSearch error');

      (mockOpenSearchService.getClient as jest.Mock).mockReturnValue({
        transport: {
          request: jest.fn().mockRejectedValue(new Error('OpenSearch error')),
        },
      });

      await expect(service.getCountsByUser(groupIds)).rejects.toThrow(
        'Failed to get rule counts by user',
      );
    });
  });

  describe('MITRE Attack Metadata Cleaning', () => {
    describe('cleanMitreAttackMetadata function', () => {
      it('should clean MITRE attack objects by keeping only allowed fields', () => {
        const inputMetadata = {
          ...baseMetadataWithMitre,
          mitre_attack: [
            {
              ...baseMitreObject,
              // Extra fields that should be removed
              description: 'Should be removed',
              url: 'https://example.com',
              platforms: ['windows'],
              extra_field: 'remove me',
            } as any,
            {
              id: 'attack-pattern--456',
              mitre_id: 'T5678',
              name: 'Another Technique',
              type: MitreAttackObjectType.SUBTECHNIQUE,
              // Extra fields that should be removed
              external_references: [{ source_name: 'mitre-attack' }],
              x_mitre_version: '1.0',
            } as any,
          ],
        };

        const result = cleanMitreAttackMetadata(inputMetadata);

        expect(result).toEqual({
          ...baseMetadataWithMitre,
          mitre_attack: [
            baseMitreObject,
            {
              id: 'attack-pattern--456',
              mitre_id: 'T5678',
              name: 'Another Technique',
              type: MitreAttackObjectType.SUBTECHNIQUE,
            },
          ],
        });
      });

      it('should handle metadata without mitre_attack field', () => {
        const result = cleanMitreAttackMetadata(baseMetadataWithoutMitre);
        expect(result).toEqual(baseMetadataWithoutMitre);
      });

      it('should handle undefined metadata', () => {
        const result = cleanMitreAttackMetadata(undefined);
        expect(result).toBeUndefined();
      });

      it('should handle empty mitre_attack array', () => {
        const inputMetadata = {
          ...baseMetadataWithoutMitre,
          mitre_attack: [],
        };

        const result = cleanMitreAttackMetadata(inputMetadata);
        expect(result).toEqual(inputMetadata);
      });

      it('should handle mitre_attack objects with only allowed fields', () => {
        const result = cleanMitreAttackMetadata(baseMetadataWithMitre);
        expect(result).toEqual(baseMetadataWithMitre);
      });

      it('should handle null mitre_attack objects', () => {
        const inputMetadata = {
          mitre_attack: [null, baseMitreObject, undefined] as (
            | MitreAttackObject
            | null
            | undefined
          )[],
        };

        const result = cleanMitreAttackMetadata(inputMetadata as RuleMetadata);

        expect(result).toEqual({
          mitre_attack: [{}, baseMitreObject, {}],
        });
      });

      it('should handle objects with undefined values for allowed fields', () => {
        const inputMetadata = {
          mitre_attack: [
            {
              id: 'attack-pattern--123',
              mitre_id: undefined,
              name: 'Test Technique',
              parent_name: undefined,
              type: MitreAttackObjectType.TECHNIQUE,
              // Extra field that should be removed
              description: 'Should be removed',
            } as any,
          ],
        };

        const result = cleanMitreAttackMetadata(inputMetadata as RuleMetadata);

        expect(result).toEqual({
          mitre_attack: [
            {
              id: 'attack-pattern--123',
              name: 'Test Technique',
              type: MitreAttackObjectType.TECHNIQUE,
            },
          ],
        });
      });
    });

    describe('create operation MITRE cleaning', () => {
      it('should clean MITRE attack metadata when creating a rule', async () => {
        const createRuleDto: CreateRuleDto = {
          title: 'Test Rule with MITRE',
          description: 'A test rule with MITRE attack metadata',
          rule_type: RuleType.SIGMA,
          content: 'rule content',
          metadata: {
            ...baseMetadataWithMitre,
            mitre_attack: [
              {
                ...baseMitreObject,
                // Extra fields that should be removed
                description: 'Should be removed',
                url: 'https://example.com',
                platforms: ['windows'],
                extra_field: 'remove me',
              } as any,
            ],
          },
        };

        const expectedStoredRule: StoredRule = {
          id: 'mocked-uuid',
          title: 'Test Rule with MITRE',
          description: 'A test rule with MITRE attack metadata',
          ai_generated: {
            description: false,
            title: false,
            content: false,
            tags: false,
          },
          rule_type: RuleType.SIGMA,
          status: RuleStatus.DRAFT,
          content: 'rule content',
          tags: [],
          metadata: {
            author: undefined,
            data_sources: undefined,
            date: '2023-01-01T00:00:00Z',
            false_positives: undefined,
            mitre_attack: [baseMitreObject],
            mitre_tactics: undefined,
            mitre_techniques: undefined,
            modified: '2023-01-01T00:00:00Z',
            platforms: undefined,
            references: undefined,
            severity: 'high',
            tags: [],
          },
          test_cases: [],
          created_by: 'author-id-1',
          owner_id: 'author-id-1',
          group_name: null,
          created_at: new Date('2023-01-01T00:00:00Z').getTime(),
          updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
          published_at: undefined,
          version: 1,
          contributor: 'author-username-1',
          likes: 0,
          downloads: 0,
          dislikes: 0,
          bookmarks: 0,
        };

        const expectedReturnedRule: Rule = {
          ...createRuleDto,
          title: createRuleDto.title
            ? { value: createRuleDto.title }
            : undefined,
          id: 'mocked-uuid',
          created_by: 'author-id-1',
          contributor: 'author-username-1',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          published_at: undefined,
          version: 1,
          status: RuleStatus.DRAFT,
          tags: [],
          metadata: {
            ...baseMetadataWithMitre,
            date: '2023-01-01T00:00:00Z',
            modified: '2023-01-01T00:00:00Z',
            tags: [],
          },
          test_cases: [],
          likes: 0,
          downloads: 0,
          dislikes: 0,
          bookmarks: 0,
          ai_generated: {
            description: false,
            title: false,
            content: false,
            tags: false,
          },
        };

        ruleRepository.create.mockResolvedValue(expectedStoredRule);

        const result = await service.create(
          createRuleDto,
          'author-id-1',
          'author-username-1',
        );

        expect(ruleRepository.create).toHaveBeenCalledWith(expectedStoredRule);
        expect(result).toEqual(expectedReturnedRule);
        expect(result.metadata?.mitre_attack).toHaveLength(1);
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty(
          'description',
        );
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty('url');
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty(
          'platforms',
        );
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty(
          'extra_field',
        );
      });

      it('should handle create operation without MITRE metadata', async () => {
        const createRuleDto: CreateRuleDto = {
          title: 'Rule without MITRE',
          description: 'A test rule without MITRE metadata',
          rule_type: RuleType.SIGMA,
          content: 'rule content',
          metadata: baseMetadataWithoutMitre,
        };

        const expectedStoredRule: StoredRule = {
          id: 'mocked-uuid',
          title: 'Rule without MITRE',
          description: 'A test rule without MITRE metadata',
          ai_generated: {
            description: false,
            title: false,
            content: false,
            tags: false,
          },
          rule_type: RuleType.SIGMA,
          status: RuleStatus.DRAFT,
          content: 'rule content',
          tags: [],
          metadata: {
            author: undefined,
            data_sources: undefined,
            date: undefined,
            false_positives: undefined,
            mitre_attack: undefined,
            mitre_tactics: undefined,
            mitre_techniques: undefined,
            modified: undefined,
            platforms: undefined,
            references: undefined,
            severity: 'medium',
            tags: [],
          },
          test_cases: [],
          created_by: 'author-id-1',
          owner_id: 'author-id-1',
          group_name: null,
          created_at: new Date('2023-01-01T00:00:00Z').getTime(),
          updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
          published_at: undefined,
          version: 1,
          contributor: 'author-username-1',
          likes: 0,
          downloads: 0,
          dislikes: 0,
          bookmarks: 0,
        };

        ruleRepository.create.mockResolvedValue(expectedStoredRule);

        const result = await service.create(
          createRuleDto,
          'author-id-1',
          'author-username-1',
        );

        expect(result.metadata?.mitre_attack).toBeUndefined();
      });
    });

    describe('update operation MITRE cleaning', () => {
      it('should clean MITRE attack metadata when updating a rule', async () => {
        // Arrange
        const updateRuleDto: UpdateRuleDto = {
          metadata: {
            severity: 'critical' as const,
            mitre_attack: [
              {
                id: 'attack-pattern--789',
                mitre_id: 'T9999',
                name: 'Updated Technique',
                type: MitreAttackObjectType.TECHNIQUE,
                // Extra fields that should be removed
                description: 'Should be removed',
                external_references: [{ source_name: 'mitre-attack' }],
                x_mitre_version: '2.0',
                extra_field: 'remove me',
              } as any,
            ],
          },
        };

        const updatedStoredRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'critical',
            mitre_attack: [
              {
                id: 'attack-pattern--789',
                mitre_id: 'T9999',
                name: 'Updated Technique',
                type: MitreAttackObjectType.TECHNIQUE,
              },
            ],
          },
          updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
        };

        ruleRepository.findOne.mockResolvedValue(mockStoredRule);
        ruleRepository.update.mockResolvedValue(updatedStoredRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(result.metadata?.mitre_attack).toHaveLength(1);
        expect(result.metadata?.mitre_attack?.[0]).toEqual({
          id: 'attack-pattern--789',
          mitre_id: 'T9999',
          name: 'Updated Technique',
          type: MitreAttackObjectType.TECHNIQUE,
        });
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty(
          'description',
        );
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty(
          'external_references',
        );
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty(
          'x_mitre_version',
        );
        expect(result.metadata?.mitre_attack?.[0]).not.toHaveProperty(
          'extra_field',
        );
      });

      it('should handle update operation that removes MITRE metadata', async () => {
        // Arrange
        const existingRule = {
          ...mockRule,
          metadata: baseMetadataWithMitre,
        };

        const updateRuleDto: UpdateRuleDto = {
          metadata: {
            ...baseMetadataWithoutMitre,
            mitre_attack: [],
          },
        };

        const updatedStoredRule: StoredRule = {
          ...mockStoredRule,
          metadata: {
            ...mockStoredRule.metadata,
            severity: 'medium',
            mitre_attack: [],
          },
          updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
        };

        jest.spyOn(service, 'findOne').mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(updatedStoredRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        expect(result.metadata?.mitre_attack).toEqual([]);
      });

      it('should handle partial metadata updates without affecting MITRE data', async () => {
        // Arrange
        const existingRule = {
          ...mockRule,
          metadata: baseMetadataWithMitre,
        };

        const updateRuleDto: UpdateRuleDto = {
          title: 'Updated Title',
          // No metadata update
        };

        const updatedStoredRule: StoredRule = {
          ...mockStoredRule,
          title: 'Updated Title',
          // Preserve the existing metadata with MITRE attack data
          metadata: {
            author: undefined,
            data_sources: undefined,
            date: undefined,
            false_positives: undefined,
            mitre_attack: [baseMitreObject],
            mitre_tactics: undefined,
            mitre_techniques: undefined,
            modified: undefined,
            platforms: undefined,
            references: undefined,
            severity: 'high',
            tags: ['test', 'security'],
          },
          updated_at: new Date('2023-01-01T00:00:00Z').getTime(),
        };

        jest.spyOn(service, 'findOne').mockResolvedValue(existingRule);
        ruleRepository.update.mockResolvedValue(updatedStoredRule);

        // Act
        const result = await service.update('rule-id-1', updateRuleDto);

        // Assert
        // Corrected assertion for 'MITRE Attack Metadata Cleaning' test
        expect(result.title).toEqual({ value: 'Updated Title' });
        expect(result.metadata?.mitre_attack).toEqual([baseMitreObject]);
      });
    });
  });
});
