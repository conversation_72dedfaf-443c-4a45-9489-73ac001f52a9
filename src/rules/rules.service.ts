import {
  Injectable,
  NotFoundException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { RuleRepository } from './repositories/rule.repository';
import {
  RuleStatus,
  Rule,
  CreateRuleDto,
  UpdateRuleDto,
  RuleType,
  RuleTitleObject,
} from './models/rule.model';
import { S3Service } from '../s3/s3.service';
import { S3SignedUrlOperation } from '../s3/s3.types';
import * as AdmZip from 'adm-zip';
import { ConfigService } from '@nestjs/config';
import { StoredRule } from './schemas/rule.schema';
import { RuleInteractionType } from '../rule-interactions/models/rule-interaction.model';
import { toStrictRuleMetadata } from './models/rule-metadata.model';
import { Cache, CACHE_MANAGER } from '@nestjs/cache-manager';
import { OpenSearchService } from '../opensearch/opensearch.service';
import { OpenSearchConfigService } from '../opensearch/config/opensearch.config';
import { cleanMitreAttackMetadata } from '../mitre/utils/mitre-metadata.utils';

export enum CounterChange {
  INCREMENT = 'increment',
  DECREMENT = 'decrement',
}

/**
 * Service for managing detection rules
 */
@Injectable()
export class RulesService {
  private readonly logger = new Logger(RulesService.name);
  private readonly s3BucketName: string;
  private readonly downloadUrlExpiry: number;

  constructor(
    private readonly ruleRepository: RuleRepository,
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
    private readonly openSearchService: OpenSearchService,
    private readonly opensearchConfigService: OpenSearchConfigService,
  ) {
    // Get S3 configuration from environment variables
    const bucketName = this.configService.get<string>(
      'RULES_DOWNLOAD_S3_BUCKET_NAME',
    );
    if (!bucketName) {
      this.logger.error(
        'RULES_DOWNLOAD_S3_BUCKET_NAME environment variable is not defined',
      );
      throw new Error('Download S3 bucket configuration is missing');
    }
    this.s3BucketName = bucketName;

    this.downloadUrlExpiry = parseInt(
      this.configService.get<string>('RULES_DOWNLOAD_URL_EXPIRY') || '3600',
      10,
    );
  }

  /**
   * Create a new rule
   * @param createRuleDto Rule creation data
   * @param created_by_user_id ID of the user creating the rule
   * @param created_by_username Username of the user creating the rule
   * @param group_name Optional group name for published rules
   * @returns Created rule
   */
  async create(
    createRuleDto: CreateRuleDto,
    created_by_user_id: string,
    created_by_username: string | null,
    group_name?: string,
  ): Promise<Rule> {
    const now = new Date().toISOString();

    // Validate group name is provided if status is PUBLISHED
    if (createRuleDto.status === RuleStatus.PUBLISHED && !group_name) {
      // Log internal server error as group name should be looked up from group_id prior to publishing
      this.logger.error(
        'Failed to publish rule due to group name not provided',
      );
      throw new InternalServerErrorException('Failed to publish rule');
    }

    // Clean MITRE attack metadata
    const cleanedMetadata =
      cleanMitreAttackMetadata(createRuleDto.metadata) || {};

    if (!cleanedMetadata.date) {
      cleanedMetadata.date = now;
    }
    if (!cleanedMetadata.modified_date) {
      cleanedMetadata.modified = now;
    }

    const { title: dtoTitle, ...restOfCreateDto } = createRuleDto;

    const rule: Rule = {
      id: uuidv4(),
      created_by: created_by_user_id,
      contributor: created_by_username || 'Unknown',
      ...restOfCreateDto,
      title: dtoTitle ? { value: dtoTitle } : undefined,
      tags: createRuleDto.tags || [],
      metadata: cleanedMetadata || {},
      test_cases: createRuleDto.test_cases || [],
      status: createRuleDto.status || RuleStatus.DRAFT,
      version: 1,
      created_at: now,
      updated_at: now,
      published_at:
        createRuleDto.status === RuleStatus.PUBLISHED ? now : undefined,
      likes: 0,
      downloads: 0,
      dislikes: 0,
      bookmarks: 0,
      group_name: group_name || undefined,
    };

    this.logger.debug(
      `[RulesService Create] Metadata before mapping to StoredRule: ${JSON.stringify(rule.metadata)}`,
    );

    let storedRule = this.mapRuleToStoredRule(rule);
    storedRule = await this.ruleRepository.create(storedRule);

    return this.mapRuleToDto(storedRule);
  }

  /**
   * Get a rule by ID
   * @param id Rule ID
   * @returns Rule or null if not found
   * @throws NotFoundException if rule not found
   */
  async findOne(id: string): Promise<Rule> {
    const rule = await this.ruleRepository.findOne({ id });
    if (!rule) {
      throw new NotFoundException(`Rule with ID ${id} not found`);
    }
    return this.mapRuleToDto(rule);
  }

  /**
   * Update a rule
   * @param id Rule ID
   * @param updateRuleDto Rule update data
   * @param group_name group name required only when publishing a rule
   * @returns Updated rule
   * @throws NotFoundException if rule not found
   */
  async update(
    id: string,
    updateRuleDto: UpdateRuleDto,
    group_name?: string,
  ): Promise<Rule> {
    // Check if rule exists
    const existing = await this.findOne(id);

    // Validate group name is provided if status is changing to PUBLISHED
    if (
      updateRuleDto.status === RuleStatus.PUBLISHED &&
      !group_name &&
      existing.status !== RuleStatus.PUBLISHED
    ) {
      // Log internal server error as group name should be looked up from group_id prior to publishing
      this.logger.error(
        'Failed to publish rule due to group name not provided',
      );
      throw new InternalServerErrorException('Failed to publish rule');
    }

    // Clean MITRE attack metadata if provided
    const cleanedMetadata = updateRuleDto.metadata
      ? cleanMitreAttackMetadata(updateRuleDto.metadata)
      : updateRuleDto.metadata;

    const now = new Date().toISOString();

    const { title: dtoTitle, ...restOfUpdateDto } = updateRuleDto;
    let finalTitle: RuleTitleObject | undefined = existing.title;

    if (Object.prototype.hasOwnProperty.call(updateRuleDto, 'title')) {
      finalTitle = dtoTitle ? { value: dtoTitle } : undefined;
    }

    const updatedRule: Rule = {
      ...existing,
      ...restOfUpdateDto,
      title: finalTitle,
      updated_at: now,
      group_name: group_name || undefined,
      metadata: {
        ...existing.metadata,
        ...(cleanedMetadata || {}),
        modified: updateRuleDto.metadata?.modified || now,
      },
    };

    if (updateRuleDto.status === RuleStatus.PUBLISHED) {
      updatedRule.published_at = new Date().toISOString();
    }

    if (
      existing.status === RuleStatus.PUBLISHED &&
      updateRuleDto.status === RuleStatus.DRAFT
    ) {
      // Remove published group from the rule
      updatedRule.group_id = undefined;
      updatedRule.group_name = undefined;
    }

    const storedRule = this.mapRuleToStoredRule(updatedRule);

    const updated = await this.ruleRepository.update({ id }, storedRule);
    if (!updated) {
      throw new NotFoundException(`Rule with ID ${id} not found`);
    }

    return this.mapRuleToDto(updated);
  }

  /**
   * Delete a rule
   * @param id Rule ID
   * @returns Deleted rule
   * @throws NotFoundException if rule not found
   */
  async remove(id: string): Promise<Rule> {
    const storedRule = await this.ruleRepository.delete({ id });
    if (!storedRule) {
      throw new NotFoundException(`Rule with ID ${id} not found`);
    }

    return this.mapRuleToDto(storedRule);
  }

  /**
   * Bulk download rules based on IDs
   * @param ids Array of rule IDs to download
   * @returns Signed URL for the zip file download and expiry time
   */
  async bulkDownload(
    ids: string[],
  ): Promise<{ downloadUrl: string; expiresIn: number }> {
    if (!ids || ids.length === 0) {
      throw new Error('No rule IDs provided for bulk download');
    }

    try {
      // 1. Fetch all rules by IDs
      const keys = ids.map((id) => ({ id }));
      const batchResult = await this.ruleRepository.batchGet(keys);

      if (batchResult.successful.length === 0) {
        throw new NotFoundException('No rules found with the provided IDs');
      }

      // Log if there were any failed fetches
      if (batchResult.failed.length > 0) {
        const failedIds = batchResult.failed.map((failure) =>
          typeof failure.item === 'object' && failure.item !== null
            ? failure.item.id
            : 'unknown',
        );
        this.logger.warn(`Failed to fetch some rules: ${failedIds.join(', ')}`);
      }

      const rules = batchResult.successful;

      // Create a zip file containing all rules, entirely in memory
      const zipBuffer = await this.createRulesZip(
        rules.map((rule) => this.mapRuleToDto(rule)),
      );

      // Upload the zip file to S3
      const s3Key = `downloads/rules-${uuidv4()}.zip`;

      await this.s3Service.uploadFile(
        this.s3BucketName,
        s3Key,
        zipBuffer,
        'application/zip',
      );

      // Generate a signed URL for the zip file
      const signedUrl = await this.s3Service.getSignedUrl(
        this.s3BucketName,
        s3Key,
        S3SignedUrlOperation.GET,
        this.downloadUrlExpiry,
      );

      return {
        downloadUrl: signedUrl,
        expiresIn: this.downloadUrlExpiry,
      };
    } catch (error) {
      this.logger.error('Error in bulk download operation', error);
      throw error;
    }
  }

  /**
   * Download a single rule by ID
   * @param id Rule ID to download
   * @returns Signed URL for the rule file download and expiry time
   */
  async singleRuleDownload(
    id: string,
  ): Promise<{ downloadUrl: string; expiresIn: number }> {
    try {
      // Get the rule by ID (this will throw NotFoundException if not found)
      const rule = await this.findOne(id);

      // Convert rule to JSON string
      const ruleContent = JSON.stringify(rule, null, 2);

      // Create a buffer from the JSON content
      const fileBuffer = Buffer.from(ruleContent, 'utf8');

      // Generate a clean filename with the rule ID and sanitized title
      const fileExtension = this.getFileExtensionForRuleType(rule.rule_type);
      const filename = `${rule.id}-${this.sanitizeFileName(rule.title?.value || '')}.${fileExtension}`;

      // Generate S3 key for the file
      const s3Key = `downloads/single/${filename}`;

      // Upload the file to S3
      await this.s3Service.uploadFile(
        this.s3BucketName,
        s3Key,
        fileBuffer,
        'application/json',
      );

      // Generate a signed URL for downloading the file
      const signedUrl = await this.s3Service.getSignedUrl(
        this.s3BucketName,
        s3Key,
        S3SignedUrlOperation.GET,
        this.downloadUrlExpiry,
      );

      return {
        downloadUrl: signedUrl,
        expiresIn: this.downloadUrlExpiry,
      };
    } catch (error) {
      this.logger.error(
        `Error in single rule download operation: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Create a zip file containing rules entirely in memory
   * @param rules Rules to include in the zip file
   * @returns Buffer containing the zip data
   */
  private async createRulesZip(rules: Rule[]): Promise<Buffer> {
    const zip = new AdmZip();

    // Add each rule as a JSON file directly to the zip in memory
    for (const rule of rules) {
      const ruleContent = JSON.stringify(rule, null, 2);
      const fileExtension = this.getFileExtensionForRuleType(rule.rule_type);
      const ruleFileName = `${rule.id}-${this.sanitizeFileName(rule.title?.value || '')}.${fileExtension}`;

      // Add file directly to zip without writing to disk first
      zip.addFile(ruleFileName, Buffer.from(ruleContent, 'utf8'));
    }

    // Create a metadata file with basic info
    const metadata = {
      totalRules: rules.length,
      generatedAt: new Date().toISOString(),
      ruleIds: rules.map((rule) => rule.id),
    };

    const metadataContent = JSON.stringify(metadata, null, 2);

    // Add metadata directly to zip without writing to disk
    zip.addFile('metadata.json', Buffer.from(metadataContent, 'utf8'));

    // Return zip as buffer
    return zip.toBuffer();
  }

  private getFileExtensionForRuleType(ruleType: RuleType): string {
    switch (ruleType) {
      case RuleType.CORTEXQL:
      case RuleType.ELASTIC_LUCENE:
      case RuleType.LEQL:
      case RuleType.NETWITNESS:
      case RuleType.QRADAR_AQL:
      case RuleType.S1QL:
      case RuleType.SPL:
      case RuleType.KQL:
      case RuleType.UNKNOWN:
        return 'txt';
      case RuleType.CARBONBLACK:
      case RuleType.DATADOG:
        return 'json';
      case RuleType.SIGMA:
        return 'yml';
      default:
        return 'txt';
    }
  }

  /**
   * Sanitize a file name to ensure it's valid
   * @param fileName File name to sanitize
   * @returns Sanitized file name
   */
  private sanitizeFileName(fileName: string): string {
    // Replace any characters that aren't alphanumeric, dots, dashes, or underscores with underscores
    return fileName.replace(/[^a-zA-Z0-9._-]/g, '_').substring(0, 100); // Limit the length
  }

  private mapRuleToStoredRule(rule: Rule): StoredRule {
    // Map all the fields from the rule to the stored rule
    return {
      id: rule.id,
      title: rule.title?.value || null,
      description: rule.description || '',
      ai_generated: {
        description: rule.ai_generated?.description || false,
        title: rule.ai_generated?.title || false,
        content: rule.ai_generated?.content || false,
        tags: rule.ai_generated?.tags || false,
      },
      rule_type: rule.rule_type,
      content: rule.content,
      tags: rule.tags,
      metadata: toStrictRuleMetadata({
        ...(rule.metadata || {}),
        tags: rule.tags || [],
      }),
      test_cases: rule.test_cases,
      created_at: this.dateToTimestampWithDefault(rule.created_at),
      updated_at: this.dateToTimestampWithDefault(rule.updated_at),
      published_at: this.dateToTimestamp(rule.published_at),
      owner_id: rule.group_id || rule.created_by,
      status: rule.status,
      created_by: rule.created_by,
      group_name: rule.group_name || null,
      version: rule.version,
      contributor: rule.contributor,
      likes: rule.likes || 0,
      downloads: rule.downloads || 0,
      dislikes: rule.dislikes || 0,
      bookmarks: rule.bookmarks || 0,
    };
  }

  private mapRuleToDto(rule: StoredRule): Rule {
    const ruleDto: Rule = {
      id: rule.id,
      title: rule.title ? { value: rule.title } : undefined,
      description: rule.description || '',
      ai_generated: {
        description: rule.ai_generated?.description || false,
        title: rule.ai_generated?.title || false,
        content: rule.ai_generated?.content || false,
        tags: rule.ai_generated?.tags || false,
      },
      rule_type: rule.rule_type,
      content: rule.content,
      tags: rule.tags,
      metadata: cleanMitreAttackMetadata(rule.metadata) || {},
      test_cases: rule.test_cases,
      created_at: this.timestampToDate(Number(rule.created_at)).toISOString(),
      updated_at: this.timestampToDate(Number(rule.updated_at)).toISOString(),
      published_at:
        rule.status === RuleStatus.PUBLISHED
          ? this.timestampToDate(Number(rule.published_at)).toISOString()
          : undefined,
      group_id:
        rule.status === RuleStatus.PUBLISHED ? rule.owner_id : undefined,
      group_name: rule.group_name ?? undefined,
      status: rule.status,
      created_by: rule.created_by,
      version: rule.version,
      contributor: rule.contributor,
      likes: rule.likes || 0,
      downloads: rule.downloads || 0,
      dislikes: rule.dislikes || 0,
      bookmarks: rule.bookmarks || 0,
    };
    return ruleDto;
  }

  /**
   * Convert Unix timestamp (milliseconds) to Date
   */
  private timestampToDate(timestamp: number): Date {
    return new Date(timestamp);
  }

  /**
   * Convert Date or ISO string to Unix timestamp (milliseconds)
   */
  private dateToTimestamp(date: Date | string | undefined): number | undefined {
    if (!date || typeof date !== 'string') {
      return undefined;
    }
    return this.stringToTimestamp(date);
  }

  private dateToTimestampWithDefault(date: Date | string | undefined): number {
    if (!date || typeof date !== 'string') {
      return new Date().getTime();
    }
    return this.stringToTimestamp(date);
  }

  private stringToTimestamp(date: string): number {
    return new Date(date).getTime();
  }

  /**
   * Generate cache key for rule counts
   * @param userId Optional user ID
   * @param groupId Optional group ID
   * @returns Cache key string
   */
  private generateRuleCountsCacheKey(
    userId?: string,
    groupId?: string,
  ): string {
    if (userId) {
      return `rule-counts:user-${userId}`;
    }
    if (groupId) {
      return `rule-counts:group-${groupId}`;
    }
    return 'rule-counts:global';
  }

  /**
   * Find rules by IDs preserving the order of IDs provided
   * @param ruleIds Array of rule IDs to find
   * @returns List of rules in the same order as the input IDs
   */
  async findByIds(ruleIds: string[]): Promise<Rule[]> {
    if (!ruleIds.length) {
      return [];
    }

    try {
      const rules = await this.ruleRepository.findByIds(ruleIds);
      return rules.map((rule) => this.mapRuleToDto(rule));
    } catch (error) {
      this.logger.error('Failed to find rules by IDs', error);
      throw error;
    }
  }

  /**
   * Update rule interaction counts with optimistic locking
   * @param id Rule ID
   * @param type New interaction type
   * @param change Whether to increment or decrement the counter
   * @returns Updated rule
   */
  async updateCounts(
    id: string,
    type: RuleInteractionType,
    change: CounterChange,
  ): Promise<void> {
    try {
      const columnToUpdate = this.getColumnToUpdate(type);
      if (!columnToUpdate) {
        // This interaction type does not affect the rule counts
        return;
      }

      if (change === CounterChange.INCREMENT) {
        await this.ruleRepository.incrementCounter(id, columnToUpdate);
      } else {
        await this.ruleRepository.decrementCounter(
          id,
          columnToUpdate,
        );
      }
    } catch (error) {
      this.logger.error(`Error updating rule counts for ID ${id}:`, error);
      throw error;
    }
  }

  private getColumnToUpdate(type: RuleInteractionType): string | null {
    // use a case statement to return the correct column to update
    switch (type) {
      case RuleInteractionType.DOWNLOAD:
        return 'downloads';
      case RuleInteractionType.LIKE:
        return 'likes';
      case RuleInteractionType.DISLIKE:
        return 'dislikes';
      case RuleInteractionType.BOOKMARK:
        return 'bookmarks';
      default:
        return null;
    }
  }

  /**
   * Find rules by user IDs or group IDs and owner ID filtering
   * @param userIds List of user IDs to filter by (created_by)
   * @param groupIds List of group IDs to filter by (owner_id)
   * @param ownerIdFilter Optional list of owner IDs to restrict results to
   * @param page Page number
   * @param size Page size
   * @returns List of rules and total count
   */
  async findFollowedRules(
    userIds: string[],
    groupIds: string[],
    ownerIdFilter: string[],
    page: number = 1,
    size: number = 100,
  ): Promise<{ items: Rule[]; total: number }> {
    try {
      const result = await this.ruleRepository.findFollowedRules(
        groupIds,
        userIds,
        ownerIdFilter,
        page,
        size,
      );

      return {
        items: result.items.map((rule) => this.mapRuleToDto(rule)),
        total: result.total,
      };
    } catch (error) {
      this.logger.error('Failed to find rules by user IDs or group IDs', error);
      throw error;
    }
  }

  /**
   * Reset all rules for a group to draft status and remove group association
   * @param groupId Group ID to find rules for
   * @returns Number of rules reset to draft status
   */
  async resetGroupRulesToDraft(groupId: string): Promise<number> {
    try {
      // First find the published rules
      const publishedRules =
        await this.ruleRepository.findRulesByGroupId(groupId);

      if (publishedRules.length === 0) {
        return 0;
      }

      return await this.resetRulesToDraft(publishedRules);
    } catch (error) {
      this.logger.error(
        `Failed to reset rules for group ${groupId} to draft`,
        error,
      );
      throw error;
    }
  }

  /**
   * Reset all published rules created by a user but owned by groups to draft status
   * @param userId User ID to find rules for
   * @returns Number of rules reset to draft status
   */
  async resetUserPublishedRulesToDraft(userId: string): Promise<number> {
    try {
      const publishedRules =
        await this.ruleRepository.findAllRulesCreatedByUser(userId);

      if (publishedRules.length === 0) {
        return 0;
      }

      return await this.resetRulesToDraft(publishedRules);
    } catch (error) {
      this.logger.error(
        `Failed to reset rules for user ${userId} to draft`,
        error,
      );
      throw error;
    }
  }

  private async resetRulesToDraft(rules: StoredRule[]): Promise<number> {
    // Process all updates in parallel
    if (rules.length === 0) {
      return 0;
    }

    // Filter out rules that are already draft
    const rulesToUpdate = rules.filter(
      (rule) => rule.status !== RuleStatus.DRAFT,
    );

    // Process all updates in parallel
    const updatePromises = rulesToUpdate.map((rule) =>
      this.ruleRepository.update(
        { id: rule.id },
        {
          ...rule,
          status: RuleStatus.DRAFT,
          owner_id: rule.created_by, // Set owner_id to created_by
          group_name: null, // Remove group_name
          updated_at: Date.now(),
        },
      ),
    );

    // Wait for all updates to complete
    const results = await Promise.allSettled(updatePromises);

    // Count successful updates
    const successCount = results.filter((r) => r.status === 'fulfilled').length;
    return successCount;
  }

  async updateMyRulesUsername(
    userId: string,
    username: string,
    allowedRuleIds: string[],
  ): Promise<void> {
    await this.ruleRepository.updateMyRulesUsername(
      userId,
      username,
      allowedRuleIds,
    );
  }

  /**
   * Get rule counts by user
   * @param userIds List of user IDs to get counts for
   * @returns Map of user IDs to their rule counts
   */
  async getCountsByUser(
    userIds: string[],
    groupId?: string,
  ): Promise<
    Record<
      string,
      {
        id: string;
        detections_count: number;
        likes_count: number;
        downloads_count: number;
        dislikes_count: number;
        followers_count: number;
        following_count: number;
        bookmarks_count: number;
      }
    >
  > {
    const ruleIndexName =
      this.opensearchConfigService.getIndexName('detection_rules');
    try {
      const results = await this.openSearchService
        .getClient()
        .transport.request({
          method: 'POST',
          path: '_plugins/_sql',
          body: {
            query: `
            SELECT created_by, count(id) as rule_count, sum(likes) as like_count,
                    sum(downloads) as download_count, sum(dislikes) as dislike_count
            FROM ${ruleIndexName} 
            WHERE status <> "DRAFT"
            ${userIds.length > 0 ? `AND created_by IN (${userIds.map((id) => `'${id}'`).join(',')})` : ''}
            ${groupId ? `AND owner_id = '${groupId}'` : ''}
            GROUP BY created_by;
          `,
          },
        });

      const result: Record<
        string,
        {
          id: string;
          detections_count: number;
          likes_count: number;
          downloads_count: number;
          dislikes_count: number;
          followers_count: number;
          following_count: number;
          bookmarks_count: number;
        }
      > = {};

      // Initialize result for all requested user IDs with default values
      for (const userId of userIds) {
        result[userId] = {
          id: userId,
          detections_count: 0,
          likes_count: 0,
          downloads_count: 0,
          dislikes_count: 0,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        };
      }

      // Update with actual values from OpenSearch
      for (const row of results.body['datarows']) {
        const userId = row[0];
        result[userId] = {
          id: userId,
          detections_count: row[1],
          likes_count: row[2],
          downloads_count: row[3],
          dislikes_count: row[4],
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        };
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to get rule counts by user', error);
      throw new InternalServerErrorException(
        'Failed to get rule counts by user',
      );
    }
  }

  /**
   * Get rule counts by group
   * @param groupIds List of group IDs to get counts for
   * @returns Map of group IDs to their rule counts
   */
  async getCountsByGroup(groupIds: string[]): Promise<
    Record<
      string,
      {
        id: string;
        detections_count: number;
        likes_count: number;
        downloads_count: number;
        dislikes_count: number;
      }
    >
  > {
    const ruleIndexName =
      this.opensearchConfigService.getIndexName('detection_rules');
    try {
      const results = await this.openSearchService
        .getClient()
        .transport.request({
          method: 'POST',
          path: '_plugins/_sql',
          body: {
            query: `
            SELECT owner_id, count(id) as rule_count, sum(likes) as like_count,
                    sum(downloads) as download_count, sum(dislikes) as dislike_count
            FROM ${ruleIndexName}
            WHERE status <> "DRAFT"
            ${groupIds.length > 0 ? `AND owner_id IN (${groupIds.map((id) => `'${id}'`).join(',')})` : ''}
            GROUP BY owner_id;
          `,
          },
        });

      const result: Record<
        string,
        {
          id: string;
          detections_count: number;
          likes_count: number;
          downloads_count: number;
          dislikes_count: number;
        }
      > = {};

      // Initialize result for all requested group IDs with default values
      for (const groupId of groupIds) {
        result[groupId] = {
          id: groupId,
          detections_count: 0,
          likes_count: 0,
          downloads_count: 0,
          dislikes_count: 0,
        };
      }

      // Update with actual values from OpenSearch
      for (const row of results.body['datarows']) {
        const groupId = row[0];
        result[groupId] = {
          id: groupId,
          detections_count: row[1],
          likes_count: row[2],
          downloads_count: row[3],
          dislikes_count: row[4],
        };
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to get rule counts by group', error);
      throw new InternalServerErrorException(
        'Failed to get rule counts by group',
      );
    }
  }
}
