import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID, IsString, IsArray } from 'class-validator';
import { UserResponse } from '../../auth/interfaces/user-response.interface';
import { GroupResponse } from '../../auth/interfaces/group-response.interface';
import { RuleMetadata, RuleMetadataForDisplayDto } from './rule-metadata.model';

/**
 * Defines the structure for the rule title object, as sourced from OpenSearch.
 */
export class RuleTitleObject {
  @ApiProperty({
    description: 'The main value of the title',
    example: 'Detect Suspicious Login Attempts',
  })
  value: string;

  @ApiPropertyOptional({
    description: 'Suggestion for type-ahead',
    example: 'detect suspicious login',
  })
  suggest?: string;

  @ApiPropertyOptional({ description: 'Shingles for search relevance' })
  shingles?: string;

  @ApiPropertyOptional({ description: 'Edge n-grams for partial matching' })
  edge_ngram?: string;

  @ApiPropertyOptional({ description: 'Keyword version of the title' })
  keyword?: string;
}

/**
 * Enum for rule types
 */
export enum RuleType {
  SIGMA = 'SIGMA',
  SPL = 'SPL',
  KQL = 'KQL',
  CARBONBLACK = 'CARBONBLACK',
  CORTEXQL = 'CORTEXQL',
  DATADOG = 'DATADOG',
  ELASTIC_LUCENE = 'ELASTIC_LUCENE',
  NETWITNESS = 'NETWITNESS',
  LEQL = 'LEQL',
  QRADAR_AQL = 'QRADAR_AQL',
  S1QL = 'S1QL',
  UNKNOWN = 'UNKNOWN',
}

export enum RuleStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
}

export enum RuleOrderBy {
  NAME = 'name',
  PUBLISHED_AT = 'published_at',
  LIKES = 'likes',
  DISLIKES = 'dislikes',
  DOWNLOADS = 'downloads',
  CREATED_AT = 'created_at',
  CONTRIBUTOR = 'contributor',
  AUTHOR = 'author',
  GROUP_NAME = 'group_name',
  RULE_TYPE = 'rule_type',
}

/**
 * Test case for a rule
 */
export class TestCase {
  @ApiProperty({
    description: 'Input data for the test case',
    example: { data: 'sample input' },
  })
  input: any;

  @ApiProperty({
    description: 'Expected output from the rule processing',
    example: { result: true },
  })
  expected_output: any;

  @ApiPropertyOptional({
    description: 'Description of the test case',
    example: 'Tests normal behavior with valid input',
  })
  description?: string;
}

/**
 * Rule model class
 */
export class Rule {
  @ApiProperty({
    description: 'Unique identifier of the rule',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Title of the rule as an object',
    example: {
      value: 'Detect Suspicious Login Attempts',
      suggest: 'detect suspicious login',
    },
    type: () => RuleTitleObject,
  })
  @IsOptional()
  title?: RuleTitleObject;

  @ApiProperty({
    description: 'Optional description of the rule',
    example: 'This rule detects suspicious PowerShell commands',
  })
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Information about AI-generated content',
    example: { description: false, title: false, content: false, tags: false },
    type: 'object',
    additionalProperties: false,
    properties: {
      description: {
        type: 'boolean',
        description:
          'Whether description was generated by AI without user edits',
      },
      title: {
        type: 'boolean',
        description: 'Whether title was generated by AI without user edits',
      },
      content: {
        type: 'boolean',
        description: 'Whether content was generated by AI without user edits',
      },
      tags: {
        type: 'boolean',
        description: 'Whether tags were generated by AI without user edits',
      },
    },
  })
  @IsOptional()
  ai_generated?: {
    description: boolean;
    title?: boolean;
    content?: boolean;
    tags?: boolean;
  };

  @ApiProperty({
    description: 'Status of the rule. Defaults to DRAFT.',
    enum: RuleStatus,
    enumName: 'RuleStatus',
    example: RuleStatus.DRAFT,
  })
  @IsEnum(RuleStatus)
  status: RuleStatus;

  @ApiProperty({
    description: 'Type of the rule',
    enum: RuleType,
    enumName: 'RuleType',
    example: RuleType.SIGMA,
  })
  rule_type: RuleType;

  @ApiProperty({
    description: 'Content/logic of the rule',
    example: `title: Multiple Failed Login Attempts from Same IP
logsource:
    category: authentication
    product: any
detection:
    selection:
        src_ip: '*'  # Any source IP
        event.action: 'login'
        event.outcome: 'failure'
    timeframe: 5m
    condition: selection | count(src_ip) > 5
falsepositives:
    - Load balancers
    - Proxy servers
    - Shared corporate networks
level: high
status: experimental
tags:
    - attack.initial_access
    - attack.t1110`,
  })
  content: string;

  @ApiProperty({
    description: 'The User ID who created the rule',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  created_by: string;

  @ApiProperty({
    description: 'The username of the contributor of the rules',
    example: 'john_doe',
  })
  contributor: string;

  @ApiProperty({
    description: 'Number of likes the rule has received',
    example: 42,
  })
  likes: number;

  @ApiProperty({
    description: 'Number of times the rule has been downloaded',
    example: 156,
  })
  downloads: number;

  @ApiProperty({
    description: 'Number of dislikes the rule has received',
    example: 3,
  })
  dislikes: number;

  @ApiProperty({
    description: 'Number of bookmarks the rule has received',
    example: 10,
  })
  @IsOptional()
  bookmarks?: number | null;

  @ApiProperty({
    description:
      'Group ID of the rule. Can be empty when rule is not published.',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  group_id?: string;

  @ApiProperty({
    description: 'Name of the group',
    example: 'Security Team',
  })
  @IsOptional()
  group_name?: string;

  @ApiProperty({
    description: 'Tags associated with the rule',
    example: ['security', 'authentication', 'login'],
  })
  tags: string[];

  @ApiProperty({
    description: 'Additional metadata for the rule',
    example: {
      severity: 'high',
      author: 'security-team',
      mitre_attack: [
        {
          id: 'attack-pattern--0f2ed3a1-7017-43e0-b5a4-7add082f1f8a',
          mitre_id: 'T1087',
          name: 'Account Discovery',
          description:
            'Adversaries may attempt to get a list of local system or domain accounts.',
          type: 'technique',
          url: 'https://attack.mitre.org/techniques/T1087',
        },
        {
          id: 'attack-pattern--8f4a33ec-8b1e-4915-8607-9d0acontext7c33',
          mitre_id: 'T1059.001',
          name: 'PowerShell',
          description:
            'Adversaries may abuse PowerShell commands and scripts for execution.',
          type: 'subtechnique',
          parent_name: 'Command and Scripting Interpreter',
          url: 'https://attack.mitre.org/techniques/T1059/001',
        },
        {
          id: 'x-mitre-tactic--d108ce10-2419-4cf9-8f98-5c3ac4463c22',
          mitre_id: 'TA0007',
          name: 'Discovery',
          description:
            'The Discovery tactic consists of techniques that adversaries may use to gain knowledge about the system and internal network.',
          type: 'tactic',
          url: 'https://attack.mitre.org/tactics/TA0007',
        },
      ],
      mitre_tactics: ['Discovery', 'Execution'],
      mitre_techniques: ['T1087', 'T1059.001'],
    },
  })
  @ApiPropertyOptional({
    description: 'Structured metadata associated with the rule.',
    type: () => RuleMetadataForDisplayDto,
  })
  metadata?: RuleMetadata;

  @ApiProperty({
    description: 'Test cases for validating the rule',
    type: [TestCase],
  })
  test_cases: TestCase[];

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00Z',
  })
  created_at: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-02T00:00:00Z',
  })
  updated_at: string;

  @ApiProperty({
    description: 'Published timestamp',
    example: '2023-01-03T00:00:00Z',
  })
  @IsOptional()
  published_at?: string;

  @ApiProperty({
    description: 'Version number of the rule',
    example: 1,
  })
  version: number;

  @ApiProperty({
    description: 'Whether the current user has bookmarked this rule',
    example: false,
  })
  @IsOptional()
  is_bookmarked?: boolean;

  @ApiProperty({
    description: 'Whether the current user has liked this rule',
    example: false,
  })
  @IsOptional()
  is_liked?: boolean;

  @ApiProperty({
    description: 'Whether the current user has disliked this rule',
    example: false,
  })
  @IsOptional()
  is_disliked?: boolean;
}

export class RuleWithUserAndGroup extends Rule {
  @ApiProperty({
    description: 'User who created the rule',
    type: UserResponse,
  })
  created_by_user: UserResponse;

  @ApiProperty({
    description: 'Group of the rule',
    type: GroupResponse,
  })
  @IsOptional()
  group?: GroupResponse;
}

/**
 * Data transfer object for creating a rule
 */
export class CreateRuleDto {
  @ApiProperty({
    description: 'Title of the rule',
    example: 'Detect Suspicious Login Attempts',
  })
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Description of the rule',
    example: 'This rule detects suspicious PowerShell commands',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Information about AI-generated content',
    example: { description: false, title: false, content: false, tags: false },
    type: 'object',
    additionalProperties: false,
    properties: {
      description: {
        type: 'boolean',
        description:
          'Whether description was generated by AI without user edits',
      },
      title: {
        type: 'boolean',
        description: 'Whether title was generated by AI without user edits',
      },
      content: {
        type: 'boolean',
        description: 'Whether content was generated by AI without user edits',
      },
      tags: {
        type: 'boolean',
        description: 'Whether tags were generated by AI without user edits',
      },
    },
  })
  @IsOptional()
  ai_generated?: {
    description: boolean;
    title?: boolean;
    content?: boolean;
    tags?: boolean;
  };

  @ApiProperty({
    description:
      'Group ID of the rule. Can be empty when rule is not published.',
    example: '1234567890',
  })
  @IsOptional()
  group_id?: string;

  @ApiProperty({
    description: 'Status of the rule. Defaults to DRAFT.',
    enum: RuleStatus,
    enumName: 'RuleStatus',
    example: RuleStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(RuleStatus)
  status?: RuleStatus;

  @ApiProperty({
    description: 'Type of the rule',
    enum: RuleType,
    enumName: 'RuleType',
    example: RuleType.SIGMA,
  })
  rule_type: RuleType;

  @ApiProperty({
    description: 'Content/logic of the rule',
    example: `title: Multiple Failed Login Attempts from Same IP
logsource:
    category: authentication
    product: any
detection:
    selection:
        src_ip: '*'  # Any source IP
        event.action: 'login'
        event.outcome: 'failure'
    timeframe: 5m
    condition: selection | count(src_ip) > 5
falsepositives:
    - Load balancers
    - Proxy servers
    - Shared corporate networks
level: high
status: experimental
tags:
    - attack.initial_access
    - attack.t1110`,
  })
  content: string;

  @ApiPropertyOptional({
    description: 'Tags associated with the rule',
    example: ['security', 'authentication', 'login'],
  })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Additional metadata for the rule',
    example: {
      severity: 'high',
      author: 'security-team',
      mitre_attack: [
        {
          id: 'attack-pattern--0f2ed3a1-7017-43e0-b5a4-7add082f1f8a',
          mitre_id: 'T1087',
          name: 'Account Discovery',
          description:
            'Adversaries may attempt to get a list of local system or domain accounts.',
          type: 'technique',
          url: 'https://attack.mitre.org/techniques/T1087',
        },
        {
          id: 'x-mitre-tactic--d108ce10-2419-4cf9-8f98-5c3ac4463c22',
          mitre_id: 'TA0007',
          name: 'Discovery',
          description:
            'The Discovery tactic consists of techniques that adversaries may use to gain knowledge about the system and internal network.',
          type: 'tactic',
          url: 'https://attack.mitre.org/tactics/TA0007',
        },
      ],
      mitre_tactics: ['Discovery'],
      mitre_techniques: ['T1087'],
    },
  })
  metadata?: RuleMetadata;

  @ApiPropertyOptional({
    description: 'Test cases for validating the rule',
    type: [TestCase],
  })
  test_cases?: TestCase[];
}

/**
 * Data transfer object for updating a rule
 */
export class UpdateRuleDto {
  @ApiPropertyOptional({
    description: 'Title of the rule',
    example: 'Detect Suspicious Login Attempts',
  })
  title?: string;

  @ApiPropertyOptional({
    description: 'Description of the rule',
    example: 'This rule detects suspicious PowerShell commands',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Information about AI-generated content',
    example: { description: false, title: false, content: false, tags: false },
    type: 'object',
    additionalProperties: false,
    properties: {
      description: {
        type: 'boolean',
        description:
          'Whether description was generated by AI without user edits',
      },
      title: {
        type: 'boolean',
        description: 'Whether title was generated by AI without user edits',
      },
      content: {
        type: 'boolean',
        description: 'Whether content was generated by AI without user edits',
      },
      tags: {
        type: 'boolean',
        description: 'Whether tags were generated by AI without user edits',
      },
    },
  })
  @IsOptional()
  ai_generated?: {
    description: boolean;
    title?: boolean;
    content?: boolean;
    tags?: boolean;
  };

  @ApiPropertyOptional({
    description: 'Group ID of the rule',
    example: '1234567890',
  })
  group_id?: string;

  @ApiPropertyOptional({
    description: 'Type of the rule',
    enum: RuleType,
    enumName: 'RuleType',
    example: RuleType.SIGMA,
  })
  rule_type?: RuleType;

  @ApiPropertyOptional({
    description: 'Content/logic of the rule',
    example: `title: Multiple Failed Login Attempts from Same IP
logsource:
    category: authentication
    product: any
detection:
    selection:
        src_ip: '*'  # Any source IP
        event.action: 'login'
        event.outcome: 'failure'
    timeframe: 5m
    condition: selection | count(src_ip) > 5
falsepositives:
    - Load balancers
    - Proxy servers
    - Shared corporate networks
level: high
status: experimental
tags:
    - attack.initial_access
    - attack.t1110`,
  })
  content?: string;

  @ApiPropertyOptional({
    description: 'Tags associated with the rule',
    example: ['security', 'authentication', 'login'],
  })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Additional metadata for the rule',
    example: {
      severity: 'high',
      author: 'security-team',
      mitre_attack: [
        {
          id: 'attack-pattern--0f2ed3a1-7017-43e0-b5a4-7add082f1f8a',
          mitre_id: 'T1087',
          name: 'Account Discovery',
          description:
            'Adversaries may attempt to get a list of local system or domain accounts.',
          type: 'technique',
          url: 'https://attack.mitre.org/techniques/T1087',
        },
        {
          id: 'x-mitre-tactic--d108ce10-2419-4cf9-8f98-5c3ac4463c22',
          mitre_id: 'TA0007',
          name: 'Discovery',
          description:
            'The Discovery tactic consists of techniques that adversaries may use to gain knowledge about the system and internal network.',
          type: 'tactic',
          url: 'https://attack.mitre.org/tactics/TA0007',
        },
      ],
      mitre_tactics: ['Discovery'],
      mitre_techniques: ['T1087'],
    },
  })
  metadata?: RuleMetadata;

  @ApiPropertyOptional({
    description: 'Test cases for validating the rule',
    type: [TestCase],
  })
  test_cases?: TestCase[];

  @ApiPropertyOptional({
    description: 'Status of the rule',
    enum: RuleStatus,
    enumName: 'RuleStatus',
    example: RuleStatus.DRAFT,
    default: RuleStatus.DRAFT,
  })
  status?: RuleStatus;
}

/**
 * Data transfer object for bulk download request
 */
export class BulkDownloadRulesDto {
  @ApiProperty({
    description: 'List of rule IDs to download',
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '550e8400-e29b-41d4-a716-446655440001',
    ],
    type: [String],
  })
  ids: string[];
}

/**
 * Data transfer object for bulk download response
 */
export class BulkDownloadResponseDto {
  @ApiProperty({
    description: 'Signed URL to download the rules zip file',
    example: 'https://mybucket.s3.amazonaws.com/rules.zip?AWSAccessKeyId=...',
  })
  downloadUrl: string;

  @ApiProperty({
    description: 'Expiration time of the download URL in seconds',
    example: 3600,
  })
  expiresIn: number;
}

/**
 * Data transfer object for single rule download response
 */
export class SingleRuleDownloadResponseDto {
  @ApiProperty({
    description: 'Signed URL to download the rule JSON file',
    example: 'https://mybucket.s3.amazonaws.com/rule.json?AWSAccessKeyId=...',
  })
  downloadUrl: string;

  @ApiProperty({
    description: 'Expiration time of the download URL in seconds',
    example: 3600,
  })
  expiresIn: number;
}

/**
 * Data transfer object for reset group rules to draft response
 */
export class ResetRulesToDraftResponseDto {
  @ApiProperty({
    description: 'Number of rules reset to draft status',
    example: 42,
  })
  count: number;

  @ApiProperty({
    description: 'Message describing the operation result',
    example: 'Successfully reset 42 rules to draft status',
  })
  message: string;
}

export class UserBulkLookupRequestDto {
  @ApiProperty({
    description: 'List of user IDs to look up',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  user_ids: string[];

  @ApiPropertyOptional({
    description:
      'Group ID to filter the contribution counts only to those made to the group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  group_id?: string;
}

export interface UserStats {
  id: string;
  detections_count: number;
  likes_count: number;
  downloads_count: number;
  dislikes_count: number;
  followers_count: number;
  following_count: number;
  bookmarks_count: number;
}

export interface GroupStats {
  id: string;
  detections_count: number;
  likes_count: number;
  downloads_count: number;
  dislikes_count: number;
}

export class UserBulkLookupResponseDto {
  @ApiProperty({
    description: 'Map of user IDs to their statistics',
    example: {
      'user-id-1': {
        id: 'user-id-1',
        detections_count: 10,
        likes_count: 25,
        downloads_count: 50,
        dislikes_count: 8,
        followers_count: 0,
        following_count: 0,
        bookmarks_count: 0,
      },
    },
  })
  users: Record<string, UserStats>;
}

export class GroupBulkLookupRequestDto {
  @ApiProperty({
    description: 'List of group IDs to look up',
    example: ['group-id-1', 'group-id-2'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  group_ids: string[];
}

export class GroupBulkLookupResponseDto {
  @ApiProperty({
    description: 'Map of group IDs to their statistics',
    example: {
      'group-id-1': {
        id: 'group-id-1',
        detections_count: 10,
        likes_count: 25,
        downloads_count: 50,
        dislikes_count: 8,
        followers_count: 0,
        following_count: 0,
        bookmarks_count: 0,
      },
    },
  })
  groups: Record<string, GroupStats>;
}
