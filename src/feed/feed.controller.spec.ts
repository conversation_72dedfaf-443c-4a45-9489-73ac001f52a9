import { Test, TestingModule } from '@nestjs/testing';
import { FeedController } from './feed.controller';
import { UserGroupsService } from '../auth/services/user-groups.service';
import { FgaService } from '../auth/fga/fga.service';
import { ConfigService } from '@nestjs/config';
import { EnrichmentHelper } from '../common/enrichment-helper';
import {
  Rule,
  RuleStatus,
  RuleType,
  RuleWithUserAndGroup,
  RuleOrderBy,
} from '../rules/models/rule.model';
import { FGAType, FGARelation } from '../auth/fga/fga.enums';
import { UnauthorizedException, NotFoundException } from '@nestjs/common';
import {
  DetailedUserResponse,
  UserResponse,
} from '../auth/interfaces/user-response.interface';
import {
  GroupResponse,
  GroupType,
} from '../auth/interfaces/group-response.interface';
import { FeedType } from './models/feed.model';
import { SearchService } from '../search/search.service';
import { RulesService } from '../rules/rules.service';
import { SortField } from '../search/models/sort-field.enum';
import { SortOrder } from '../search/models/sort-order.enum';
import {
  OpenSearchResponse,
  OpenSearchRuleResult,
} from '../common/helpers/rule-mappers';
import { PaginationMode } from '../search/models/pagination-mode.enum';

describe('FeedController', () => {
  let controller: FeedController;
  let mockSearchService: jest.Mocked<SearchService>;
  let mockRulesService: jest.Mocked<RulesService>;
  let mockUserGroupsService: jest.Mocked<UserGroupsService>;
  let mockFgaService: jest.Mocked<FgaService>;
  let mockEnrichmentHelper: jest.Mocked<EnrichmentHelper>;

  const mockUser: UserResponse = {
    id: 'user-id-1',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    username: 'test-user',
    display_picture_url: 'https://example.com/test-user.png',
  };

  const mockGroup: GroupResponse = {
    id: 'group-id-1',
    name: 'Test Group',
    description: 'A test group',
    type: GroupType.PUBLIC,
  };

  const mockRules: Rule[] = [
    {
      id: 'rule-id-1',
      title: { value: 'Test Rule 1' },
      description: 'Test Description 1',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 1',
      created_by: 'test-user',
      group_id: 'test-group',
      status: RuleStatus.PUBLISHED,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-1',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
    {
      id: 'rule-id-2',
      title: { value: 'Test Rule 2' },
      description: 'Test Description 2',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 2',
      created_by: 'test-user',
      status: RuleStatus.DRAFT,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-2',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
  ];

  const mockOpenSearchRules: OpenSearchRuleResult[] = [
    {
      id: 'rule-id-1',
      title: {
        value: 'Test Rule 1',
        suggest: {
          input: 'Test Rule 1',
          contexts: {
            rule_type: ['SIGMA'],
            owner_id: ['test-group'],
          },
        },
      },
      description: 'Test Description 1',
      content: 'Test Content 1',
      created_by: 'test-user',
      owner_id: 'test-group',
      status: RuleStatus.PUBLISHED,
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      version: 1,
      contributor: 'test-contributor-1',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      rule_type: RuleType.SIGMA,
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
    {
      id: 'rule-id-2',
      title: {
        value: 'Test Rule 2',
        suggest: {
          input: 'Test Rule 2',
          contexts: {
            rule_type: ['SIGMA'],
            owner_id: ['test-group'],
          },
        },
      },
      description: 'Test Description 2',
      content: 'Test Content 2',
      created_by: 'test-user',
      owner_id: 'test-group',
      status: RuleStatus.DRAFT,
      rule_type: RuleType.SIGMA,
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      version: 1,
      contributor: 'test-contributor-2',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
  ];

  const mockUnenrichedRules: Rule[] = [
    {
      id: 'rule-id-1',
      title: { value: 'Test Rule 1' },
      description: 'Test Description 1',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 1',
      created_by: 'test-user',
      group_id: 'test-group',
      group_name: undefined,
      status: RuleStatus.PUBLISHED,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-1',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      is_bookmarked: undefined,
      is_disliked: undefined,
      is_liked: undefined,
      published_at: '2021-01-01',
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
    {
      id: 'rule-id-2',
      title: { value: 'Test Rule 2' },
      description: 'Test Description 2',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 2',
      created_by: 'test-user',
      status: RuleStatus.DRAFT,
      group_id: 'test-group',
      group_name: undefined,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-2',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      is_bookmarked: undefined,
      is_disliked: undefined,
      is_liked: undefined,
      published_at: '2021-01-01',
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
  ];

  const mockEnrichedRules: RuleWithUserAndGroup[] = [
    {
      ...mockRules[0],
      created_by_user: mockUser,
      group: mockGroup,
      is_bookmarked: false,
    },
    {
      ...mockRules[1],
      created_by_user: mockUser,
      group: undefined,
      is_bookmarked: true,
    },
  ];

  const mockRequest = {
    user: {
      sub: 'external-user-id-1',
    },
    headers: {
      authorization: 'Bearer test-token',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FeedController],
      providers: [
        {
          provide: SearchService,
          useFactory: () => ({
            pagedSearch: jest.fn(),
            pagedCustomSortSearch: jest.fn(),
            simpleRawSearch: jest.fn(),
          }),
        },
        {
          provide: RulesService,
          useFactory: () => ({
            findFollowedRules: jest.fn(),
          }),
        },
        {
          provide: UserGroupsService,
          useFactory: () => ({
            getInternalUser: jest.fn().mockResolvedValue(mockUser),
            getFollowedGroupIds: jest.fn(),
            getFollowedUserIds: jest.fn(),
            getBulkUserDetails: jest.fn(),
          }),
        },
        {
          provide: FgaService,
          useFactory: () => ({
            getAccessibleResourceIds: jest.fn(),
          }),
        },
        {
          provide: ConfigService,
          useFactory: () => ({
            get: jest.fn().mockReturnValue('workspace-id-1'),
          }),
        },
        {
          provide: EnrichmentHelper,
          useFactory: () => ({
            enrichRulesWithDetails: jest.fn(),
          }),
        },
      ],
    }).compile();

    controller = module.get<FeedController>(FeedController);
    mockSearchService = module.get(SearchService);
    mockRulesService = module.get(RulesService);
    mockUserGroupsService = module.get(UserGroupsService);
    mockFgaService = module.get(FgaService);
    mockEnrichmentHelper = module.get(EnrichmentHelper);

    // Manually invoke onModuleInit to initialize ConfigService
    controller.onModuleInit();

    // Reset mock implementations before each test
    jest.clearAllMocks();

    // Setup common mock implementations
    mockUserGroupsService.getInternalUser.mockResolvedValue(
      mockUser as DetailedUserResponse,
    );
    mockFgaService.getAccessibleResourceIds.mockResolvedValue(['group-id-1']);
    mockEnrichmentHelper.enrichRulesWithDetails.mockResolvedValue(
      mockEnrichedRules,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getFeed', () => {
    it('should return a featured feed of rules by default', async () => {
      // Arrange
      const mockSearchResponse: OpenSearchResponse = {
        data: mockOpenSearchRules,
        meta: {
          total: mockRules.length,
          size: 20,
          current_page: 1,
          total_pages: 1,
          page_size: 20,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);

      // Act
      const result = await controller.getFeed(mockRequest, undefined, {});

      // Assert
      expect(mockFgaService.getAccessibleResourceIds).toHaveBeenCalledWith(
        FGAType.USER,
        'external-user-id-1',
        FGARelation.GROUP_CONTENT_VIEWER,
        FGAType.GROUP,
      );
      expect(mockSearchService.pagedCustomSortSearch).toHaveBeenCalledWith(
        expect.anything(),
        undefined,
        {
          owner_ids: ['group-id-1'],
          statuses: ['PUBLISHED'],
        },
        {
          size: 20,
          page: 1,
        },
      );
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'Bearer test-token',
      );
      /*
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalledWith(
        mockUnenrichedRules,
        'Bearer test-token',
        'user-id-1',
      );*/
      expect(result.data).toEqual(mockEnrichedRules);
      expect(result.meta).toEqual({
        current_page: 1,
        has_next_page: false,
        has_prev_page: false,
        page_size: 20,
        size: 2,
        total: 0,
        total_pages: 0,
      });
    });

    it('should return a trending feed of rules when specified', async () => {
      // Arrange
      const mockSearchResponse = {
        data: mockRules,
        meta: {
          total: mockRules.length,
          size: 20,
          current_page: 1,
          total_pages: 1,
          page_size: 20,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);

      // Act
      const result = await controller.getFeed(
        mockRequest,
        FeedType.TRENDING,
        {},
      );

      // Assert
      expect(mockSearchService.pagedCustomSortSearch).toHaveBeenCalledWith(
        expect.anything(),
        undefined,
        {
          owner_ids: ['group-id-1'],
          statuses: ['PUBLISHED'],
        },
        {
          size: 20,
          page: 1,
        },
      );
      expect(result.data).toEqual(mockEnrichedRules);
      expect(result.meta).toEqual({
        current_page: 1,
        has_next_page: false,
        has_prev_page: false,
        page_size: 20,
        size: 2,
        total: 0,
        total_pages: 0,
      });
    });

    it('should return a following feed of rules when specified', async () => {
      // Arrange
      mockRulesService.findFollowedRules.mockResolvedValue({
        items: mockRules,
        total: mockRules.length,
      });

      const mockFollowedGroupIds = ['followed-group-1'];
      const mockFollowedUserIds = ['followed-user-1'];

      mockUserGroupsService.getFollowedGroupIds.mockResolvedValue(
        mockFollowedGroupIds,
      );
      mockUserGroupsService.getFollowedUserIds.mockResolvedValue(
        mockFollowedUserIds,
      );

      // Act
      const result = await controller.getFeed(mockRequest, FeedType.FOLLOWING);

      // Assert
      expect(mockUserGroupsService.getFollowedGroupIds).toHaveBeenCalledWith(
        'Bearer test-token',
      );
      expect(mockUserGroupsService.getFollowedUserIds).toHaveBeenCalledWith(
        'Bearer test-token',
      );
      expect(mockRulesService.findFollowedRules).toHaveBeenCalledWith(
        mockFollowedUserIds,
        mockFollowedGroupIds,
        ['group-id-1'],
        1,
        20,
      );
      expect(result.data).toEqual(mockEnrichedRules);
      expect(result.meta).toEqual({
        current_page: 1,
        has_next_page: false,
        has_prev_page: false,
        page_size: 20,
        size: 2,
        total: 2,
        total_pages: 1,
      });
    });

    it('should handle custom pagination parameters', async () => {
      // Arrange
      const mockSearchResponse: OpenSearchResponse = {
        data: [mockOpenSearchRules[0]],
        meta: {
          total: 1,
          size: 10,
          current_page: 2,
          total_pages: 1,
          page_size: 10,
          has_next_page: false,
          has_prev_page: true,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);

      const paginationParams = {
        page: 2,
        size: 10,
      };

      // Act
      const result = await controller.getFeed(
        mockRequest,
        undefined,
        paginationParams,
      );

      // Assert
      expect(mockSearchService.pagedCustomSortSearch).toHaveBeenCalledWith(
        expect.anything(),
        undefined,
        {
          owner_ids: ['group-id-1'],
          statuses: ['PUBLISHED'],
        },
        {
          size: 10,
          page: 2,
        },
      );
      expect(result.meta).toEqual({
        current_page: 2,
        has_next_page: false,
        has_prev_page: true,
        page_size: 10,
        size: 2,
        total: 0,
        total_pages: 0,
      });
    });

    it('should be able to use different feed type with custom pagination', async () => {
      // Arrange
      const mockSearchResponse: OpenSearchResponse = {
        data: mockOpenSearchRules,
        meta: {
          total: mockRules.length,
          size: 5,
          current_page: 3,
          total_pages: 4,
          page_size: 5,
          has_next_page: false,
          has_prev_page: true,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);

      const paginationParams = {
        page: 3,
        size: 5,
      };

      // Act
      const result = await controller.getFeed(
        mockRequest,
        FeedType.TRENDING,
        paginationParams,
      );

      // Assert
      expect(mockSearchService.pagedCustomSortSearch).toHaveBeenCalledWith(
        expect.anything(),
        undefined,
        {
          owner_ids: ['group-id-1'],
          statuses: ['PUBLISHED'],
        },
        {
          size: 5,
          page: 3,
        },
      );
      expect(result.meta.current_page).toEqual(3);
      expect(result.meta.page_size).toEqual(5);
      expect(result.meta.has_next_page).toEqual(false);
      expect(result.meta.has_prev_page).toEqual(true);
    });
  });

  describe('getGroupFeed', () => {
    it('should return a feed of rules for a specific group', async () => {
      // Arrange
      const mockSearchResponse: OpenSearchResponse = {
        data: mockOpenSearchRules,
        meta: {
          total: mockRules.length,
          size: 20,
          current_page: 1,
          total_pages: 1,
          page_size: 20,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);

      // Act
      const result = await controller.getGroupFeed(
        mockRequest,
        'group-id-1',
        undefined,
        undefined,
        undefined,
        {},
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 20,
          page: 1,
          sort_by: SortField.PUBLISHED_AT,
          sort_order: SortOrder.DESC,
          pagination_mode: PaginationMode.OFFSET,
        },
      );
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'Bearer test-token',
      );
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalledWith(
        mockUnenrichedRules,
        'Bearer test-token',
        'user-id-1',
      );
      expect(result.data).toEqual(mockEnrichedRules);
      expect(result.meta).toEqual({
        current_page: 1,
        has_next_page: false,
        has_prev_page: false,
        page_size: 20,
        size: 2,
        total: 2,
        total_pages: 1,
      });
    });

    it('should use provided sorting parameters for group feed', async () => {
      // Arrange
      const mockSearchResponse: OpenSearchResponse = {
        data: mockOpenSearchRules,
        meta: {
          total: mockRules.length,
          size: 15,
          current_page: 2,
          total_pages: 1,
          page_size: 15,
          has_next_page: false,
          has_prev_page: true,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);

      // Act
      const result = await controller.getGroupFeed(
        mockRequest,
        'group-id-1',
        'test query',
        RuleOrderBy.NAME,
        SortOrder.ASC,
        { page: 2, size: 15 },
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        'test query',
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 15,
          page: 2,
          sort_by: SortField.TITLE,
          sort_order: SortOrder.ASC,
          pagination_mode: PaginationMode.OFFSET,
        },
      );
      expect(result.meta.current_page).toEqual(2);
      expect(result.meta.page_size).toEqual(15);
    });
  });

  describe('getUserFeed', () => {
    it('should return a feed of rules for a specific user', async () => {
      // Arrange
      const mockSearchResponse: OpenSearchResponse = {
        data: mockOpenSearchRules,
        meta: {
          total: mockRules.length,
          size: 100,
          current_page: 1,
          total_pages: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);

      // Act
      const result = await controller.getUserFeed(
        mockRequest,
        'user-id-1',
        undefined, // q parameter
        undefined, // sort_by parameter
        undefined, // sort_order parameter
        { size: 100 }, // pagination parameters
      );

      // Assert
      expect(mockFgaService.getAccessibleResourceIds).toHaveBeenCalledWith(
        FGAType.USER,
        'external-user-id-1',
        FGARelation.GROUP_CONTENT_VIEWER,
        FGAType.GROUP,
      );
      expect(mockUserGroupsService.getBulkUserDetails).toHaveBeenCalledWith(
        ['user-id-1'],
        'Bearer test-token',
      );
      // The getSortParams function will return SortField.PUBLISHED_AT when no sort parameters are provided
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
          created_by: ['user-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.PUBLISHED_AT,
          sort_order: SortOrder.DESC,
          pagination_mode: PaginationMode.OFFSET,
        },
      );
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'Bearer test-token',
      );
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalledWith(
        mockUnenrichedRules,
        'Bearer test-token',
        'user-id-1',
      );
      expect(result.data).toEqual(mockEnrichedRules);
      expect(result.meta).toEqual({
        current_page: 1,
        has_next_page: false,
        has_prev_page: false,
        page_size: 100,
        size: 2,
        total: 2,
        total_pages: 1,
      });
    });

    it('should use provided sorting parameters for user feed', async () => {
      // Arrange
      const mockSearchResponse: OpenSearchResponse = {
        data: mockOpenSearchRules,
        meta: {
          total: mockRules.length,
          size: 20,
          current_page: 1,
          total_pages: 1,
          page_size: 20,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      };
      mockSearchService.pagedSearch.mockResolvedValue(mockSearchResponse);
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);

      // Act
      const result = await controller.getUserFeed(
        mockRequest,
        'user-id-1',
        'detection', // q parameter
        RuleOrderBy.LIKES, // sort_by parameter
        SortOrder.DESC, // sort_order parameter
        { page: 3, size: 10 }, // pagination parameters
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        'detection',
        {
          owner_ids: ['group-id-1'],
          created_by: ['user-id-1'],
        },
        {
          size: 10,
          page: 3,
          sort_by: SortField.LIKES,
          sort_order: SortOrder.DESC,
          pagination_mode: PaginationMode.OFFSET,
        },
      );
      expect(result.meta.current_page).toEqual(3);
      expect(result.meta.page_size).toEqual(10);
    });

    it('should throw NotFoundException if user is not found', async () => {
      // Arrange
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([]);

      // Act & Assert
      await expect(
        controller.getUserFeed(mockRequest, 'non-existent-user-id'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getAuthorizationHeader', () => {
    it('should return the authorization header when present', () => {
      // Act
      const result = (controller as any).getAuthorizationHeader(mockRequest);

      // Assert
      expect(result).toBe('Bearer test-token');
    });

    it('should throw UnauthorizedException when authorization header is missing', () => {
      // Arrange
      const requestWithoutAuth = {
        headers: {},
      };

      // Act & Assert
      expect(() => {
        (controller as any).getAuthorizationHeader(requestWithoutAuth);
      }).toThrow(UnauthorizedException);
    });
  });

  describe('getInternalUserFromRequest', () => {
    it('should return the internal user', async () => {
      // Act
      const result = await (controller as any).getInternalUserFromRequest(
        mockRequest,
      );

      // Assert
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'Bearer test-token',
      );
      expect(result).toEqual(mockUser);
    });
  });
});
