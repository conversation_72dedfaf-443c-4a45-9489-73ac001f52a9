import { Test, TestingModule } from '@nestjs/testing';
import { ParsingService } from './parsing.service';
import { RuleType } from '../rules/models/rule.model';
import { YamlParsingError, RuleValidationError } from './parsing.errors';
import { MitreAttackObjectType } from '../rules/models/rule-metadata.model';
import { RuleParserFactory } from './services/rule-parser-factory.service';
import { ErrorHandlingService } from './services/error-handling.service';
import { ParsingTestUtils } from './test-utils/parsing-test-utils';

describe('ParsingService', () => {
  let service: ParsingService;
  let mockRuleParserFactory: jest.Mocked<RuleParserFactory>;
  let mockErrorHandlingService: jest.Mocked<ErrorHandlingService>;

  const parsers = ParsingTestUtils.createMockParsers();
  const mockParserFactory = ParsingTestUtils.createMockParserFactory(parsers);
  const mockErrorService = ParsingTestUtils.createMockErrorHandlingService();

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup parser mock implementations
    parsers.mockSigmaParser.parseRule.mockResolvedValue(
      ParsingTestUtils.createMockParseResult(RuleType.SIGMA),
    );
    parsers.mockKqlParser.parseRule.mockResolvedValue(
      ParsingTestUtils.createMockParseResult(RuleType.KQL),
    );
    parsers.mockSplParser.parseRule.mockResolvedValue(
      ParsingTestUtils.createMockParseResult(RuleType.SPL),
    );
    parsers.mockGenericParser.parseRule.mockResolvedValue(
      ParsingTestUtils.createMockParseResult(RuleType.UNKNOWN, {
        title: 'Generic Rule',
        description: 'Generic rule content',
      }),
    );

    mockRuleParserFactory =
      mockParserFactory as unknown as jest.Mocked<RuleParserFactory>;

    mockErrorHandlingService =
      mockErrorService as jest.Mocked<ErrorHandlingService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ParsingService,
        {
          provide: RuleParserFactory,
          useValue: mockRuleParserFactory,
        },
        {
          provide: ErrorHandlingService,
          useValue: mockErrorHandlingService,
        },
      ],
    }).compile();

    service = module.get<ParsingService>(ParsingService);
  });

  describe('parseRule', () => {
    it('should successfully parse a Sigma rule with explicit type', async () => {
      const result = await service.parseRule({
        content: 'title: Test Sigma Rule',
        fileName: 'test-sigma.yml',
        ruleType: RuleType.SIGMA,
      });

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.SIGMA);
      expect(result.title).toBe('Test SIGMA Rule');
      const getParserSpy = jest.spyOn(mockRuleParserFactory, 'getParser');
      expect(getParserSpy).toHaveBeenCalledWith(RuleType.SIGMA);
    });

    it('should successfully parse a KQL rule with explicit type', async () => {
      const result = await service.parseRule({
        content:
          'name: Test KQL Rule\nquery: SecurityEvent | where EventID == 4624',
        fileName: 'test-kql.yml',
        ruleType: RuleType.KQL,
      });

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.KQL);
      expect(result.title).toBe('Test KQL Rule');
      const getParserSpy = jest.spyOn(mockRuleParserFactory, 'getParser');
      expect(getParserSpy).toHaveBeenCalledWith(RuleType.KQL);
    });

    it('should auto-detect SIGMA rule type when not provided', async () => {
      const sigmaContent =
        'title: Auto-detected Sigma\ndetection:\n  selection:\n    EventID: 4624';

      const result = await service.parseRule({
        content: sigmaContent,
        fileName: 'auto-sigma.yml',
      });

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.SIGMA);
      const getParserSpy = jest.spyOn(mockRuleParserFactory, 'getParser');
      expect(getParserSpy).toHaveBeenCalledWith(RuleType.SIGMA);
    });

    it('should auto-detect KQL rule type when not provided', async () => {
      const kqlContent =
        'name: Auto-detected KQL\nquery: SecurityEvent | where EventID == 4624';

      const result = await service.parseRule({
        content: kqlContent,
        fileName: 'auto-kql.yml',
      });

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.KQL);
      const getParserSpy = jest.spyOn(mockRuleParserFactory, 'getParser');
      expect(getParserSpy).toHaveBeenCalledWith(RuleType.KQL);
    });

    it('should default to UNKNOWN when rule type cannot be detected', async () => {
      const unknownContent = 'some_field: value\nanother_field: another value';

      const result = await service.parseRule({
        content: unknownContent,
        fileName: 'unknown.yml',
      });

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.UNKNOWN);
      const getParserSpy = jest.spyOn(mockRuleParserFactory, 'getParser');
      expect(getParserSpy).toHaveBeenCalledWith(RuleType.UNKNOWN);
    });

    it('should handle YAML parsing errors during rule type detection', async () => {
      const invalidYaml = 'invalid: yaml: content:';

      const result = await service.parseRule({
        content: invalidYaml,
        fileName: 'invalid.yml',
      });

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.UNKNOWN);
      const getParserSpy = jest.spyOn(mockRuleParserFactory, 'getParser');
      expect(getParserSpy).toHaveBeenCalledWith(RuleType.UNKNOWN);
    });

    it('should handle parser errors gracefully', async () => {
      const mockParser = {
        parseRule: jest
          .fn()
          .mockRejectedValue(
            new YamlParsingError('Invalid YAML format', 'error.yml'),
          ),
      };
      mockRuleParserFactory.getParser.mockReturnValue(mockParser);

      const result = await service.parseRule({
        content: 'invalid content',
        fileName: 'error.yml',
        ruleType: RuleType.SIGMA,
      });

      expect(result.success).toBe(false);
      const createErrorResultSpy = jest.spyOn(
        mockErrorHandlingService,
        'createErrorResult',
      );
      expect(createErrorResultSpy).toHaveBeenCalled();
    });

    it('should handle generic errors gracefully', async () => {
      const mockParser = {
        parseRule: jest.fn().mockRejectedValue(new Error('Unexpected error')),
      };
      mockRuleParserFactory.getParser.mockReturnValue(mockParser);

      const result = await service.parseRule({
        content: 'some content',
        fileName: 'error.yml',
        ruleType: RuleType.SIGMA,
      });

      expect(result.success).toBe(false);
      const createGenericErrorResultSpy = jest.spyOn(
        mockErrorHandlingService,
        'createGenericErrorResult',
      );
      expect(createGenericErrorResultSpy).toHaveBeenCalled();
    });

    it('should respect user-supplied MITRE attack data', async () => {
      const userMitreAttack = [
        {
          id: 'user-supplied-1',
          mitre_id: 'T1000',
          name: 'User Tactic',
          type: MitreAttackObjectType.TACTIC,
        },
      ];

      // Setup a specific mock for this test case that returns success when called with user MITRE data
      const mockResult = ParsingTestUtils.createMockParseResult(
        RuleType.SIGMA,
        {
          title: 'Rule with user MITRE',
          metadata: {
            author: 'Test Author',
            severity: 'medium',
            mitre_attack: userMitreAttack,
            tags: ['attack.persistence'],
            data_sources: [],
            created_at: '2023-01-01T00:00:00.000Z',
            updated_at: '2023-01-01T00:00:00.000Z',
          },
        },
      );

      // Reset and setup the specific mock for this test
      jest.clearAllMocks();
      parsers.mockSigmaParser.parseRule.mockResolvedValue(mockResult);

      // Ensure the factory returns the mocked parser
      mockRuleParserFactory.getParser.mockReturnValue(parsers.mockSigmaParser);

      const result = await service.parseRule({
        content: 'title: Rule with user MITRE',
        fileName: 'user-override.yml',
        ruleType: RuleType.SIGMA,
        existingMitreAttack: userMitreAttack,
      });

      expect(result.success).toBe(true);
      expect(result.metadata?.mitre_attack).toEqual(userMitreAttack);

      const parseRuleSpy = jest.spyOn(parsers.mockSigmaParser, 'parseRule');
      expect(parseRuleSpy).toHaveBeenCalledWith(
        'title: Rule with user MITRE',
        'user-override.yml',
        userMitreAttack,
      );
    });

    describe('Input Validation', () => {
      it('should handle null input by calling createGenericErrorResult', async () => {
        // @ts-expect-error Testing invalid input
        const result = await service.parseRule(null);
        expect(result.success).toBe(false);
        const createGenericErrorResultSpy = jest.spyOn(
          mockErrorHandlingService,
          'createGenericErrorResult',
        );
        expect(createGenericErrorResultSpy).toHaveBeenCalledWith(
          expect.any(Error),
          '',
          'unknown',
          RuleType.UNKNOWN,
        );
      });

      it('should handle missing content by calling createGenericErrorResult', async () => {
        const result = await service.parseRule({
          // @ts-expect-error Testing invalid input
          content: null,
          fileName: 'no-content.yml',
        });
        expect(result.success).toBe(false);
        const createGenericErrorResultSpy = jest.spyOn(
          mockErrorHandlingService,
          'createGenericErrorResult',
        );
        expect(createGenericErrorResultSpy).toHaveBeenCalledWith(
          expect.any(Error),
          '',
          'no-content.yml',
          RuleType.UNKNOWN,
        );
      });

      it('should handle missing fileName by calling createGenericErrorResult', async () => {
        const result = await service.parseRule({
          content: 'some content',
          // @ts-expect-error Testing invalid input
          fileName: null,
        });
        expect(result.success).toBe(false);
        const createGenericErrorResultSpy = jest.spyOn(
          mockErrorHandlingService,
          'createGenericErrorResult',
        );
        expect(createGenericErrorResultSpy).toHaveBeenCalledWith(
          expect.any(Error),
          'some content',
          'unknown',
          RuleType.UNKNOWN,
        );
      });
    });

    describe('Parser Validation', () => {
      it('should handle null parser from factory by calling createErrorResult', async () => {
        mockRuleParserFactory.getParser.mockReturnValue(null as any);
        const result = await service.parseRule({
          content: 'some content',
          fileName: 'test.yml',
          ruleType: RuleType.SIGMA,
        });
        expect(result.success).toBe(false);
        const createErrorResultSpy = jest.spyOn(
          mockErrorHandlingService,
          'createErrorResult',
        );
        expect(createErrorResultSpy).toHaveBeenCalledWith(
          expect.any(RuleValidationError),
          'some content',
          'test.yml',
          RuleType.SIGMA,
        );
      });

      it('should handle parser without parseRule method by calling createErrorResult', async () => {
        mockRuleParserFactory.getParser.mockReturnValue({} as any); // Invalid parser
        const result = await service.parseRule({
          content: 'some content',
          fileName: 'test.yml',
          ruleType: RuleType.SIGMA,
        });
        expect(result.success).toBe(false);
        const createErrorResultSpy = jest.spyOn(
          mockErrorHandlingService,
          'createErrorResult',
        );
        expect(createErrorResultSpy).toHaveBeenCalledWith(
          expect.any(RuleValidationError),
          'some content',
          'test.yml',
          RuleType.SIGMA,
        );
      });
    });
  });
});
