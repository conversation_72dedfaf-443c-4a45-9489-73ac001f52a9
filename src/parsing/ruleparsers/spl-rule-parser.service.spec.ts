import { Test, TestingModule } from '@nestjs/testing';
import { SplRuleParser } from './spl-rule-parser.service';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { RuleType } from '../../rules/models/rule.model';
import { MitreAttackObject } from '../../rules/models/rule-metadata.model';

// Mock services
const mockMitreDataProcessor = {
  processAndEnrichMitreData: jest.fn(),
};

const mockMetadataBuilder = {
  createBaseMetadata: jest.fn(),
  populateCommonMetadata: jest.fn(),
};

const mockTagProcessor = {
  extractTags: jest.fn(),
};

const mockTestCaseFactory = {
  createDefaultTestCase: jest.fn(),
};

const mockErrorHandlingService = {
  createErrorResult: jest.fn(),
};

describe('SplRuleParser', () => {
  let parser: SplRuleParser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SplRuleParser,
        { provide: MitreDataProcessor, useValue: mockMitreDataProcessor },
        { provide: MetadataBuilder, useValue: mockMetadataBuilder },
        { provide: TagProcessor, useValue: mockTagProcessor },
        { provide: TestCaseFactory, useValue: mockTestCaseFactory },
        { provide: ErrorHandlingService, useValue: mockErrorHandlingService },
      ],
    }).compile();

    parser = module.get<SplRuleParser>(SplRuleParser);
    jest.clearAllMocks();

    // Setup default mock returns
    mockMetadataBuilder.createBaseMetadata.mockReturnValue({
      source: 'file_import',
      imported_at: new Date().toISOString(),
      original_filename: 'test.spl',
    });
    mockTagProcessor.extractTags.mockReturnValue([]);
    mockTestCaseFactory.createDefaultTestCase.mockReturnValue({
      input: { event: { type: 'test' } },
      expected_output: true,
    });
    mockMitreDataProcessor.processAndEnrichMitreData.mockResolvedValue(
      undefined,
    );
  });

  it('should be defined', () => {
    expect(parser).toBeDefined();
  });

  describe('parseRule', () => {
    const mockSplContent =
      'index=security EventCode=4624 | stats count by user';
    const mockFileName = 'test-spl.spl';
    const mockExistingMitre: MitreAttackObject[] = [];

    it('should parse raw SPL content successfully', async () => {
      const result = await parser.parseRule(
        mockSplContent,
        mockFileName,
        mockExistingMitre,
      );

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.SPL);
      expect(result.content).toBe(mockSplContent);
      expect(result.tags).toContain('spl');
      expect(result.title).toBe(mockFileName);
      expect(result.description).toBe(`SPL Query from file: ${mockFileName}`);
      expect(mockMetadataBuilder.createBaseMetadata).toHaveBeenCalledWith(
        mockFileName,
        'Splunk',
      );
    });

    it('should parse YAML-wrapped SPL content with search field', async () => {
      const yamlContent = `
title: Test SPL Rule
description: Test SPL detection
search: |
  index=security EventCode=4624
  | stats count by user
tags:
  - authentication
  - login
`;

      const result = await parser.parseRule(yamlContent, mockFileName);

      expect(result.success).toBe(true);
      expect(result.rule_type).toBe(RuleType.SPL);
      expect(result.content).toBe(
        'index=security EventCode=4624\n| stats count by user\n',
      );
      expect(result.title).toBe('Test SPL Rule');
      expect(result.description).toBe('Test SPL detection');
      expect(mockMetadataBuilder.populateCommonMetadata).toHaveBeenCalled();
    });

    it('should set "SPL Query" description for non-file names', async () => {
      const result = await parser.parseRule(
        mockSplContent,
        'Untitled Detection',
      );

      expect(result.success).toBe(true);
      expect(result.description).toBe('SPL Query');
    });

    it('should handle YAML content missing search field', async () => {
      const yamlContent = `
title: Invalid SPL Rule
description: Missing search field
query: some other field
`;

      mockErrorHandlingService.createErrorResult.mockReturnValue({
        success: false,
        error: 'Missing required field: search',
        title: 'Error',
        description: 'Validation error',
        content: yamlContent,
        rule_type: RuleType.SPL,
        tags: ['error'],
        metadata: {},
        test_cases: [],
      });

      const result = await parser.parseRule(yamlContent, mockFileName);

      expect(result.success).toBe(false);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalled();
    });

    it('should handle empty SPL content', async () => {
      mockErrorHandlingService.createErrorResult.mockReturnValue({
        success: false,
        error: 'SPL query content cannot be empty.',
        title: 'Error',
        description: 'Content error',
        content: '',
        rule_type: RuleType.SPL,
        tags: ['error'],
        metadata: {},
        test_cases: [],
      });

      const result = await parser.parseRule('', mockFileName);

      expect(result.success).toBe(false);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalled();
    });

    it('should include spl tag and preserve extracted tags', async () => {
      mockTagProcessor.extractTags.mockReturnValue(['security', 'detection']);

      // Use YAML-wrapped content so that extractTags gets called
      const yamlContent = `
title: SPL Rule with Tags
search: index=security EventCode=4624 | stats count by user
tags:
  - existing-tag
`;

      const result = await parser.parseRule(yamlContent, mockFileName);

      expect(result.success).toBe(true);
      expect(result.tags).toEqual(['spl', 'security', 'detection']);
      expect(mockTagProcessor.extractTags).toHaveBeenCalledWith(
        expect.any(Object),
        RuleType.SPL,
        mockFileName,
      );
    });

    it('should process MITRE data', async () => {
      const result = await parser.parseRule(
        mockSplContent,
        mockFileName,
        mockExistingMitre,
      );

      expect(result.success).toBe(true);
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          title: mockFileName,
          description: `SPL Query from file: ${mockFileName}`,
          tags: ['spl'],
        }),
        expect.any(Object), // metadata
        mockSplContent,
        mockFileName,
        mockExistingMitre,
      );
    });

    it('should handle invalid YAML gracefully', async () => {
      const invalidYaml = 'invalid: yaml: content: [unclosed';

      const result = await parser.parseRule(invalidYaml, mockFileName);

      expect(result.success).toBe(true);
      expect(result.content).toBe(invalidYaml); // Treated as raw SPL
      expect(result.rule_type).toBe(RuleType.SPL);
    });

    it('should extract MITRE data from YAML metadata', async () => {
      const yamlWithMitre = `
title: SPL with MITRE
search: index=security
metadata:
  mitre_attack:
    - id: T1059
      name: Command and Scripting Interpreter
`;

      const result = await parser.parseRule(yamlWithMitre, mockFileName);

      expect(result.success).toBe(true);
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Object),
        'index=security',
        mockFileName,
        expect.arrayContaining([expect.objectContaining({ id: 'T1059' })]),
      );
    });
  });
});
