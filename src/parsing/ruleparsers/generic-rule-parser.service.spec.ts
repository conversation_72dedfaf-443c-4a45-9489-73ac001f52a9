import { Test, TestingModule } from '@nestjs/testing';
import { GenericRuleParser } from './generic-rule-parser.service';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { RuleType } from '../../rules/models/rule.model';
import {
  MitreAttackObject,
  MitreAttackObjectType,
} from '../../rules/models/rule-metadata.model';

// Mocks
const mockMitreDataProcessor = {
  processAndEnrichMitreData: jest.fn(),
};
const mockMetadataBuilder = {
  createBaseMetadata: jest.fn(),
  populateCommonMetadata: jest.fn(), // Not directly used by generic but good to have if structure changes
  extractDataSources: jest.fn(),
};
const mockTagProcessor = {
  extractTags: jest.fn(), // Generic doesn't extract structured tags, but MitreDataProcessor might use its output indirectly
};
const mockTestCaseFactory = {
  createDefaultTestCase: jest.fn(),
};
const mockErrorHandlingService = {
  createErrorResult: jest.fn(),
};

describe('GenericRuleParser', () => {
  let parser: GenericRuleParser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GenericRuleParser,
        { provide: MitreDataProcessor, useValue: mockMitreDataProcessor },
        { provide: MetadataBuilder, useValue: mockMetadataBuilder },
        { provide: TagProcessor, useValue: mockTagProcessor },
        { provide: TestCaseFactory, useValue: mockTestCaseFactory },
        { provide: ErrorHandlingService, useValue: mockErrorHandlingService },
      ],
    }).compile();
    parser = module.get<GenericRuleParser>(GenericRuleParser);
    jest.clearAllMocks();

    // Default mock implementations
    mockMetadataBuilder.createBaseMetadata.mockImplementation(
      (fileName, platform) => ({
        source: 'file_import',
        imported_at: new Date().toISOString(),
        original_filename: fileName || 'unknown_generic.txt',
        platform: platform,
      }),
    );
    mockTagProcessor.extractTags.mockReturnValue(['generic-tag']); // MitreDataProcessor might get this
    mockTestCaseFactory.createDefaultTestCase.mockImplementation(
      (ruleType) => ({
        input: { data: 'test' },
        expected_output: false,
        description: `Default ${ruleType} test case`,
      }),
    );
  });

  it('should be defined', () => {
    expect(parser).toBeDefined();
  });

  describe('parseRuleWithType (and parseRule via UNKNOWN)', () => {
    const mockFileName = 'my-generic-rule.txt';
    const mockContent =
      'This is some generic rule content with T1059 and TA0005 inside.';

    it('should parse with RuleType.UNKNOWN by default (via parseRule)', async () => {
      const result = await parser.parseRule(mockContent, mockFileName);

      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.title).toBe(mockFileName);
      expect(result.description).toContain(mockFileName);
      expect(result.rule_type).toBe(RuleType.UNKNOWN);
      expect(result.content).toBe(mockContent);
      expect(result.metadata?.platform).toBe('Generic');
      expect(result.test_cases?.[0].description).toContain(RuleType.UNKNOWN);
    });

    it('should parse successfully with a specified rule type', async () => {
      const specifiedRuleType = RuleType.ELASTIC_LUCENE; // Example of another type handled generically
      const result = await parser.parseRuleWithType(
        mockContent,
        mockFileName,
        [],
        specifiedRuleType,
      );

      expect(mockMetadataBuilder.createBaseMetadata).toHaveBeenCalledWith(
        mockFileName,
        specifiedRuleType,
      );
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.title).toBe(mockFileName);
      expect(result.description).toContain(mockFileName);
      expect(result.rule_type).toBe(specifiedRuleType);
      expect(result.metadata?.platform).toBe(specifiedRuleType);
      expect(result.tags).toEqual(['generic-tag']); // Comes from mockTagProcessor via mitreDataProcessor
      expect(result.test_cases?.[0].description).toContain(specifiedRuleType);
    });

    it('should use existingMitreAttack if provided', async () => {
      const existingMitre: MitreAttackObject[] = [
        {
          id: 'mitre1',
          mitre_id: 'T0001',
          name: 'Existing Technique',
          type: MitreAttackObjectType.TECHNIQUE,
        },
      ];
      await parser.parseRuleWithType(
        mockContent,
        mockFileName,
        existingMitre,
        RuleType.UNKNOWN,
      );
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        expect.objectContaining({ title: mockFileName }), // ruleData for generic parser
        expect.any(Object), // metadata
        mockContent,
        mockFileName,
        existingMitre, // This is key
      );
    });

    it('should parse successfully even with empty content', async () => {
      const emptyContent = '';
      const result = await parser.parseRuleWithType(
        emptyContent,
        mockFileName,
        [],
        RuleType.UNKNOWN,
      );

      expect(result.success).toBe(true);
      expect(result.title).toBe(mockFileName);
      expect(result.description).toContain(mockFileName);
      expect(result.content).toBe(emptyContent);
      // MitreDataProcessor will be called, but likely won't find anything
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalled();
      // ErrorHandlingService should not be called by GenericRuleParser directly
      expect(mockErrorHandlingService.createErrorResult).not.toHaveBeenCalled();
    });

    it('should re-throw errors from MitreDataProcessor', async () => {
      const mitreError = new Error('Mitre service down');
      mockMitreDataProcessor.processAndEnrichMitreData.mockRejectedValueOnce(
        mitreError,
      );

      await expect(
        parser.parseRuleWithType(
          mockContent,
          mockFileName,
          [],
          RuleType.UNKNOWN,
        ),
      ).rejects.toThrow(mitreError);

      // ErrorHandlingService should not be called by GenericRuleParser directly
      expect(mockErrorHandlingService.createErrorResult).not.toHaveBeenCalled();
    });

    it('should set title and description based on filename if content is just whitespace', async () => {
      const whitespaceContent = '   \n  \t  '; // Not empty, but effectively no parsable data
      const result = await parser.parseRuleWithType(
        whitespaceContent,
        mockFileName,
        [],
        RuleType.UNKNOWN,
      );
      expect(result.success).toBe(true);
      expect(result.title).toBe(mockFileName);
      expect(result.description).toBe(
        `Generic rule content from file: ${mockFileName}`,
      );
      expect(result.content).toBe(whitespaceContent);
    });
  });
});
