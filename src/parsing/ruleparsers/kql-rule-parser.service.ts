import { Injectable, Logger } from '@nestjs/common';
import * as yaml from 'js-yaml';
import { IRuleParser } from './rule-parser.interface';
import { ParseRuleResult, CommonRuleData } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';
import { KqlRuleDto } from '../../validation/dto/kql-rule.dto';
import {
  MitreAttackObject,
  toStrictRuleMetadata,
} from '../../rules/models/rule-metadata.model';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { RuleValidationError, RuleContentError } from '../parsing.errors';

@Injectable()
export class KqlRuleParser implements IRuleParser {
  private readonly logger = new Logger(KqlRuleParser.name);

  constructor(
    private readonly mitreDataProcessor: MitreDataProcessor,
    private readonly metadataBuilder: MetadataBuilder,
    private readonly tagProcessor: TagProcessor,
    private readonly testCaseFactory: TestCaseFactory,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  async parseRule(
    content: string,
    fileName: string,
    existingMitreAttack?: MitreAttackObject[],
  ): Promise<ParseRuleResult> {
    try {
      const { ruleData, actualKqlQueryContent, mitreAttackFromYaml } =
        this.parseKqlContent(content, fileName);

      const metadata = this.metadataBuilder.createBaseMetadata(
        fileName,
        'Azure Sentinel',
      );

      let tags = ['kql'];
      let title = fileName;

      // Check if fileName looks like an actual file name, not a default title
      const isActualFileName =
        fileName &&
        !fileName.startsWith('Untitled') &&
        fileName !== 'untitled_rule' &&
        (fileName.includes('.') || fileName.length < 50); // Likely a file if it has extension or is reasonably short

      let description = isActualFileName
        ? `KQL Query from file: ${fileName}`
        : 'KQL Query';

      if (ruleData) {
        this.metadataBuilder.populateCommonMetadata(ruleData, metadata);
        this.metadataBuilder.extractDataSources(
          ruleData,
          metadata,
          RuleType.KQL,
        );

        const kqlDto = ruleData as KqlRuleDto;
        this.metadataBuilder.populateKqlSpecificMetadata(kqlDto, metadata);

        title = String(kqlDto.name || kqlDto.title || fileName);
        description = kqlDto.description || description;

        const yamlTags = this.tagProcessor.extractTags(
          ruleData,
          RuleType.KQL,
          fileName,
        );
        tags = [...new Set([...tags, ...yamlTags])];
      }

      metadata.tags = tags;

      const effectiveRuleDataForMitre = ruleData || {
        title,
        description,
        tags,
      };

      await this.mitreDataProcessor.processAndEnrichMitreData(
        effectiveRuleDataForMitre,
        metadata,
        actualKqlQueryContent,
        fileName,
        existingMitreAttack || mitreAttackFromYaml,
      );

      return {
        title,
        description,
        rule_type: RuleType.KQL,
        content: actualKqlQueryContent,
        tags,
        metadata: toStrictRuleMetadata(metadata),
        test_cases: [this.testCaseFactory.createDefaultTestCase(RuleType.KQL)],
        success: true,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to parse KQL rule ${fileName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );
      return this.errorHandlingService.createErrorResult(
        error,
        content,
        fileName,
        RuleType.KQL,
      );
    }
  }

  private parseKqlContent(
    content: string,
    fileName: string,
  ): {
    ruleData: CommonRuleData | null;
    actualKqlQueryContent: string;
    mitreAttackFromYaml?: MitreAttackObject[];
  } {
    let ruleData: CommonRuleData | null = null;
    let actualKqlQueryContent = content;
    let mitreAttackFromYaml: MitreAttackObject[] | undefined = undefined;

    try {
      const parsedYaml = yaml.load(content) as
        | Record<string, unknown>
        | CommonRuleData
        | null;

      if (parsedYaml && typeof parsedYaml === 'object') {
        if (
          'query' in parsedYaml &&
          typeof parsedYaml.query === 'string' &&
          parsedYaml.query.trim() !== ''
        ) {
          this.logger.debug(
            `KQL rule ${fileName} appears to be YAML-wrapped. Extracting fields.`,
          );
          ruleData = parsedYaml as CommonRuleData;
          actualKqlQueryContent = parsedYaml.query;

          this.validateKqlRule(ruleData as KqlRuleDto, fileName);

          // Extract mitre_attack from metadata if present
          if (ruleData && typeof ruleData === 'object') {
            if (
              'metadata' in ruleData &&
              ruleData.metadata &&
              typeof ruleData.metadata === 'object' &&
              'mitre_attack' in ruleData.metadata &&
              ruleData.metadata.mitre_attack
            ) {
              mitreAttackFromYaml = ruleData.metadata.mitre_attack;
            } else if ('mitre_attack' in ruleData && ruleData.mitre_attack) {
              mitreAttackFromYaml =
                ruleData.mitre_attack as MitreAttackObject[];
            }
          }
        } else {
          this.logger.warn(
            `Content for KQL rule ${fileName} is YAML, but not the expected KQL-in-YAML structure (missing/empty 'query' field).`,
          );
          throw new RuleValidationError(
            'Missing required field: query',
            fileName,
          );
        }
      } else {
        if (!actualKqlQueryContent || actualKqlQueryContent.trim() === '') {
          this.logger.warn(`Raw KQL content for ${fileName} is empty.`);
          throw new RuleContentError(
            'KQL query content cannot be empty.',
            fileName,
          );
        }
        this.logger.debug(
          `Content for KQL rule ${fileName} is being treated as raw KQL (not valid YAML or not object type).`,
        );
      }
    } catch (yamlError) {
      if (yamlError instanceof yaml.YAMLException) {
        // Check if raw content is empty when YAML parsing fails
        if (!actualKqlQueryContent || actualKqlQueryContent.trim() === '') {
          this.logger.warn(`Raw KQL content for ${fileName} is empty.`);
          throw new RuleContentError(
            'KQL query content cannot be empty.',
            fileName,
          );
        }
        this.logger.debug(
          `Content for KQL rule ${fileName} is not YAML. Treating as raw KQL. Error: ${yamlError.message}`,
        );
      } else if (
        yamlError instanceof RuleValidationError ||
        yamlError instanceof RuleContentError
      ) {
        throw yamlError;
      } else {
        this.logger.warn(
          `Unexpected error trying to parse KQL content as YAML for ${fileName}: ${yamlError}. Treating as raw KQL.`,
        );
      }
    }

    return { ruleData, actualKqlQueryContent, mitreAttackFromYaml };
  }

  private validateKqlRule(ruleData: KqlRuleDto, fileName: string): void {
    if (!ruleData.name && !ruleData.title) {
      throw new RuleValidationError(
        'Missing required field: name/title',
        fileName,
      );
    }
    if (!ruleData.query) {
      throw new RuleValidationError('Missing required field: query', fileName);
    }
  }
}
