import { Test, TestingModule } from '@nestjs/testing';
import { SigmaRuleParser } from './sigma-rule-parser.service';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { RuleType } from '../../rules/models/rule.model';
import { YamlParsingError, RuleValidationError } from '../parsing.errors';
import { ParseRuleResult } from '../parsing.types';
import {
  MitreAttackObject,
  MitreAttackObjectType,
} from '../../rules/models/rule-metadata.model';
import * as yaml from 'js-yaml';

// Mocks for dependent services
const mockMitreDataProcessor = {
  processAndEnrichMitreData: jest.fn(),
};
const mockMetadataBuilder = {
  createBaseMetadata: jest.fn(),
  populateCommonMetadata: jest.fn(),
  extractDataSources: jest.fn(),
};
const mockTagProcessor = {
  extractTags: jest.fn(),
};
const mockTestCaseFactory = {
  createDefaultTestCase: jest.fn(),
};
const mockErrorHandlingService = {
  createErrorResult: jest.fn(),
};

// Spy on yaml.load
jest.mock('js-yaml');

describe('SigmaRuleParser', () => {
  let parser: SigmaRuleParser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SigmaRuleParser,
        { provide: MitreDataProcessor, useValue: mockMitreDataProcessor },
        { provide: MetadataBuilder, useValue: mockMetadataBuilder },
        { provide: TagProcessor, useValue: mockTagProcessor },
        { provide: TestCaseFactory, useValue: mockTestCaseFactory },
        { provide: ErrorHandlingService, useValue: mockErrorHandlingService },
      ],
    }).compile();
    parser = module.get<SigmaRuleParser>(SigmaRuleParser);
    jest.clearAllMocks(); // Clear all mocks before each test

    // Default mock implementations
    mockMetadataBuilder.createBaseMetadata.mockReturnValue({
      source: 'file_import',
      imported_at: new Date().toISOString(),
      original_filename: 'test.yml',
    });
    mockTagProcessor.extractTags.mockReturnValue(['tag1', 'sigma']);
    mockTestCaseFactory.createDefaultTestCase.mockReturnValue({
      input: { event: { type: 'test' } },
      expected_output: true,
      description: 'Default Sigma test case',
    });
  });

  it('should be defined', () => {
    expect(parser).toBeDefined();
  });

  describe('parseRule', () => {
    const mockFileName = 'test-sigma.yml';
    const validSigmaContent = `
title: Test Sigma Rule
status: stable
description: A test rule.
logsource:
  product: windows
  service: security
detection:
  selection:
    EventID: 4688
  condition: selection
level: high
`;
    const parsedYamlData = {
      title: 'Test Sigma Rule',
      status: 'stable',
      description: 'A test rule.',
      logsource: { product: 'windows', service: 'security' },
      detection: { selection: { EventID: 4688 }, condition: 'selection' },
      level: 'high',
    };

    it('should successfully parse a valid Sigma rule', async () => {
      (yaml.load as jest.Mock).mockReturnValue(parsedYamlData);

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(yaml.load).toHaveBeenCalledWith(validSigmaContent);
      expect(mockMetadataBuilder.createBaseMetadata).toHaveBeenCalledWith(
        mockFileName,
      );
      expect(mockMetadataBuilder.populateCommonMetadata).toHaveBeenCalled();
      expect(mockMetadataBuilder.extractDataSources).toHaveBeenCalled();
      expect(mockTagProcessor.extractTags).toHaveBeenCalled();
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalled();
      expect(mockTestCaseFactory.createDefaultTestCase).toHaveBeenCalledWith(
        RuleType.SIGMA,
      );

      expect(result.success).toBe(true);
      expect(result.title).toBe(parsedYamlData.title);
      expect(result.description).toBe(parsedYamlData.description);
      expect(result.rule_type).toBe(RuleType.SIGMA);
      expect(result.content).toBe(validSigmaContent);
      expect(result.tags).toEqual(['tag1', 'sigma']);
      expect(result.metadata).toBeDefined();
      expect(result.test_cases).toHaveLength(1);
    });

    it('should use existingMitreAttack if provided', async () => {
      (yaml.load as jest.Mock).mockReturnValue(parsedYamlData);
      const existingMitre: MitreAttackObject[] = [
        {
          id: 'm1',
          mitre_id: 'T1000',
          name: 'Mitre1',
          type: MitreAttackObjectType.TECHNIQUE,
        },
      ];

      await parser.parseRule(validSigmaContent, mockFileName, existingMitre);

      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        parsedYamlData,
        expect.any(Object), // metadata object
        validSigmaContent,
        mockFileName,
        existingMitre,
      );
    });

    it('should handle YamlParsingError', async () => {
      const invalidContent = 'invalid: yaml: here';
      const yamlError = new yaml.YAMLException('yaml parse issue');
      (yaml.load as jest.Mock).mockImplementation(() => {
        throw yamlError;
      });
      const expectedErrorResult = {
        success: false,
        error: 'yaml parse issue',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(invalidContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(YamlParsingError), // Checks if it's a YamlParsingError instance
        invalidContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle YamlParsingError when yaml.load returns null/undefined', async () => {
      (yaml.load as jest.Mock).mockReturnValue(null);
      const expectedErrorResult = {
        success: false,
        error: 'empty yaml',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(YamlParsingError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for missing title', async () => {
      const invalidData = { ...parsedYamlData, title: undefined };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'missing title',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for missing logsource', async () => {
      const invalidData = { ...parsedYamlData, logsource: undefined };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'missing logsource',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for invalid logsource (not an object)', async () => {
      const invalidData = { ...parsedYamlData, logsource: 'not_an_object' };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'invalid logsource',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for empty logsource object', async () => {
      const invalidData = { ...parsedYamlData, logsource: {} };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'empty logsource',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for missing detection', async () => {
      const invalidData = { ...parsedYamlData, detection: undefined };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'missing detection',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for invalid detection (not an object)', async () => {
      const invalidData = { ...parsedYamlData, detection: 'not_an_object' };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'invalid detection',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for missing detection.condition', async () => {
      const invalidData = {
        ...parsedYamlData,
        detection: { ...parsedYamlData.detection, condition: undefined },
      };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'missing condition',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for empty detection.condition', async () => {
      const invalidData = {
        ...parsedYamlData,
        detection: { ...parsedYamlData.detection, condition: '   ' }, // whitespace
      };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'empty condition',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle RuleValidationError for detection with no selections/filters', async () => {
      const invalidData = {
        ...parsedYamlData,
        detection: { condition: 'any_condition' }, // No other keys like 'selection'
      };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedErrorResult = {
        success: false,
        error: 'no selections',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });

    it('should handle generic errors during parsing', async () => {
      (yaml.load as jest.Mock).mockReturnValue(parsedYamlData);
      mockMitreDataProcessor.processAndEnrichMitreData.mockRejectedValueOnce(
        new Error('Internal Server Error'),
      );
      const expectedErrorResult = {
        success: false,
        error: 'generic parse error',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(validSigmaContent, mockFileName);

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(Error), // Checks for a generic error
        validSigmaContent,
        mockFileName,
        RuleType.SIGMA,
      );
    });
  });
});
