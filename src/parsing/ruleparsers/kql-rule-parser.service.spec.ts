import { Test, TestingModule } from '@nestjs/testing';
import { KqlRuleParser } from './kql-rule-parser.service';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { RuleType } from '../../rules/models/rule.model';
import { RuleValidationError, RuleContentError } from '../parsing.errors';
import { ParseRuleResult, CommonRuleData } from '../parsing.types';
import {
  MitreAttackObject,
  MitreAttackObjectType,
} from '../../rules/models/rule-metadata.model';
import * as yaml from 'js-yaml';

// Mocks for dependent services
const mockMitreDataProcessor = {
  processAndEnrichMitreData: jest.fn(),
};
const mockMetadataBuilder = {
  createBaseMetadata: jest.fn(),
  populateCommonMetadata: jest.fn(),
  extractDataSources: jest.fn(),
  populateKqlSpecificMetadata: jest.fn(),
};
const mockTagProcessor = {
  extractTags: jest.fn(),
};
const mockTestCaseFactory = {
  createDefaultTestCase: jest.fn(),
};
const mockErrorHandlingService = {
  createErrorResult: jest.fn(),
};

jest.mock('js-yaml');

describe('KqlRuleParser', () => {
  let parser: KqlRuleParser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KqlRuleParser,
        { provide: MitreDataProcessor, useValue: mockMitreDataProcessor },
        { provide: MetadataBuilder, useValue: mockMetadataBuilder },
        { provide: TagProcessor, useValue: mockTagProcessor },
        { provide: TestCaseFactory, useValue: mockTestCaseFactory },
        { provide: ErrorHandlingService, useValue: mockErrorHandlingService },
      ],
    }).compile();
    parser = module.get<KqlRuleParser>(KqlRuleParser);
    jest.clearAllMocks();

    mockMetadataBuilder.createBaseMetadata.mockReturnValue({
      source: 'file_import',
      imported_at: new Date().toISOString(),
      original_filename: 'test.kql',
      platform: 'Azure Sentinel',
    });
    mockTagProcessor.extractTags.mockReturnValue(['tag1', 'kql']);
    mockTestCaseFactory.createDefaultTestCase.mockReturnValue({
      input: { log: { message: 'test' } },
      expected_output: true,
      description: 'Default KQL test case',
    });
  });

  it('should be defined', () => {
    expect(parser).toBeDefined();
  });

  describe('parseRule', () => {
    const mockFileName = 'test-kql.kql';
    const rawKqlContent = 'SecurityEvent | where EventID == 4624';
    const yamlWrappedKqlContent = `
name: Test KQL Rule
description: A test KQL rule.
query: |
  ${rawKqlContent}
severity: Medium
    `;
    const parsedYamlData: CommonRuleData = {
      name: 'Test KQL Rule',
      description: 'A test KQL rule.',
      query: rawKqlContent,
      severity: 'Medium',
    };

    it('should parse YAML-wrapped KQL rule successfully', async () => {
      (yaml.load as jest.Mock).mockReturnValue(parsedYamlData);
      const result = await parser.parseRule(
        yamlWrappedKqlContent,
        mockFileName,
      );

      expect(yaml.load).toHaveBeenCalledWith(yamlWrappedKqlContent);
      expect(
        mockMetadataBuilder.populateKqlSpecificMetadata,
      ).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.title).toBe(parsedYamlData.name);
      expect(result.content).toBe(rawKqlContent);
      expect(result.rule_type).toBe(RuleType.KQL);
      expect(result.metadata).toBeDefined();
    });

    it('should parse raw KQL content successfully', async () => {
      (yaml.load as jest.Mock).mockImplementation(() => {
        throw new yaml.YAMLException('not yaml');
      });
      const result = await parser.parseRule(rawKqlContent, mockFileName);

      expect(result.success).toBe(true);
      expect(result.title).toBe(mockFileName); // Defaults to filename
      expect(result.content).toBe(rawKqlContent);
      expect(result.rule_type).toBe(RuleType.KQL);
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        {
          title: mockFileName,
          description: expect.stringContaining(mockFileName),
          tags: expect.any(Array),
        },
        expect.any(Object),
        rawKqlContent,
        mockFileName,
        undefined,
      );
    });

    it('should extract mitre_attack from top-level YAML if present', async () => {
      const mitreAttackFromYaml: MitreAttackObject[] = [
        {
          id: 'yaml-mitre',
          mitre_id: 'T9999',
          name: 'YAML Mitre',
          type: MitreAttackObjectType.TECHNIQUE,
        },
      ];
      const yamlWithTopLevelMitre = {
        ...parsedYamlData,
        mitre_attack: mitreAttackFromYaml,
      };
      (yaml.load as jest.Mock).mockReturnValue(yamlWithTopLevelMitre);
      await parser.parseRule(yamlWrappedKqlContent, mockFileName);
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        expect.any(Object), // ruleData
        expect.any(Object), // metadata
        rawKqlContent,
        mockFileName,
        mitreAttackFromYaml, // Expecting this to be passed
      );
    });

    it('should extract mitre_attack from metadata field in YAML if present', async () => {
      const mitreAttackInMetadata: MitreAttackObject[] = [
        {
          id: 'metadata-mitre',
          mitre_id: 'T9998',
          name: 'Metadata Mitre',
          type: MitreAttackObjectType.TECHNIQUE,
        },
      ];
      const yamlWithMetadataMitre = {
        ...parsedYamlData,
        metadata: {
          mitre_attack: mitreAttackInMetadata,
        },
      };
      (yaml.load as jest.Mock).mockReturnValue(yamlWithMetadataMitre);
      await parser.parseRule(yamlWrappedKqlContent, mockFileName);
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Object),
        rawKqlContent,
        mockFileName,
        mitreAttackInMetadata,
      );
    });

    it('should prioritize existingMitreAttack over YAML-extracted mitre_attack', async () => {
      const mitreAttackFromYaml: MitreAttackObject[] = [
        {
          id: 'yaml-mitre',
          mitre_id: 'T9999',
          type: MitreAttackObjectType.TECHNIQUE,
          name: 'YAML',
        },
      ];
      const existingExternalMitre: MitreAttackObject[] = [
        {
          id: 'external-mitre',
          mitre_id: 'T9997',
          type: MitreAttackObjectType.TECHNIQUE,
          name: 'External',
        },
      ];
      const yamlWithTopLevelMitre = {
        ...parsedYamlData,
        mitre_attack: mitreAttackFromYaml,
      };
      (yaml.load as jest.Mock).mockReturnValue(yamlWithTopLevelMitre);
      await parser.parseRule(
        yamlWrappedKqlContent,
        mockFileName,
        existingExternalMitre,
      );
      expect(
        mockMitreDataProcessor.processAndEnrichMitreData,
      ).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Object),
        rawKqlContent,
        mockFileName,
        existingExternalMitre, // existingMitreAttack should take precedence
      );
    });

    it('should handle RuleValidationError if YAML-wrapped KQL is missing name/title', async () => {
      const invalidData = {
        ...parsedYamlData,
        name: undefined,
        title: undefined,
      };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedError = { success: false } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(expectedError);

      const result = await parser.parseRule(
        yamlWrappedKqlContent,
        mockFileName,
      );
      expect(result).toBe(expectedError);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        yamlWrappedKqlContent,
        mockFileName,
        RuleType.KQL,
      );
    });

    it('should handle RuleValidationError if YAML-wrapped KQL is missing query', async () => {
      const invalidData = { ...parsedYamlData, query: undefined };
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedError = { success: false } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(expectedError);

      const result = await parser.parseRule(
        yamlWrappedKqlContent,
        mockFileName,
      );
      expect(result).toBe(expectedError);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        yamlWrappedKqlContent,
        mockFileName,
        RuleType.KQL,
      );
    });

    it('should handle RuleValidationError if YAML-wrapped KQL query is empty string', async () => {
      const invalidData = { ...parsedYamlData, query: '   ' }; // Whitespace query
      (yaml.load as jest.Mock).mockReturnValue(invalidData);
      const expectedError = { success: false } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(expectedError);

      const result = await parser.parseRule(
        yamlWrappedKqlContent, // content itself doesn't matter as yaml.load is mocked
        mockFileName,
      );
      expect(result).toBe(expectedError);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleValidationError),
        yamlWrappedKqlContent,
        mockFileName,
        RuleType.KQL,
      );
    });

    it('should handle RuleContentError for empty raw KQL content', async () => {
      (yaml.load as jest.Mock).mockImplementation(() => {
        throw new yaml.YAMLException('not yaml');
      });
      const expectedError = { success: false } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(expectedError);

      const result = await parser.parseRule('', mockFileName);
      expect(result).toBe(expectedError);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(RuleContentError),
        '',
        mockFileName,
        RuleType.KQL,
      );
    });

    it('should handle generic errors during parsing', async () => {
      (yaml.load as jest.Mock).mockReturnValue(parsedYamlData);
      mockMitreDataProcessor.processAndEnrichMitreData.mockRejectedValueOnce(
        new Error('Internal KQL Error'),
      );
      const expectedErrorResult = {
        success: false,
        error: 'generic kql parse error',
      } as ParseRuleResult;
      mockErrorHandlingService.createErrorResult.mockReturnValue(
        expectedErrorResult,
      );

      const result = await parser.parseRule(
        yamlWrappedKqlContent,
        mockFileName,
      );

      expect(result).toBe(expectedErrorResult);
      expect(mockErrorHandlingService.createErrorResult).toHaveBeenCalledWith(
        expect.any(Error),
        yamlWrappedKqlContent,
        mockFileName,
        RuleType.KQL,
      );
    });
  });
});
