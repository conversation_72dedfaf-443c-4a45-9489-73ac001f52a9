import { Injectable, Logger } from '@nestjs/common';
import * as yaml from 'js-yaml';
import { IRuleParser } from './rule-parser.interface';
import { ParseRuleResult, CommonRuleData } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';
import { SigmaRuleDto } from '../../validation/dto/sigma-rule.dto';
import {
  MitreAttackObject,
  toStrictRuleMetadata,
} from '../../rules/models/rule-metadata.model';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { YamlParsingError, RuleValidationError } from '../parsing.errors';

@Injectable()
export class SigmaRuleParser implements IRuleParser {
  private readonly logger = new Logger(SigmaRuleParser.name);

  constructor(
    private readonly mitreDataProcessor: MitreDataProcessor,
    private readonly metadataBuilder: MetadataBuilder,
    private readonly tagProcessor: TagProcessor,
    private readonly testCaseFactory: TestCaseFactory,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  async parseRule(
    content: string,
    fileName: string,
    existingMitreAttack?: MitreAttackObject[],
  ): Promise<ParseRuleResult> {
    try {
      const ruleData = this.parseYamlContent(content, fileName);
      const validatedRule = this.validateSigmaRule(ruleData, fileName);

      const metadata = this.metadataBuilder.createBaseMetadata(fileName);
      this.metadataBuilder.populateCommonMetadata(ruleData, metadata);
      this.metadataBuilder.extractDataSources(
        ruleData,
        metadata,
        RuleType.SIGMA,
      );

      const tags = this.tagProcessor.extractTags(
        ruleData,
        RuleType.SIGMA,
        fileName,
      );
      metadata.tags = tags;

      await this.mitreDataProcessor.processAndEnrichMitreData(
        ruleData,
        metadata,
        content,
        fileName,
        existingMitreAttack,
      );

      return {
        title: validatedRule.title,
        description: validatedRule.description || '',
        rule_type: RuleType.SIGMA,
        content,
        tags,
        metadata: toStrictRuleMetadata(metadata),
        test_cases: [
          this.testCaseFactory.createDefaultTestCase(RuleType.SIGMA),
        ],
        success: true,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to parse Sigma rule ${fileName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );
      return this.errorHandlingService.createErrorResult(
        error,
        content,
        fileName,
        RuleType.SIGMA,
      );
    }
  }

  private parseYamlContent(content: string, fileName: string): CommonRuleData {
    try {
      const ruleData = yaml.load(content) as CommonRuleData;
      if (!ruleData) {
        throw new YamlParsingError(
          'Failed to parse YAML content, empty result',
          fileName,
        );
      }
      return ruleData;
    } catch (e) {
      if (e instanceof yaml.YAMLException) {
        throw new YamlParsingError(e.message, fileName);
      }
      throw e;
    }
  }

  private validateSigmaRule(
    ruleData: CommonRuleData,
    fileName: string,
  ): SigmaRuleDto {
    if (!ruleData.title) {
      throw new RuleValidationError('Missing required field: title', fileName);
    }

    // Validate logsource (must exist and be an object)
    if (!ruleData.logsource || typeof ruleData.logsource !== 'object') {
      throw new RuleValidationError(
        'Missing or invalid required field: logsource (must be an object)',
        fileName,
      );
    }
    // A simple check, could be expanded if specific logsource fields are mandatory
    if (Object.keys(ruleData.logsource).length === 0) {
      throw new RuleValidationError(
        'Invalid logsource: must contain at least one field (e.g., product, category, service)',
        fileName,
      );
    }

    // Cast to Record<string, any> to safely access potential Sigma-specific fields
    const untypedRuleData = ruleData as Record<string, any>;

    // Validate detection (must exist and be an object)
    if (
      !untypedRuleData['detection'] ||
      typeof untypedRuleData['detection'] !== 'object'
    ) {
      throw new RuleValidationError(
        'Missing or invalid required field: detection (must be an object)',
        fileName,
      );
    }

    // Validate detection.condition (must exist and be a non-empty string)
    const detectionField = untypedRuleData['detection'] as Record<string, any>;
    if (
      !detectionField.condition ||
      typeof detectionField.condition !== 'string' ||
      detectionField.condition.trim() === ''
    ) {
      throw new RuleValidationError(
        'Missing or invalid required field: detection.condition (must be a non-empty string)',
        fileName,
      );
    }

    // Ensure at least one selection/filter within detection, excluding 'condition'
    const detectionKeys = Object.keys(detectionField);
    const selectionKeys = detectionKeys.filter((key) => key !== 'condition');
    if (selectionKeys.length === 0) {
      throw new RuleValidationError(
        'Invalid detection: must contain at least one selection or filter definition besides "condition"',
        fileName,
      );
    }

    return ruleData as SigmaRuleDto;
  }
}
