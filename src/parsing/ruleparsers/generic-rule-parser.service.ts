import { Injectable, Logger } from '@nestjs/common';
import { IRuleParser } from './rule-parser.interface';
import { ParseRuleResult, CommonRuleData } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';
import { MitreAttackObject } from '../../rules/models/rule-metadata.model';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';

/**
 * Generic rule parser for handling unknown or unsupported rule types.
 *
 * This parser serves as a fallback for rule content that doesn't match
 * any of the specialized parsers (Sigma, KQL, SPL, etc.). It performs
 * basic parsing by:
 *
 * 1. Extracting MITRE ATT&CK IDs from the content using pattern matching
 * 2. Creating basic metadata with the specified rule type
 * 3. Generating default test cases appropriate for the rule type
 *
 * This ensures that even unknown rule formats can be imported with
 * some level of metadata extraction and structure.
 */
@Injectable()
export class GenericRuleParser implements IRuleParser {
  private readonly logger = new Logger(GenericRuleParser.name);

  constructor(
    private readonly mitreDataProcessor: MitreDataProcessor,
    private readonly metadataBuilder: MetadataBuilder,
    private readonly tagProcessor: TagProcessor,
    private readonly testCaseFactory: TestCaseFactory,
  ) {}

  /**
   * Parse rule content using generic parsing logic
   *
   * @param content - Raw rule content to parse
   * @param fileName - Name of the source file for metadata
   * @param existingMitreAttack - User-supplied MITRE ATT&CK objects (optional)
   * @returns Promise<ParseRuleResult> with basic parsing results
   */
  async parseRule(
    content: string,
    fileName: string,
    existingMitreAttack?: MitreAttackObject[],
  ): Promise<ParseRuleResult> {
    return this.parseRuleWithType(
      content,
      fileName,
      existingMitreAttack,
      RuleType.UNKNOWN,
    );
  }

  /**
   * Parse rule content with a specific rule type override
   *
   * This method allows the generic parser to handle any rule type
   * while applying the same basic parsing logic.
   *
   * @param content - Raw rule content to parse
   * @param fileName - Name of the source file for metadata
   * @param existingMitreAttack - User-supplied MITRE ATT&CK objects (optional)
   * @param ruleType - The rule type to assign to the parsed rule
   * @returns Promise<ParseRuleResult> with parsing results using the specified type
   */
  async parseRuleWithType(
    content: string,
    fileName: string,
    existingMitreAttack?: MitreAttackObject[],
    ruleType: RuleType = RuleType.UNKNOWN,
  ): Promise<ParseRuleResult> {
    try {
      this.logger.debug(
        `Parsing rule ${fileName} with generic parser (type: ${ruleType})`,
      );

      // Create base metadata with platform based on rule type
      const platform = ruleType === RuleType.UNKNOWN ? 'Generic' : ruleType;
      const metadata = this.metadataBuilder.createBaseMetadata(
        fileName,
        platform,
      );

      // Create minimal rule data for MITRE processing
      const ruleData: CommonRuleData = {
        title: fileName,
        description: `Generic rule content from file: ${fileName}`,
      };

      // Extract MITRE data (unless user provided their own)
      await this.mitreDataProcessor.processAndEnrichMitreData(
        ruleData,
        metadata,
        content,
        fileName,
        existingMitreAttack,
      );

      // Extract tags (empty for generic content, but consistent with other parsers)
      const tags = this.tagProcessor.extractTags({}, ruleType, fileName);

      // Create default test case for this rule type
      const testCase = this.testCaseFactory.createDefaultTestCase(ruleType);

      const result: ParseRuleResult = {
        title: fileName, // Use filename as title for unknown content
        description: `Generic rule content from file: ${fileName}`,
        rule_type: ruleType,
        content,
        tags,
        metadata,
        test_cases: [testCase],
        success: true,
      };

      this.logger.debug(
        `Successfully parsed generic rule ${fileName} (type: ${ruleType})`,
      );

      return result;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to parse generic rule ${fileName} (type: ${ruleType}): ${errorMessage}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error; // Re-throw to be handled by error handling service
    }
  }
}
