import { Injectable, Logger } from '@nestjs/common';
import * as yaml from 'js-yaml';
import { IRuleParser } from './rule-parser.interface';
import { ParseRuleResult, CommonRuleData } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';
import {
  MitreAttackObject,
  toStrictRuleMetadata,
} from '../../rules/models/rule-metadata.model';
import { MitreDataProcessor } from '../services/mitre-data-processor.service';
import { MetadataBuilder } from '../services/metadata-builder.service';
import { TagProcessor } from '../services/tag-processor.service';
import { TestCaseFactory } from '../services/test-case-factory.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { RuleValidationError, RuleContentError } from '../parsing.errors';

/**
 * Parser for SPL (Splunk Processing Language) rules
 * Handles YAML-wrapped SPL queries and raw SPL content
 */
@Injectable()
export class SplRuleParser implements IRuleParser {
  private readonly logger = new Logger(SplRuleParser.name);

  constructor(
    private readonly mitreDataProcessor: MitreDataProcessor,
    private readonly metadataBuilder: MetadataBuilder,
    private readonly tagProcessor: TagProcessor,
    private readonly testCaseFactory: TestCaseFactory,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  async parseRule(
    content: string,
    fileName: string,
    existingMitreAttack?: MitreAttackObject[],
  ): Promise<ParseRuleResult> {
    try {
      const { ruleData, actualSplQueryContent, mitreAttackFromYaml } =
        this.parseSplContent(content, fileName);

      const metadata = this.metadataBuilder.createBaseMetadata(
        fileName,
        'Splunk',
      );

      let tags = ['spl'];
      let title = fileName;

      // Check if fileName looks like an actual file name, not a default title
      const isActualFileName =
        fileName &&
        !fileName.startsWith('Untitled') &&
        fileName !== 'untitled_rule' &&
        (fileName.includes('.') || fileName.length < 50);

      let description = isActualFileName
        ? `SPL Query from file: ${fileName}`
        : 'SPL Query';

      if (ruleData) {
        this.metadataBuilder.populateCommonMetadata(ruleData, metadata);
        // SPL doesn't have the same data source structure as KQL, so we skip extractDataSources

        title = String(ruleData.name || ruleData.title || fileName);
        description = ruleData.description || description;

        const yamlTags = this.tagProcessor.extractTags(
          ruleData,
          RuleType.SPL,
          fileName,
        );
        tags = [...new Set([...tags, ...yamlTags])];
      }

      metadata.tags = tags;

      const effectiveRuleDataForMitre = ruleData || {
        title,
        description,
        tags,
      };

      await this.mitreDataProcessor.processAndEnrichMitreData(
        effectiveRuleDataForMitre,
        metadata,
        actualSplQueryContent,
        fileName,
        existingMitreAttack || mitreAttackFromYaml,
      );

      return {
        title,
        description,
        rule_type: RuleType.SPL,
        content: actualSplQueryContent,
        tags,
        metadata: toStrictRuleMetadata(metadata),
        test_cases: [this.testCaseFactory.createDefaultTestCase(RuleType.SPL)],
        success: true,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to parse SPL rule ${fileName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );
      return this.errorHandlingService.createErrorResult(
        error,
        content,
        fileName,
        RuleType.SPL,
      );
    }
  }

  private parseSplContent(
    content: string,
    fileName: string,
  ): {
    ruleData: CommonRuleData | null;
    actualSplQueryContent: string;
    mitreAttackFromYaml?: MitreAttackObject[];
  } {
    let ruleData: CommonRuleData | null = null;
    let actualSplQueryContent = content;
    let mitreAttackFromYaml: MitreAttackObject[] | undefined = undefined;

    try {
      const parsedYaml = yaml.load(content) as
        | Record<string, unknown>
        | CommonRuleData
        | null;

      if (parsedYaml && typeof parsedYaml === 'object') {
        if (
          'search' in parsedYaml &&
          typeof parsedYaml.search === 'string' &&
          parsedYaml.search.trim() !== ''
        ) {
          this.logger.debug(
            `SPL rule ${fileName} appears to be YAML-wrapped. Extracting fields.`,
          );
          ruleData = {
            ...parsedYaml,
            query: parsedYaml.search,
          } as CommonRuleData;
          actualSplQueryContent = parsedYaml.search;

          this.validateSplRule(ruleData, fileName);

          // Extract mitre_attack from metadata if present
          if (ruleData && typeof ruleData === 'object') {
            if (
              'metadata' in ruleData &&
              ruleData.metadata &&
              typeof ruleData.metadata === 'object' &&
              'mitre_attack' in ruleData.metadata &&
              ruleData.metadata.mitre_attack
            ) {
              mitreAttackFromYaml = ruleData.metadata.mitre_attack;
            } else if ('mitre_attack' in ruleData && ruleData.mitre_attack) {
              mitreAttackFromYaml =
                ruleData.mitre_attack as MitreAttackObject[];
            }
          }
        } else {
          this.logger.warn(
            `Content for SPL rule ${fileName} is YAML, but not the expected SPL-in-YAML structure (missing/empty 'search' field).`,
          );
          throw new RuleValidationError(
            'Missing required field: search',
            fileName,
          );
        }
      } else {
        if (!actualSplQueryContent || actualSplQueryContent.trim() === '') {
          this.logger.warn(`Raw SPL content for ${fileName} is empty.`);
          throw new RuleContentError(
            'SPL query content cannot be empty.',
            fileName,
          );
        }
        this.logger.debug(
          `Content for SPL rule ${fileName} is being treated as raw SPL (not valid YAML or not object type).`,
        );
      }
    } catch (yamlError) {
      if (yamlError instanceof yaml.YAMLException) {
        // Check if raw content is empty when YAML parsing fails
        if (!actualSplQueryContent || actualSplQueryContent.trim() === '') {
          this.logger.warn(`Raw SPL content for ${fileName} is empty.`);
          throw new RuleContentError(
            'SPL query content cannot be empty.',
            fileName,
          );
        }
        this.logger.debug(
          `Content for SPL rule ${fileName} is not YAML. Treating as raw SPL. Error: ${yamlError.message}`,
        );
      } else if (
        yamlError instanceof RuleValidationError ||
        yamlError instanceof RuleContentError
      ) {
        throw yamlError;
      } else {
        this.logger.warn(
          `Unexpected error trying to parse SPL content as YAML for ${fileName}: ${yamlError}. Treating as raw SPL.`,
        );
      }
    }

    return { ruleData, actualSplQueryContent, mitreAttackFromYaml };
  }

  private validateSplRule(ruleData: CommonRuleData, fileName: string): void {
    if (!ruleData.name && !ruleData.title) {
      throw new RuleValidationError(
        'Missing required field: name/title',
        fileName,
      );
    }
    // Note: We check for 'search' field before calling this, so no need to validate it again
  }
}
