import { ParseRuleResult } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';
import { MitreAttackObjectType } from '../../rules/models/rule-metadata.model';

/**
 * Simplified test utility for creating mock ParseRuleResult objects
 * This replaces the overly complex createMockParseResult function
 */
export class ParsingTestUtils {
  /**
   * Create a basic mock ParseRuleResult with minimal data
   */
  static createMockParseResult(
    ruleType: RuleType,
    overrides: Partial<ParseRuleResult> = {},
  ): ParseRuleResult {
    const baseResult: ParseRuleResult = {
      title: `Test ${ruleType} Rule`,
      description: `Test ${ruleType} rule for detection`,
      rule_type: ruleType,
      content: this.getDefaultContent(ruleType),
      tags: this.getDefaultTags(ruleType),
      metadata: {
        author: ruleType === RuleType.SIGMA ? 'Test Author' : undefined,
        severity: 'medium',
        mitre_attack: this.getDefaultMitreAttack(ruleType),
        tags: this.getDefaultTags(ruleType),
        data_sources: [],
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
      },
      test_cases: [this.getDefaultTestCase(ruleType)],
      success: true,
    };

    return { ...baseResult, ...overrides };
  }

  /**
   * Create mock parser objects for testing
   */
  static createMockParsers() {
    return {
      mockSigmaParser: { parseRule: jest.fn() },
      mockKqlParser: { parseRule: jest.fn() },
      mockSplParser: { parseRule: jest.fn() },
      mockGenericParser: { parseRule: jest.fn() },
    };
  }

  /**
   * Create mock factory that returns appropriate parsers
   */
  static createMockParserFactory(
    parsers: ReturnType<typeof ParsingTestUtils.createMockParsers>,
  ) {
    return {
      getParser: jest.fn().mockImplementation((ruleType: RuleType) => {
        switch (ruleType) {
          case RuleType.SIGMA:
            return parsers.mockSigmaParser;
          case RuleType.KQL:
            return parsers.mockKqlParser;
          case RuleType.SPL:
            return parsers.mockSplParser;
          default:
            return parsers.mockGenericParser;
        }
      }),
    };
  }

  /**
   * Create mock error handling service
   */
  static createMockErrorHandlingService() {
    return {
      createErrorResult: jest
        .fn()
        .mockImplementation(
          (
            error: unknown,
            content: string,
            fileName: string,
            ruleType: RuleType,
          ) => ({
            title: fileName,
            description: `Error processing rule: ${error instanceof Error ? error.message : 'Unknown error'}`,
            rule_type: ruleType,
            content,
            tags: [],
            metadata: {
              tags: [],
              data_sources: [],
              created_at: '2023-01-01T00:00:00.000Z',
              updated_at: '2023-01-01T00:00:00.000Z',
            },
            test_cases: [],
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          }),
        ),
      createGenericErrorResult: jest
        .fn()
        .mockImplementation(
          (
            error: unknown,
            content: string,
            fileName: string,
            ruleType: RuleType,
          ) => ({
            title: fileName,
            description: `Generic error processing rule: ${error instanceof Error ? error.message : 'Unknown error'}`,
            rule_type: ruleType,
            content,
            tags: [],
            metadata: {
              tags: [],
              data_sources: [],
              created_at: '2023-01-01T00:00:00.000Z',
              updated_at: '2023-01-01T00:00:00.000Z',
            },
            test_cases: [],
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          }),
        ),
    };
  }

  private static getDefaultContent(ruleType: RuleType): string {
    const contentMap: Record<RuleType, string> = {
      [RuleType.SIGMA]: 'test content',
      [RuleType.KQL]: 'SecurityEvent | where EventID == 4624',
      [RuleType.SPL]: 'index=security EventCode=4624',
      [RuleType.CARBONBLACK]: 'process_name:cmd.exe',
      [RuleType.CORTEXQL]: 'dataset = xdr_data',
      [RuleType.DATADOG]: 'service:web',
      [RuleType.ELASTIC_LUCENE]: 'process.name:cmd.exe',
      [RuleType.NETWITNESS]: 'filename="cmd.exe"',
      [RuleType.LEQL]: 'where(process_name="cmd.exe")',
      [RuleType.QRADAR_AQL]: 'SELECT * FROM events',
      [RuleType.S1QL]: 'ProcessName="cmd.exe"',
      [RuleType.UNKNOWN]: 'test content',
    };
    return contentMap[ruleType];
  }

  private static getDefaultTags(ruleType: RuleType): string[] {
    const tagsMap: Record<RuleType, string[]> = {
      [RuleType.SIGMA]: ['attack.persistence'],
      [RuleType.KQL]: [
        'category:Initial Access',
        'tactic:InitialAccess',
        'technique:T1078',
      ],
      [RuleType.SPL]: ['spl.search'],
      [RuleType.CARBONBLACK]: ['carbonblack.endpoint'],
      [RuleType.CORTEXQL]: ['cortex.xql'],
      [RuleType.DATADOG]: ['datadog.log'],
      [RuleType.ELASTIC_LUCENE]: ['elastic.lucene'],
      [RuleType.NETWITNESS]: ['netwitness.query'],
      [RuleType.LEQL]: ['leql.query'],
      [RuleType.QRADAR_AQL]: ['qradar.aql'],
      [RuleType.S1QL]: ['s1ql.query'],
      [RuleType.UNKNOWN]: [],
    };
    return tagsMap[ruleType];
  }

  private static getDefaultMitreAttack(ruleType: RuleType) {
    if (ruleType === RuleType.SIGMA) {
      return [
        {
          id: 'attack-pattern--test',
          mitre_id: 'T1003',
          name: 'OS Credential Dumping',
          type: MitreAttackObjectType.TECHNIQUE,
        },
      ];
    }
    if (ruleType === RuleType.KQL) {
      return [
        {
          id: 'attack-pattern--test',
          mitre_id: 'T1078',
          name: 'Valid Accounts',
          type: MitreAttackObjectType.TECHNIQUE,
        },
      ];
    }
    return [];
  }

  private static getDefaultTestCase(ruleType: RuleType) {
    const testCaseMap: Record<RuleType, any> = {
      [RuleType.SIGMA]: {
        description: 'Default Sigma rule test case',
        input: 'EventID: 4624',
        expected_output: 'Detection triggered',
      },
      [RuleType.KQL]: {
        description: 'Default KQL rule test case',
        input: 'SecurityEvent | where EventID == 4624',
        expected_output: 'Query executed successfully',
      },
      [RuleType.SPL]: {
        description: 'Default SPL rule test case',
        input: 'index=security EventCode=4624',
        expected_output: 'Search completed',
      },
      [RuleType.CARBONBLACK]: {
        description: 'Default Carbon Black test case',
        input: 'process_name:cmd.exe',
        expected_output: 'Query executed',
      },
      [RuleType.CORTEXQL]: {
        description: 'Default Cortex XQL test case',
        input: 'dataset = xdr_data',
        expected_output: 'Query executed',
      },
      [RuleType.DATADOG]: {
        description: 'Default Datadog test case',
        input: 'service:web',
        expected_output: 'Query executed',
      },
      [RuleType.ELASTIC_LUCENE]: {
        description: 'Default Elastic Lucene test case',
        input: 'process.name:cmd.exe',
        expected_output: 'Query executed',
      },
      [RuleType.NETWITNESS]: {
        description: 'Default NetWitness test case',
        input: 'filename="cmd.exe"',
        expected_output: 'Query executed',
      },
      [RuleType.LEQL]: {
        description: 'Default LEQL test case',
        input: 'where(process_name="cmd.exe")',
        expected_output: 'Query executed',
      },
      [RuleType.QRADAR_AQL]: {
        description: 'Default QRadar AQL test case',
        input: 'SELECT * FROM events',
        expected_output: 'Query executed',
      },
      [RuleType.S1QL]: {
        description: 'Default S1QL test case',
        input: 'ProcessName="cmd.exe"',
        expected_output: 'Query executed',
      },
      [RuleType.UNKNOWN]: {
        description: 'Default unknown rule test case',
        input: 'generic data',
        expected_output: 'Processed',
      },
    };
    return testCaseMap[ruleType];
  }
}
