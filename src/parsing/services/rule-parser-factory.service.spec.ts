import { Test, TestingModule } from '@nestjs/testing';
import { RuleParserFactory } from './rule-parser-factory.service';
import { SigmaRuleParser } from '../ruleparsers/sigma-rule-parser.service';
import { KqlRuleParser } from '../ruleparsers/kql-rule-parser.service';
import { SplRuleParser } from '../ruleparsers/spl-rule-parser.service';
import { GenericRuleParser } from '../ruleparsers/generic-rule-parser.service';
import { RuleType } from '../../rules/models/rule.model';
import { IRuleParser } from '../ruleparsers/rule-parser.interface';

// Mocks for the actual parser services
const mockSigmaParser = { parseRule: jest.fn() };
const mockKqlParser = { parseRule: jest.fn() };
const mockSplParser = { parseRule: jest.fn() };
const mockGenericParser = { parseRuleWithType: jest.fn() }; // Note: mock parseRuleWithType

describe('RuleParserFactory', () => {
  let factory: RuleParserFactory;
  let genericParser: GenericRuleParser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RuleParserFactory,
        { provide: SigmaRuleParser, useValue: mockSigmaParser },
        { provide: KqlRuleParser, useValue: mockKqlParser },
        { provide: SplRuleParser, useValue: mockSplParser },
        { provide: GenericRuleParser, useValue: mockGenericParser },
      ],
    }).compile();

    factory = module.get<RuleParserFactory>(RuleParserFactory);
    genericParser = module.get<GenericRuleParser>(GenericRuleParser);
  });

  it('should be defined', () => {
    expect(factory).toBeDefined();
  });

  it('should return SigmaRuleParser for SIGMA type', () => {
    const parser = factory.getParser(RuleType.SIGMA);
    expect(parser).toBe(mockSigmaParser);
  });

  it('should return KqlRuleParser for KQL type', () => {
    const parser = factory.getParser(RuleType.KQL);
    expect(parser).toBe(mockKqlParser);
  });

  it('should return SplRuleParser for SPL type', () => {
    const parser = factory.getParser(RuleType.SPL);
    expect(parser).toBe(mockSplParser);
  });

  it('should return a delegate to GenericRuleParser for UNKNOWN type', async () => {
    const parser = factory.getParser(RuleType.UNKNOWN);
    expect(parser).toBeDefined();
    expect(parser.parseRule).toBeInstanceOf(Function);

    // Test if the delegate calls the generic parser's parseRuleWithType
    const mockArgs = ['content', 'file.txt', []];
    // @ts-ignore because parseRule is assigned dynamically for this case
    await parser.parseRule(...mockArgs);
    expect(mockGenericParser.parseRuleWithType).toHaveBeenCalledWith(
      ...mockArgs,
      RuleType.UNKNOWN,
    );
  });

  it('should return a delegate to GenericRuleParser for other unhandled types', async () => {
    const unhandledType = 'SOME_NEW_TYPE' as RuleType; // Simulate a new rule type
    const parser = factory.getParser(unhandledType);
    expect(parser).toBeDefined();
    expect(parser.parseRule).toBeInstanceOf(Function);

    const mockArgs = ['new_content', 'new_file.dat', []];
    // @ts-ignore because parseRule is assigned dynamically for this case
    await parser.parseRule(...mockArgs);
    expect(mockGenericParser.parseRuleWithType).toHaveBeenCalledWith(
      ...mockArgs,
      unhandledType,
    );
  });
});
