import { Test, TestingModule } from '@nestjs/testing';
import { MitreDataProcessor } from './mitre-data-processor.service';
import { MitreSearchService } from '../../mitre/services/mitre-search.service';
import {
  RuleMetadata,
  MitreAttackObject,
  MitreAttackObjectType,
} from '../../rules/models/rule-metadata.model';
import { CommonRuleData } from '../parsing.types';

// Mock MitreSearchService
const mockMitreSearchService = {
  getObjectByUniqueMitreId: jest.fn(),
  getObjectByUniqueName: jest.fn(),
  searchMitreData: jest.fn(),
};

describe('MitreDataProcessor', () => {
  let service: MitreDataProcessor;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MitreDataProcessor,
        {
          provide: MitreSearchService,
          useValue: mockMitreSearchService,
        },
      ],
    }).compile();

    service = module.get<MitreDataProcessor>(MitreDataProcessor);
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('extractMitreIdsFromContent', () => {
    it('should return empty array for invalid content', () => {
      // @ts-expect-error testing invalid parameters
      expect(service.extractMitreIdsFromContent(null)).toEqual([]);
      // @ts-expect-error testing invalid parameters
      expect(service.extractMitreIdsFromContent(undefined)).toEqual([]);
      expect(service.extractMitreIdsFromContent('')).toEqual([]);
    });
    it('should extract various MITRE ID formats', () => {
      const content = `
        This rule detects T1003 (OS Credential Dumping) and its sub-technique T1003.001.
        It also covers tactic TA0006 (Credential Access).
        Related to group G0016 (APT29) and software S0154 (Cobalt Strike).
        Also mentions mitigation M1028 and campaign C0001.
        Data source DS0017.
        Attack tag: attack.t1055 and attack.defense-evasion.
        Malformed: T123, TA123, G123, S123, M123, C123, DS123
      `;
      const ids = service.extractMitreIdsFromContent(content);

      // With corrected regex, malformed IDs should be excluded
      expect(ids).toHaveLength(10);
      expect(ids).toEqual(
        expect.arrayContaining([
          'T1003',
          'T1003.001',
          'TA0006',
          'G0016',
          'S0154',
          'M1028',
          'C0001',
          'DS0017',
          'T1055',
          'defense-evasion',
        ]),
      );
    });

    it('should handle content with no MITRE IDs', () => {
      const content = 'This is a normal sentence without any specific IDs.';
      expect(service.extractMitreIdsFromContent(content)).toEqual([]);
    });
  });

  describe('extractStructuredMitreIds', () => {
    it('should return empty array for invalid ruleData', () => {
      // @ts-expect-error testing invalid parameters
      expect(service['extractStructuredMitreIds'](null)).toEqual([]);
      // @ts-expect-error testing invalid parameters
      expect(service['extractStructuredMitreIds'](undefined)).toEqual([]);
    });
    it('should extract MITRE IDs from tags, tactics, and relevantTechniques', () => {
      const ruleData: CommonRuleData = {
        tags: ['attack.t1059', 'attack.privilege-escalation', 'other.tag'],
        tactics: ['TA0004', 'Discovery'],
        relevantTechniques: ['T1087', 'T1087.001'],
      };
      // Accessing private method for testing
      const ids = service['extractStructuredMitreIds'](ruleData);
      expect(ids).toHaveLength(6);
      expect(ids).toEqual(
        expect.arrayContaining(
          [
            'T1059',
            'privilege-escalation',
            'TA0004',
            'Discovery',
            'T1087',
            'T1087.001',
          ].map((id) =>
            ['privilege-escalation', 'Discovery'].includes(id)
              ? id
              : id.toUpperCase(),
          ),
        ),
      );
    });

    it('should handle missing fields in ruleData', () => {
      const ruleData: CommonRuleData = { tags: ['attack.t1003'] };
      const ids = service['extractStructuredMitreIds'](ruleData);
      expect(ids).toEqual(['T1003']);
    });
  });

  describe('processAndEnrichMitreData', () => {
    const mockFileName = 'test.yml';
    let metadata: RuleMetadata;

    beforeEach(() => {
      metadata = {
        source: 'file_import',
        imported_at: new Date().toISOString(),
        original_filename: mockFileName,
      };
    });

    it('should use existingMitreAttack if provided', async () => {
      const existing: MitreAttackObject[] = [
        {
          id: 'obj1',
          mitre_id: 'T0001',
          name: 'Test1',
          type: MitreAttackObjectType.TECHNIQUE,
        },
      ];
      const ruleData: CommonRuleData = { title: 'Test' };
      const content = 'T1234'; // This should be ignored

      await service.processAndEnrichMitreData(
        ruleData,
        metadata,
        content,
        mockFileName,
        existing,
      );
      expect(metadata.mitre_attack).toEqual(existing);
      expect(
        mockMitreSearchService.getObjectByUniqueMitreId,
      ).not.toHaveBeenCalled();
    });

    it('should fetch and combine MITRE objects from content, description and structured data', async () => {
      const ruleData: CommonRuleData = {
        title: 'Complex Rule',
        description: 'This rule targets S0001.',
        tags: ['attack.t1003'],
        tactics: ['TA0005'],
      };
      const content = 'Also mentions G0002.';
      const mockT1003: MitreAttackObject = {
        id: 't1003',
        mitre_id: 'T1003',
        name: 'OS Credential Dumping',
        type: MitreAttackObjectType.TECHNIQUE,
      };
      const mockTA0005: MitreAttackObject = {
        id: 'ta0005',
        mitre_id: 'TA0005',
        name: 'Defense Evasion',
        type: MitreAttackObjectType.TACTIC,
      };
      const mockS0001: MitreAttackObject = {
        id: 's0001',
        mitre_id: 'S0001',
        name: 'Malware1',
        type: MitreAttackObjectType.MALWARE,
        parent_name: 'Parent S0001',
      };
      const mockG0002: MitreAttackObject = {
        id: 'g0002',
        mitre_id: 'G0002',
        name: 'Group2',
        type: MitreAttackObjectType.GROUP,
      };

      mockMitreSearchService.getObjectByUniqueMitreId
        .mockResolvedValueOnce(mockT1003) // For T1003 from tags
        .mockResolvedValueOnce(mockTA0005) // For TA0005 from tactics
        .mockResolvedValueOnce(mockS0001) // For S0001 from description
        .mockResolvedValueOnce(mockG0002); // For G0002 from content

      await service.processAndEnrichMitreData(
        ruleData,
        metadata,
        content,
        mockFileName,
      );

      expect(metadata.mitre_attack).toBeDefined();
      expect(metadata.mitre_attack).toHaveLength(4);
      expect(metadata.mitre_attack).toEqual(
        expect.arrayContaining([mockT1003, mockTA0005, mockS0001, mockG0002]),
      );

      expect(
        mockMitreSearchService.getObjectByUniqueMitreId,
      ).toHaveBeenCalledTimes(4);
    });

    it('should handle textual identifiers (e.g., defense-evasion)', async () => {
      const ruleData: CommonRuleData = { tags: ['attack.defense-evasion'] };
      const content = '';
      const mockDefenseEvasion: MitreAttackObject = {
        id: 'de',
        mitre_id: 'TA0005',
        name: 'Defense Evasion',
        type: MitreAttackObjectType.TACTIC,
      };
      mockMitreSearchService.getObjectByUniqueName.mockResolvedValueOnce(
        mockDefenseEvasion,
      );

      await service.processAndEnrichMitreData(
        ruleData,
        metadata,
        content,
        mockFileName,
      );
      expect(metadata.mitre_attack).toEqual([mockDefenseEvasion]);
      expect(mockMitreSearchService.getObjectByUniqueName).toHaveBeenCalledWith(
        'defense evasion',
        ['id', 'mitre_id', 'name', 'parent_name', 'type'],
      );
    });

    it('should use searchMitreData as fallback', async () => {
      const ruleData: CommonRuleData = { tags: ['attack.xyz123'] }; // Not a STIX ID, not a textual identifier
      const content = '';
      const mockSearchResult: MitreAttackObject = {
        id: 'search',
        mitre_id: 'X1234',
        name: 'Some Term',
        type: MitreAttackObjectType.TECHNIQUE,
      };

      // Mock both getObjectByUniqueMitreId and getObjectByUniqueName to return null
      // so it falls back to searchMitreData
      mockMitreSearchService.getObjectByUniqueMitreId.mockResolvedValueOnce(
        null,
      );
      mockMitreSearchService.getObjectByUniqueName.mockResolvedValueOnce(null);
      mockMitreSearchService.searchMitreData.mockResolvedValueOnce([
        mockSearchResult,
      ]);

      await service.processAndEnrichMitreData(
        ruleData,
        metadata,
        content,
        mockFileName,
      );
      expect(metadata.mitre_attack).toEqual([mockSearchResult]);
      expect(mockMitreSearchService.searchMitreData).toHaveBeenCalledWith({
        q: 'xyz123',
        fields: ['id', 'mitre_id', 'name', 'parent_name', 'type'],
      });
    });

    it('should handle cases where mitreSearchService returns null or empty arrays', async () => {
      const ruleData: CommonRuleData = { tags: ['attack.t9999'] }; // Assume this won't be found
      const content = '';
      mockMitreSearchService.getObjectByUniqueMitreId.mockResolvedValueOnce(
        null,
      );

      await service.processAndEnrichMitreData(
        ruleData,
        metadata,
        content,
        mockFileName,
      );
      expect(metadata.mitre_attack).toEqual([]);
    });

    it('should handle errors from mitreSearchService gracefully', async () => {
      const ruleData: CommonRuleData = { tags: ['attack.t1234'] };
      const content = 'valid content';

      // Mock the service to throw an error
      mockMitreSearchService.getObjectByUniqueMitreId.mockRejectedValueOnce(
        new Error('Mitre Service Down'),
      );

      // The service should handle the error gracefully and not crash
      await expect(
        service.processAndEnrichMitreData(
          ruleData,
          metadata,
          content,
          mockFileName,
        ),
      ).resolves.not.toThrow();

      // Should still set mitre_attack to empty array when errors occur
      expect(metadata.mitre_attack).toEqual([]);
    });

    it('should do nothing if invalid parameters provided', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error');

      // Test with missing required properties in ruleData (triggers validation)
      await service.processAndEnrichMitreData(
        {} as CommonRuleData,
        metadata,
        'content',
        '',
      );
      expect(loggerSpy).toHaveBeenCalledWith(
        'Invalid parameters provided to processAndEnrichMitreData',
      );

      // Test with missing required properties in metadata (triggers validation)
      await service.processAndEnrichMitreData(
        { title: 'test' } as CommonRuleData,
        {} as RuleMetadata,
        'content',
        '',
      );
      expect(loggerSpy).toHaveBeenCalledWith(
        'Invalid parameters provided to processAndEnrichMitreData',
      );
    });
  });

  describe('normalizeTacticName', () => {
    it('should normalize common MITRE tactic names', () => {
      // Test explicit mappings
      expect(service['normalizeTacticName']('CommandAndControl')).toBe(
        'Command and Control',
      );
      expect(service['normalizeTacticName']('PrivilegeEscalation')).toBe(
        'Privilege Escalation',
      );
      expect(service['normalizeTacticName']('DefenseEvasion')).toBe(
        'Defense Evasion',
      );
      expect(service['normalizeTacticName']('CredentialAccess')).toBe(
        'Credential Access',
      );
      expect(service['normalizeTacticName']('InitialAccess')).toBe(
        'Initial Access',
      );
      expect(service['normalizeTacticName']('LateralMovement')).toBe(
        'Lateral Movement',
      );
      expect(service['normalizeTacticName']('ResourceDevelopment')).toBe(
        'Resource Development',
      );
    });

    it('should handle camelCase to spaced conversion for unknown tactics', () => {
      expect(service['normalizeTacticName']('SomeNewTactic')).toBe(
        'Some New Tactic',
      );
      expect(service['normalizeTacticName']('anotherTestCase')).toBe(
        'another Test Case',
      );
    });

    it('should return original string if no transformation is needed', () => {
      expect(service['normalizeTacticName']('Discovery')).toBe('Discovery');
      expect(service['normalizeTacticName']('Collection')).toBe('Collection');
      expect(service['normalizeTacticName']('already spaced')).toBe(
        'already spaced',
      );
    });

    it('should handle edge cases', () => {
      expect(service['normalizeTacticName']('')).toBe('');
      expect(service['normalizeTacticName']('A')).toBe('A');
      expect(service['normalizeTacticName']('ABC')).toBe('ABC');
      // @ts-expect-error testing invalid input
      expect(service['normalizeTacticName'](null)).toBe(null);
      // @ts-expect-error testing invalid input
      expect(service['normalizeTacticName'](undefined)).toBe(undefined);
    });
  });
});
