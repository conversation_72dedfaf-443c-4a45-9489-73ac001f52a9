import { Injectable } from '@nestjs/common';
import { ParseRuleResult } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';
import { toStrictRuleMetadata } from '../../rules/models/rule-metadata.model';
import {
  RuleParsingErrorType,
  YamlParsingError,
  RuleValidationError,
  RuleContentError,
} from '../parsing.errors';

@Injectable()
export class ErrorHandlingService {
  /**
   * Create standardized error result for parsing failures
   */
  createErrorResult(
    error: unknown,
    content: string,
    fileName: string,
    ruleType: RuleType,
  ): ParseRuleResult {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    let errorType = RuleParsingErrorType.UNKNOWN;
    let errorDescription = `Failed to parse ${RuleType[ruleType] || 'Unknown'} rule from ${fileName}`;

    if (error instanceof YamlParsingError) {
      errorType = RuleParsingErrorType.YAML_PARSING;
      errorDescription = `YAML parsing error in ${RuleType[ruleType] || 'Unknown'} rule from ${fileName}`;
    } else if (error instanceof RuleValidationError) {
      errorType = RuleParsingErrorType.RULE_VALIDATION;
      errorDescription = `Validation error in ${RuleType[ruleType] || 'Unknown'} rule from ${fileName}`;
    } else if (error instanceof RuleContentError) {
      errorType = RuleParsingErrorType.RULE_CONTENT;
      errorDescription = `Content error in ${RuleType[ruleType] || 'Unknown'} rule from ${fileName}`;
    }

    return {
      title: `Failed ${RuleType[ruleType] || 'Unknown'} Rule from ${fileName}`,
      description: errorDescription,
      rule_type: ruleType,
      content: content,
      tags: ['failed_import', ruleType.toString().toLowerCase(), errorType],
      metadata: toStrictRuleMetadata({
        error_type: errorType,
        error_message: errorMessage,
        source: 'file_import',
        imported_at: new Date().toISOString(),
        original_filename: fileName,
      }),
      test_cases: [],
      success: false,
      error: errorMessage,
    };
  }

  /**
   * Create error result for generic/unexpected errors
   */
  createGenericErrorResult(
    error: unknown,
    content: string,
    fileName: string,
    ruleType: RuleType,
  ): ParseRuleResult {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';

    return {
      title: '',
      description: '',
      rule_type: ruleType,
      content: content,
      tags: [],
      metadata: toStrictRuleMetadata({
        error_type: 'GenericParsingError',
        error_details: errorMessage,
        file_name: fileName,
      }),
      test_cases: [],
      success: false,
      error: `Unexpected error during parsing: ${errorMessage}`,
    };
  }
}
