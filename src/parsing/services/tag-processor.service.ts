import { Injectable, Logger } from '@nestjs/common';
import { CommonRuleData } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';

@Injectable()
export class TagProcessor {
  private readonly logger = new Logger(TagProcessor.name);

  /**
   * Extract and normalize tags from rule data based on rule type
   */
  extractTags(
    ruleData: CommonRuleData,
    ruleType: RuleType,
    fileName: string,
  ): string[] {
    if (!ruleData || !ruleType || !fileName) {
      this.logger.warn('Invalid parameters provided to extractTags');
      return [];
    }

    let tags: string[] = [];

    if (ruleData.tags) {
      if (typeof ruleData.tags === 'string') {
        tags = [ruleData.tags];
      } else if (Array.isArray(ruleData.tags)) {
        tags = ruleData.tags.filter(
          (tag): tag is string => typeof tag === 'string',
        );
      } else if (
        ruleType === RuleType.KQL &&
        typeof ruleData.tags === 'object' &&
        !Array.isArray(ruleData.tags)
      ) {
        tags = Object.entries(ruleData.tags).map(([key, value]) =>
          typeof value === 'string' ? `${key}:${value}` : key,
        );
      } else {
        this.logger.warn(
          `Unexpected tags format in ${RuleType[ruleType]} rule ${fileName}`,
        );
      }
    }

    this.enrichTagsWithRuleSpecificData(tags, ruleData, ruleType);

    return tags;
  }

  /**
   * Enrich tags with rule-type specific data (tactics, techniques)
   */
  private enrichTagsWithRuleSpecificData(
    tags: string[],
    ruleData: CommonRuleData,
    ruleType: RuleType,
  ): void {
    if (!tags || !Array.isArray(tags) || !ruleData || !ruleType) {
      this.logger.warn(
        'Invalid parameters provided to enrichTagsWithRuleSpecificData',
      );
      return;
    }

    if (ruleData.tactics && Array.isArray(ruleData.tactics)) {
      const tacticPrefix = ruleType === RuleType.KQL ? 'tactic:' : '';
      const validTactics = ruleData.tactics.filter(
        (tactic): tactic is string =>
          typeof tactic === 'string' && tactic.trim().length > 0,
      );
      tags.push(...validTactics.map((tactic) => `${tacticPrefix}${tactic}`));
    }

    if (
      ruleData.relevantTechniques &&
      Array.isArray(ruleData.relevantTechniques)
    ) {
      const techniquePrefix = ruleType === RuleType.KQL ? 'technique:' : '';
      tags.push(
        ...ruleData.relevantTechniques
          .filter(
            (tech): tech is string =>
              typeof tech === 'string' && tech.trim().length > 0,
          )
          .map((technique) => `${techniquePrefix}${technique}`),
      );
    }
  }
}
