import { Injectable, Logger } from '@nestjs/common';
import { MitreSearchService } from '../../mitre/services/mitre-search.service';
import {
  MitreAttackObject,
  RuleMetadata,
} from '../../rules/models/rule-metadata.model';
import { CommonRuleData } from '../parsing.types';
import {
  MITRE_API_IDENTIFIER_REGEX,
  MITRE_TEXTUAL_IDENTIFIER_REGEX,
  MITRE_CONTENT_EXTRACTION_REGEX,
  ATTACK_TECHNIQUE_TAG_REGEX,
  ATTACK_TACTIC_TAG_REGEX,
} from '../../common/constants/stix-mitre-regexes';

@Injectable()
export class MitreDataProcessor {
  private readonly logger = new Logger(MitreDataProcessor.name);

  constructor(private readonly mitreSearchService: MitreSearchService) {}

  /**
   * Normalize tactic names from camelCase/PascalCase to spaced format
   * e.g., "CommandAndControl" -> "Command and Control"
   */
  private normalizeTacticName(tacticName: string): string {
    if (!tacticName || typeof tacticName !== 'string') {
      return tacticName;
    }

    // Handle common MITRE tactic name variations
    const tacticMappings: Record<string, string> = {
      CommandAndControl: 'Command and Control',
      PrivilegeEscalation: 'Privilege Escalation',
      DefenseEvasion: 'Defense Evasion',
      CredentialAccess: 'Credential Access',
      InitialAccess: 'Initial Access',
      LateralMovement: 'Lateral Movement',
      ResourceDevelopment: 'Resource Development',
    };

    // Check explicit mappings first
    if (tacticMappings[tacticName]) {
      return tacticMappings[tacticName];
    }

    // If no explicit mapping, try camelCase to spaced conversion
    // Insert space before uppercase letters (except the first)
    const spacedName = tacticName.replace(/([a-z])([A-Z])/g, '$1 $2');

    // Only return the spaced version if it's different from the original
    return spacedName !== tacticName ? spacedName : tacticName;
  }

  /**
   * Extract MITRE IDs from rule content using regex patterns
   */
  extractMitreIdsFromContent(content: string): string[] {
    if (!content || typeof content !== 'string') {
      this.logger.warn('Invalid content provided for MITRE ID extraction');
      return [];
    }

    const mitreIds = new Set<string>();

    let match: RegExpExecArray | null;

    // Reset regex lastIndex to ensure fresh matching
    MITRE_CONTENT_EXTRACTION_REGEX.lastIndex = 0;
    while ((match = MITRE_CONTENT_EXTRACTION_REGEX.exec(content)) !== null) {
      if (match[1]) {
        mitreIds.add(match[1].toUpperCase());
      }
    }

    ATTACK_TECHNIQUE_TAG_REGEX.lastIndex = 0;
    while ((match = ATTACK_TECHNIQUE_TAG_REGEX.exec(content)) !== null) {
      if (match[1]) {
        mitreIds.add(match[1].toUpperCase());
      }
    }

    ATTACK_TACTIC_TAG_REGEX.lastIndex = 0;
    while ((match = ATTACK_TACTIC_TAG_REGEX.exec(content)) !== null) {
      if (match[1] && !match[1].startsWith('t')) {
        mitreIds.add(match[1].toLowerCase());
      }
    }

    this.logger.debug(
      `Extracted MITRE IDs from content: ${Array.from(mitreIds).join(', ')}`,
    );
    return Array.from(mitreIds);
  }

  /**
   * Process and enrich metadata with MITRE data
   */
  async processAndEnrichMitreData(
    ruleData: CommonRuleData,
    metadata: RuleMetadata,
    content: string,
    fileName: string,
    existingMitreAttack?: MitreAttackObject[],
  ): Promise<void> {
    if (!ruleData || !metadata || !fileName) {
      this.logger.error(
        'Invalid parameters provided to processAndEnrichMitreData',
      );
      return;
    }

    const structuredMitreIds = this.extractStructuredMitreIds(ruleData);
    const contentMitreIds = this.extractMitreIdsFromContent(content);
    let descriptionMitreIds: string[] = [];

    if (ruleData.description && typeof ruleData.description === 'string') {
      descriptionMitreIds = this.extractMitreIdsFromContent(
        ruleData.description,
      );
    }

    const allMitreIdentifiers = [
      ...new Set([
        ...structuredMitreIds,
        ...contentMitreIds,
        ...descriptionMitreIds,
      ]),
    ];

    await this.enrichMetadataWithMitreData(
      allMitreIdentifiers,
      metadata,
      fileName,
      existingMitreAttack,
    );
  }

  /**
   * Enrich metadata with MITRE attack objects by looking up MITRE IDs
   */
  async enrichMetadataWithMitreData(
    mitreIds: string[],
    metadata: RuleMetadata,
    fileName: string,
    userSuppliedMitreAttack?: MitreAttackObject[],
  ): Promise<void> {
    if (userSuppliedMitreAttack !== undefined) {
      metadata.mitre_attack = this.uniqueMitreAttackObjects(
        userSuppliedMitreAttack,
      );
      this.logger.debug(
        `Using user-supplied mitre_attack exclusively for ${fileName}. Count: ${metadata.mitre_attack.length}`,
      );
      return;
    }

    if (mitreIds.length === 0) {
      metadata.mitre_attack = metadata.mitre_attack || [];
      return;
    }

    let fetchedMitreObjects: MitreAttackObject[] = [];
    try {
      const processingPromises = mitreIds.map(async (identifier) => {
        let resultObject: MitreAttackObject | null = null;
        const requestedFields = [
          'id',
          'mitre_id',
          'name',
          'parent_name',
          'type',
        ];

        if (MITRE_API_IDENTIFIER_REGEX.test(identifier)) {
          resultObject = await this.mitreSearchService.getObjectByUniqueMitreId(
            identifier.toUpperCase(),
            requestedFields,
          );
        } else if (this.isTextualIdentifier(identifier)) {
          const normalizedName = this.normalizeTextualIdentifier(identifier);
          resultObject = await this.mitreSearchService.getObjectByUniqueName(
            normalizedName,
            requestedFields,
          );

          // If not found with standard normalization, try tactic name normalization
          if (!resultObject) {
            const tacticNormalizedName = this.normalizeTacticName(identifier);
            if (tacticNormalizedName !== identifier) {
              this.logger.debug(
                `Trying tactic normalization: "${identifier}" -> "${tacticNormalizedName}"`,
              );
              resultObject =
                await this.mitreSearchService.getObjectByUniqueName(
                  tacticNormalizedName,
                  requestedFields,
                );
            }
          }
        } else {
          const searchResults = await this.mitreSearchService.searchMitreData({
            q: identifier,
            fields: requestedFields,
          });
          if (searchResults && searchResults.length > 0) {
            resultObject = searchResults[0];
          }
        }
        return resultObject;
      });

      const fetchedMitreObjectsOrNulls = await Promise.all(processingPromises);
      fetchedMitreObjects = fetchedMitreObjectsOrNulls.filter(
        (obj): obj is MitreAttackObject => obj !== null,
      );
    } catch (mitreError: unknown) {
      const errorMessage =
        mitreError instanceof Error ? mitreError.message : 'Unknown error';
      this.logger.warn(
        `Failed to fetch some MITRE ATT&CK data for ${fileName} during parsing: ${errorMessage}`,
      );
    }

    const existingMitreAttack = metadata.mitre_attack || [];
    const allMitreObjects = [...existingMitreAttack, ...fetchedMitreObjects];

    metadata.mitre_attack = this.uniqueMitreAttackObjects(allMitreObjects);

    this.logger.debug(
      `Enriched mitre_attack for ${fileName} using three-tier lookup. Fetched: ${fetchedMitreObjects.length}, Total unique: ${metadata.mitre_attack.length}`,
    );
  }

  private extractStructuredMitreIds(ruleData: CommonRuleData): string[] {
    if (!ruleData) {
      this.logger.warn(
        'No rule data provided for structured MITRE ID extraction',
      );
      return [];
    }

    const structuredMitreIds: string[] = [];

    if (ruleData.tags && Array.isArray(ruleData.tags)) {
      ruleData.tags.forEach((tag: any) => {
        if (typeof tag === 'string') {
          if (tag.startsWith('attack.t')) {
            structuredMitreIds.push(tag.replace('attack.', '').toUpperCase());
          } else if (tag.startsWith('attack.')) {
            structuredMitreIds.push(tag.replace('attack.', ''));
          }
        }
      });
    }

    if (
      ruleData.relevantTechniques &&
      Array.isArray(ruleData.relevantTechniques)
    ) {
      structuredMitreIds.push(
        ...ruleData.relevantTechniques.map((id) => String(id).toUpperCase()),
      );
    }

    if (ruleData.tactics && Array.isArray(ruleData.tactics)) {
      structuredMitreIds.push(...ruleData.tactics.map((id) => String(id)));
    }

    return [...new Set(structuredMitreIds)];
  }

  private isTextualIdentifier(identifier: string): boolean {
    return MITRE_TEXTUAL_IDENTIFIER_REGEX.test(identifier);
  }

  private normalizeTextualIdentifier(identifier: string): string {
    return identifier.replace(/-/g, ' ');
  }

  private uniqueMitreAttackObjects(
    objects: (MitreAttackObject | null)[],
  ): MitreAttackObject[] {
    const seen = new Set<string>();
    return objects.filter((obj): obj is MitreAttackObject => {
      if (!obj || !obj.mitre_id) return false;
      if (seen.has(obj.mitre_id)) {
        return false;
      }
      seen.add(obj.mitre_id);
      return true;
    });
  }
}
