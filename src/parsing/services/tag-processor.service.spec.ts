import { Test, TestingModule } from '@nestjs/testing';
import { TagProcessor } from './tag-processor.service';
import { CommonRuleData } from '../parsing.types';
import { RuleType } from '../../rules/models/rule.model';

describe('TagProcessor', () => {
  let service: TagProcessor;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TagProcessor],
    }).compile();
    service = module.get<TagProcessor>(TagProcessor);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('extractTags', () => {
    const mockFileName = 'test-rule.yml';

    it('should return empty array for invalid parameters', () => {
      // @ts-expect-error testing invalid parameters
      expect(service.extractTags(null, RuleType.SIGMA, mockFileName)).toEqual(
        [],
      );
      // @ts-expect-error testing invalid parameters
      expect(service.extractTags({}, null, mockFileName)).toEqual([]);
      // @ts-expect-error testing invalid parameters
      expect(service.extractTags({}, RuleType.SIGMA, null)).toEqual([]);
    });

    it('should extract tags from a string', () => {
      const ruleData: CommonRuleData = { tags: 'tag1' };
      const tags = service.extractTags(ruleData, RuleType.SIGMA, mockFileName);
      expect(tags).toEqual(['tag1']);
    });

    it('should extract tags from an array of strings', () => {
      const ruleData: CommonRuleData = { tags: ['tag1', 'tag2'] };
      const tags = service.extractTags(ruleData, RuleType.SIGMA, mockFileName);
      expect(tags).toEqual(['tag1', 'tag2']);
    });

    it('should filter out non-string tags from an array', () => {
      const ruleData: CommonRuleData = { tags: ['tag1', 123, 'tag2'] as any };
      const tags = service.extractTags(ruleData, RuleType.SIGMA, mockFileName);
      expect(tags).toEqual(['tag1', 'tag2']);
    });

    it('should extract KQL tags from an object', () => {
      const ruleData: CommonRuleData = {
        tags: { category: 'Initial Access', custom: 'value' },
      };
      const tags = service.extractTags(ruleData, RuleType.KQL, mockFileName);
      expect(tags).toContain('category:Initial Access');
      expect(tags).toContain('custom:value');
    });

    it('should handle KQL tags object with non-string values', () => {
      const ruleData: CommonRuleData = {
        tags: { count: 5, valid: 'tag' },
      };
      const tags = service.extractTags(ruleData, RuleType.KQL, mockFileName);
      expect(tags).toContain('count'); // Key becomes the tag if value is not string
      expect(tags).toContain('valid:tag');
    });

    it('should log warning for unexpected tags format and return empty', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');
      const ruleData: CommonRuleData = { tags: 12345 as any }; // Invalid format
      const tags = service.extractTags(ruleData, RuleType.SIGMA, mockFileName);
      expect(tags).toEqual([]);
      expect(loggerSpy).toHaveBeenCalledWith(
        `Unexpected tags format in SIGMA rule ${mockFileName}`,
      );
    });

    it('should return empty array if ruleData.tags is undefined', () => {
      const ruleData: CommonRuleData = {};
      const tags = service.extractTags(ruleData, RuleType.SIGMA, mockFileName);
      expect(tags).toEqual([]);
    });

    it('should enrich tags with tactics and techniques for SIGMA', () => {
      const ruleData: CommonRuleData = {
        tags: ['base.tag'],
        tactics: ['TA0001', 'TA0002'],
        relevantTechniques: ['T1001', 'T1002'],
      };
      const tags = service.extractTags(ruleData, RuleType.SIGMA, mockFileName);
      expect(tags).toEqual(['base.tag', 'TA0001', 'TA0002', 'T1001', 'T1002']);
    });

    it('should enrich tags with prefixed tactics and techniques for KQL', () => {
      const ruleData: CommonRuleData = {
        tags: { kql: 'base' },
        tactics: ['InitialAccess'],
        relevantTechniques: ['T1078'],
      };
      const tags = service.extractTags(ruleData, RuleType.KQL, mockFileName);
      expect(tags).toEqual([
        'kql:base',
        'tactic:InitialAccess',
        'technique:T1078',
      ]);
    });

    it('should handle empty or undefined tactics/techniques arrays gracefully', () => {
      const ruleData1: CommonRuleData = {
        tags: ['tag1'],
        tactics: [],
        relevantTechniques: undefined,
      };
      const tags1 = service.extractTags(
        ruleData1,
        RuleType.SIGMA,
        mockFileName,
      );
      expect(tags1).toEqual(['tag1']);

      const ruleData2: CommonRuleData = {
        tags: ['tag2'],
      };
      const tags2 = service.extractTags(ruleData2, RuleType.KQL, mockFileName);
      expect(tags2).toEqual(['tag2']);
    });

    it('should filter out empty or whitespace-only tactics and techniques', () => {
      const ruleData: CommonRuleData = {
        tags: ['base.tag'],
        tactics: ['TA0001', ' ', 'TA0002'],
        relevantTechniques: ['T1001', '', 'T1002'],
      };
      const tags = service.extractTags(ruleData, RuleType.SIGMA, mockFileName);
      expect(tags).toEqual(['base.tag', 'TA0001', 'TA0002', 'T1001', 'T1002']);
    });
  });
});
