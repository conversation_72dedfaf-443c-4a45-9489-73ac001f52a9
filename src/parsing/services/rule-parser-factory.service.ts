import { Injectable } from '@nestjs/common';
import { IRuleParser } from '../ruleparsers/rule-parser.interface';
import { SigmaRuleParser } from '../ruleparsers/sigma-rule-parser.service';
import { KqlRuleParser } from '../ruleparsers/kql-rule-parser.service';
import { SplRuleParser } from '../ruleparsers/spl-rule-parser.service';
import { GenericRuleParser } from '../ruleparsers/generic-rule-parser.service';
import { RuleType } from '../../rules/models/rule.model';

/**
 * Factory service for creating appropriate rule parsers based on rule type.
 *
 * Directly supported rule types:
 * - SIGMA: Full YAML parsing with detection logic validation
 * - KQL: YAML-wrapped or raw KQL query parsing
 * - SPL: Delegates to KQL parser and adjusts rule type
 *
 * Generically supported rule types (basic MITRE extraction only):
 * - CARBONBLACK, CORTEXQL, DATADOG, ELASTIC_LUCENE, NETWITNESS,
 *   LEQL, QRADAR_AQL, S1QL, UNKNOWN, and any future rule types
 */
@Injectable()
export class RuleParserFactory {
  constructor(
    private readonly sigmaRuleParser: SigmaRuleParser,
    private readonly kqlRuleParser: KqlRuleParser,
    private readonly splRuleParser: SplRuleParser,
    private readonly genericRuleParser: GenericRuleParser,
  ) {}

  /**
   * Get the appropriate parser for the given rule type
   * @param ruleType The type of rule to parse
   * @returns IRuleParser instance for the specified rule type
   */
  getParser(ruleType: RuleType): IRuleParser {
    switch (ruleType) {
      case RuleType.SIGMA:
        return this.sigmaRuleParser;
      case RuleType.KQL:
        return this.kqlRuleParser;
      case RuleType.SPL:
        return this.splRuleParser;
      default:
        // For unknown or unsupported types, create a parser that delegates to generic parser
        return {
          parseRule: async (content, fileName, existingMitreAttack) => {
            return this.genericRuleParser.parseRuleWithType(
              content,
              fileName,
              existingMitreAttack,
              ruleType,
            );
          },
        };
    }
  }
}
