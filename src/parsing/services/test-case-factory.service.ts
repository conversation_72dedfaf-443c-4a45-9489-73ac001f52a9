import { Injectable } from '@nestjs/common';
import { TestCase, RuleType } from '../../rules/models/rule.model';

@Injectable()
export class TestCaseFactory {
  /**
   * Create default test case based on rule type
   */
  createDefaultTestCase(ruleType: RuleType): TestCase {
    const testCases: Record<RuleType, TestCase> = {
      [RuleType.SIGMA]: {
        input: { event: { type: 'test' } },
        expected_output: true,
        description: 'Default Sigma test case',
      },
      [RuleType.KQL]: {
        input: { log: { message: 'test' } },
        expected_output: true,
        description: 'Default KQL test case',
      },
      [RuleType.SPL]: {
        input: { index: 'test', search: 'test' },
        expected_output: true,
        description: 'Default SPL test case',
      },
      [RuleType.CARBONBLACK]: {
        input: { process_name: 'test.exe' },
        expected_output: true,
        description: 'Default Carbon Black test case',
      },
      [RuleType.CORTEXQL]: {
        input: { dataset: 'test', query: 'test' },
        expected_output: true,
        description: 'Default Cortex XQL test case',
      },
      [RuleType.DATADOG]: {
        input: { service: 'test', log: 'test' },
        expected_output: true,
        description: 'Default Datadog test case',
      },
      [RuleType.ELASTIC_LUCENE]: {
        input: { index: 'test', query: 'test' },
        expected_output: true,
        description: 'Default Elastic Lucene test case',
      },
      [RuleType.NETWITNESS]: {
        input: { meta: 'test', query: 'test' },
        expected_output: true,
        description: 'Default NetWitness test case',
      },
      [RuleType.LEQL]: {
        input: { log: 'test', query: 'test' },
        expected_output: true,
        description: 'Default LEQL test case',
      },
      [RuleType.QRADAR_AQL]: {
        input: { events: 'test', query: 'test' },
        expected_output: true,
        description: 'Default QRadar AQL test case',
      },
      [RuleType.S1QL]: {
        input: { endpoint: 'test', query: 'test' },
        expected_output: true,
        description: 'Default S1QL test case',
      },
      [RuleType.UNKNOWN]: {
        input: { data: 'test' },
        expected_output: false,
        description: 'Default unknown rule type test case',
      },
    };

    return testCases[ruleType];
  }
}
