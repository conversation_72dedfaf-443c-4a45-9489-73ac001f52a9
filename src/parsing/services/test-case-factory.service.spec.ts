import { Test, TestingModule } from '@nestjs/testing';
import { TestCaseFactory } from './test-case-factory.service';
import { RuleType } from '../../rules/models/rule.model';

describe('TestCaseFactory', () => {
  let service: TestCaseFactory;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TestCaseFactory],
    }).compile();

    service = module.get<TestCaseFactory>(TestCaseFactory);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createDefaultTestCase', () => {
    // Mapping of RuleType to expected human-readable description
    const expectedDescriptions: Record<RuleType, string> = {
      [RuleType.SIGMA]: 'Default Sigma test case',
      [RuleType.KQL]: 'Default KQL test case',
      [RuleType.SPL]: 'Default SPL test case',
      [RuleType.CARBONBLACK]: 'Default Carbon Black test case',
      [RuleType.CORTEXQL]: 'Default Cortex XQL test case',
      [RuleType.DATADOG]: 'Default Datadog test case',
      [RuleType.ELASTIC_LUCENE]: 'Default Elastic Lucene test case',
      [RuleType.NETWITNESS]: 'Default NetWitness test case',
      [RuleType.LEQL]: 'Default LEQL test case',
      [RuleType.QRADAR_AQL]: 'Default QRadar AQL test case',
      [RuleType.S1QL]: 'Default S1QL test case',
      [RuleType.UNKNOWN]: 'Default unknown rule type test case',
    };

    // Test for each RuleType to ensure it returns a valid TestCase object
    Object.values(RuleType).forEach((ruleType) => {
      it(`should create a default test case for ${ruleType}`, () => {
        const testCase = service.createDefaultTestCase(ruleType);

        expect(testCase).toBeDefined();
        expect(testCase.input).toBeDefined();
        expect(typeof testCase.expected_output).toBe('boolean');
        expect(typeof testCase.description).toBe('string');
        expect(testCase.description).toBe(expectedDescriptions[ruleType]);

        // Add more specific checks if needed based on ruleType
        if (ruleType === RuleType.SIGMA) {
          expect(testCase.input).toEqual({ event: { type: 'test' } });
        } else if (ruleType === RuleType.KQL) {
          expect(testCase.input).toEqual({ log: { message: 'test' } });
        } else if (ruleType === RuleType.UNKNOWN) {
          expect(testCase.expected_output).toBe(false);
        }
      });
    });

    it('should return the specific UNKNOWN test case structure', () => {
      const testCase = service.createDefaultTestCase(RuleType.UNKNOWN);
      expect(testCase).toEqual({
        input: { data: 'test' },
        expected_output: false,
        description: 'Default unknown rule type test case',
      });
    });
  });
});
