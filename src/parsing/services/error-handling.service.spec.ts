import { Test, TestingModule } from '@nestjs/testing';
import { ErrorHandlingService } from './error-handling.service';
import { RuleType } from '../../rules/models/rule.model';
import {
  RuleParsingErrorType,
  YamlParsingError,
  RuleValidationError,
  RuleContentError,
} from '../parsing.errors';

describe('ErrorHandlingService', () => {
  let service: ErrorHandlingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ErrorHandlingService],
    }).compile();

    service = module.get<ErrorHandlingService>(ErrorHandlingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createErrorResult', () => {
    const mockContent = 'test content';
    const mockFileName = 'test.yml';

    it('should create a YamlParsingError result', () => {
      const error = new YamlParsingError('YAML issue', mockFileName);
      const result = service.createErrorResult(
        error,
        mockContent,
        mockFileName,
        RuleType.SIGMA,
      );

      expect(result.success).toBe(false);
      expect(result.rule_type).toBe(RuleType.SIGMA);
      expect(result.error).toBe('YAML parsing error in test.yml: YAML issue');
      expect(result.metadata.error_type).toBeUndefined();
      expect(result.metadata.error_message).toBeUndefined();
      expect(result.tags).toContain(RuleParsingErrorType.YAML_PARSING);
    });

    it('should create a RuleValidationError result', () => {
      const error = new RuleValidationError('Validation issue', mockFileName);
      const result = service.createErrorResult(
        error,
        mockContent,
        mockFileName,
        RuleType.KQL,
      );

      expect(result.success).toBe(false);
      expect(result.rule_type).toBe(RuleType.KQL);
      expect(result.error).toBe(
        'Rule validation error in test.yml: Validation issue',
      );
      expect(result.metadata.error_type).toBeUndefined();
      expect(result.metadata.error_message).toBeUndefined();
      expect(result.tags).toContain(RuleParsingErrorType.RULE_VALIDATION);
    });

    it('should create a RuleContentError result', () => {
      const error = new RuleContentError('Content issue', mockFileName);
      const result = service.createErrorResult(
        error,
        mockContent,
        mockFileName,
        RuleType.SPL,
      );

      expect(result.success).toBe(false);
      expect(result.rule_type).toBe(RuleType.SPL);
      expect(result.error).toBe(
        'Rule content error in test.yml: Content issue',
      );
      expect(result.metadata.error_type).toBeUndefined();
      expect(result.metadata.error_message).toBeUndefined();
      expect(result.tags).toContain(RuleParsingErrorType.RULE_CONTENT);
    });

    it('should create an UNKNOWN error result for generic Error', () => {
      const error = new Error('Generic issue');
      const result = service.createErrorResult(
        error,
        mockContent,
        mockFileName,
        RuleType.UNKNOWN,
      );

      expect(result.success).toBe(false);
      expect(result.rule_type).toBe(RuleType.UNKNOWN);
      expect(result.error).toBe('Generic issue');
      expect(result.metadata.error_type).toBeUndefined();
      expect(result.metadata.error_message).toBeUndefined();
      expect(result.tags).toContain(RuleParsingErrorType.UNKNOWN);
    });

    it('should create an UNKNOWN error result for non-Error type', () => {
      const error = 'Some string error';
      const result = service.createErrorResult(
        error,
        mockContent,
        mockFileName,
        RuleType.SIGMA,
      );

      expect(result.success).toBe(false);
      expect(result.rule_type).toBe(RuleType.SIGMA);
      expect(result.error).toBe('Unknown error');
      expect(result.metadata.error_type).toBeUndefined();
      expect(result.metadata.error_message).toBeUndefined();
      expect(result.tags).toContain(RuleParsingErrorType.UNKNOWN);
    });
  });

  describe('createGenericErrorResult', () => {
    const mockContent = 'generic test content';
    const mockFileName = 'generic.txt';

    it('should create a generic error result for an Error instance', () => {
      const error = new Error('A surprising problem');
      const result = service.createGenericErrorResult(
        error,
        mockContent,
        mockFileName,
        RuleType.UNKNOWN,
      );

      expect(result.success).toBe(false);
      expect(result.rule_type).toBe(RuleType.UNKNOWN);
      expect(result.error).toBe(
        'Unexpected error during parsing: A surprising problem',
      );
      expect(result.metadata.error_type).toBeUndefined();
      expect(result.metadata.error_details).toBeUndefined();
      expect(result.metadata.file_name).toBeUndefined();
      expect(result.content).toBe(mockContent);
      expect(result.tags).toEqual([]);
    });

    it('should create a generic error result for a non-Error type', () => {
      const error = { message: 'An object error' };
      const result = service.createGenericErrorResult(
        error,
        mockContent,
        mockFileName,
        RuleType.SIGMA,
      );

      expect(result.success).toBe(false);
      expect(result.rule_type).toBe(RuleType.SIGMA);
      expect(result.error).toBe(
        'Unexpected error during parsing: Unknown error',
      );
      expect(result.metadata.error_type).toBeUndefined();
      expect(result.metadata.error_details).toBeUndefined();
    });
  });
});
