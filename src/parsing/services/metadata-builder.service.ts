import { Injectable, Logger } from '@nestjs/common';
import { RuleMetadata } from '../../rules/models/rule-metadata.model';
import { CommonRuleData } from '../parsing.types';
import { KqlRuleDto } from '../../validation/dto/kql-rule.dto';
import { RuleType } from '../../rules/models/rule.model';

@Injectable()
export class MetadataBuilder {
  private readonly logger = new Logger(MetadataBuilder.name);

  /**
   * Create base metadata for file import
   */
  createBaseMetadata(fileName: string, platform?: string): RuleMetadata {
    if (!fileName || typeof fileName !== 'string') {
      this.logger.warn('Invalid fileName provided, using fallback');
      fileName = 'unknown_file';
    }

    const metadata: RuleMetadata = {
      source: 'file_import',
      imported_at: new Date().toISOString(),
      original_filename: fileName,
    };

    if (platform && typeof platform === 'string') {
      metadata.platform = platform;
    }

    return metadata;
  }

  /**
   * Populate common metadata fields from rule data
   */
  populateCommonMetadata(
    rawData: CommonRuleData,
    metadata: RuleMetadata,
  ): void {
    if (!rawData || !metadata) {
      this.logger.warn('Invalid parameters provided to populateCommonMetadata');
      return;
    }

    if (rawData.author && typeof rawData.author === 'string') {
      metadata.author = rawData.author;
    }
    if (rawData.status && typeof rawData.status === 'string') {
      metadata.status = rawData.status;
    }
    if (rawData.id && typeof rawData.id === 'string') {
      metadata.original_id = rawData.id;
    }
    if (rawData.references && Array.isArray(rawData.references)) {
      metadata.references = rawData.references.filter(
        (ref): ref is string => typeof ref === 'string',
      );
    }

    const parsedDate = this.safeParseDateToISO(rawData.date);
    if (parsedDate) metadata.date = parsedDate;

    const parsedModifiedDate = this.safeParseDateToISO(rawData.modified);
    if (parsedModifiedDate) metadata.modified = parsedModifiedDate;

    const severityValue = rawData.level || rawData.severity;
    if (severityValue) {
      metadata.severity = severityValue;
      metadata.level = severityValue;
    }
  }

  /**
   * Populate KQL-specific metadata fields
   */
  populateKqlSpecificMetadata(
    ruleData: KqlRuleDto,
    metadata: RuleMetadata,
  ): void {
    if (ruleData.id) metadata.original_id = ruleData.id;
    if (ruleData.version) metadata.original_version = ruleData.version;
    if (ruleData.kind) metadata.kind = ruleData.kind;
    if (ruleData.queryFrequency)
      metadata.queryFrequency = ruleData.queryFrequency;
    if (ruleData.queryPeriod) metadata.queryPeriod = ruleData.queryPeriod;
    if (ruleData.triggerOperator)
      metadata.triggerOperator = ruleData.triggerOperator;
    if (ruleData.triggerThreshold)
      metadata.triggerThreshold = ruleData.triggerThreshold;
    if (ruleData.entityMappings)
      metadata.entityMappings = ruleData.entityMappings;
  }

  /**
   * Extract data sources from rule data based on rule type
   */
  extractDataSources(
    ruleData: CommonRuleData,
    metadata: RuleMetadata,
    ruleType: RuleType,
  ): void {
    if (ruleType === RuleType.SIGMA && ruleData.logsource) {
      const dataSources: string[] = [];
      if (ruleData.logsource.category)
        dataSources.push(ruleData.logsource.category);
      if (ruleData.logsource.product)
        dataSources.push(ruleData.logsource.product);
      if (ruleData.logsource.service)
        dataSources.push(ruleData.logsource.service);

      if (dataSources.length > 0) metadata.data_sources = dataSources;
      metadata.logsource = ruleData.logsource;
    } else if (
      ruleType === RuleType.KQL &&
      ruleData.requiredDataConnectors &&
      Array.isArray(ruleData.requiredDataConnectors)
    ) {
      const dataSources = ruleData.requiredDataConnectors.flatMap(
        (connector) => connector.dataTypes || [],
      );
      if (dataSources.length > 0) metadata.data_sources = dataSources;
      metadata.requiredDataConnectors = ruleData.requiredDataConnectors;
    }
  }

  /**
   * Safely parses a date string and returns an ISO string if valid, otherwise undefined
   */
  private safeParseDateToISO(dateString: any): string | undefined {
    if (dateString instanceof Date) {
      if (!isNaN(dateString.getTime())) {
        return dateString.toISOString();
      }
      return undefined;
    }

    if (typeof dateString !== 'string' || !dateString.trim()) {
      return undefined;
    }

    const standardizedDateString = dateString.replace(/\//g, '-');
    const date = new Date(standardizedDateString);

    if (!isNaN(date.getTime())) {
      const inputYear = parseInt(standardizedDateString.substring(0, 4), 10);
      if (
        date.getFullYear() === inputYear ||
        (date.getFullYear() > 1990 && date.getFullYear() < 2100)
      ) {
        const parts = standardizedDateString.split('-');
        let dateMatches = true;
        if (parts.length > 1) {
          const inputMonth = parseInt(parts[1], 10);
          const parsedMonth = date.getMonth() + 1;
          dateMatches = dateMatches && parsedMonth === inputMonth;
        }
        if (parts.length > 2) {
          const inputDay = parseInt(parts[2], 10);
          const parsedDay = date.getDate();
          dateMatches = dateMatches && parsedDay === inputDay;
        }
        if (dateMatches) {
          return date.toISOString();
        }
      }
    }

    this.logger.warn(
      `[safeParseDateToISO] Could not reliably parse date string: "${String(dateString)}" to ISO format. Returning undefined.`,
    );
    return undefined;
  }
}
