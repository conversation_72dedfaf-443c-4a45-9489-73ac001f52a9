import { Injectable, Logger } from '@nestjs/common';
import { ParseRuleInput, ParseRuleResult } from './parsing.types';
import { RuleType } from '../rules/models/rule.model';
import { RuleParserFactory } from './services/rule-parser-factory.service';
import { ErrorHandlingService } from './services/error-handling.service';
import { IRuleParser } from './ruleparsers/rule-parser.interface';
import {
  YamlParsingError,
  RuleValidationError,
  RuleContentError,
} from './parsing.errors';
import * as yaml from 'js-yaml';

/**
 * Constants for rule type detection
 */
const RULE_TYPE_DETECTION_PATTERNS = {
  SIGMA: {
    YAML_FIELDS: ['detection', 'logsource'] as const,
    CONTENT_PATTERNS: ['detection:', 'logsource:'] as const,
  },
  KQL: {
    YAML_FIELDS: ['query'] as const,
    CONTENT_PATTERNS: ['query:'] as const,
  },
  SPL: {
    YAML_FIELDS: ['search'] as const,
    CONTENT_PATTERNS: ['search:', 'index='] as const,
  },
} as const;

/**
 * Main service for parsing rule files of various types.
 *
 * This service:
 * 1. Auto-detects rule types from content (YAML parsing + pattern matching)
 * 2. Delegates to specialized parsers via RuleParserFactory
 * 3. Handles errors gracefully to ensure parsing always attempts to succeed
 * 4. Supports user-provided MITRE ATT&CK data override
 *
 * The service uses YAML parsing only for rule type detection, not for
 * rule content parsing (which is handled by individual rule parsers).
 */
@Injectable()
export class ParsingService {
  private readonly logger = new Logger(ParsingService.name);

  constructor(
    private readonly ruleParserFactory: RuleParserFactory,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  async parseRule(input: ParseRuleInput): Promise<ParseRuleResult> {
    if (!input || !input.content || !input.fileName) {
      const errorMessage = 'Invalid input provided to parseRule';
      this.logger.error(errorMessage);
      return this.errorHandlingService.createGenericErrorResult(
        new Error(errorMessage),
        input?.content || '',
        input?.fileName || 'unknown',
        input?.ruleType || RuleType.UNKNOWN,
      );
    }

    try {
      const { content, fileName, existingMitreAttack } = input;
      let { ruleType } = input;

      // If ruleType is not provided, attempt to detect it
      if (!ruleType) {
        ruleType = this.detectRuleType(content, fileName);
        this.logger.debug(
          `Auto-detected rule type for ${fileName}: ${ruleType}`,
        );
      }

      this.logger.debug(`Parsing rule ${fileName} with type: ${ruleType}`);

      const parser = this.ruleParserFactory.getParser(ruleType);
      this.validateParser(parser, ruleType, fileName);

      return await parser.parseRule(content, fileName, existingMitreAttack);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to parse rule ${input.fileName} (type: ${input.ruleType || 'auto-detected'}): ${errorMessage}`,
        errorStack,
      );

      const determinedRuleType = input.ruleType || RuleType.UNKNOWN;

      if (
        error instanceof YamlParsingError ||
        error instanceof RuleValidationError ||
        error instanceof RuleContentError
      ) {
        return this.errorHandlingService.createErrorResult(
          error,
          input.content,
          input.fileName,
          determinedRuleType,
        );
      }

      return this.errorHandlingService.createGenericErrorResult(
        error,
        input.content,
        input.fileName,
        determinedRuleType,
      );
    }
  }

  /**
   * Detect rule type from content when not explicitly provided
   * @param content The rule content to analyze
   * @param fileName The filename for logging purposes
   * @returns Detected rule type or UNKNOWN if detection fails
   */
  private detectRuleType(content: string, fileName: string): RuleType {
    try {
      const parsed = yaml.load(content) as Record<string, any>;
      if (parsed) {
        // Check for YAML field-based detection
        if (
          this.hasAnyField(
            parsed,
            RULE_TYPE_DETECTION_PATTERNS.SIGMA.YAML_FIELDS,
          )
        ) {
          return RuleType.SIGMA;
        }
        if (
          this.hasAnyField(parsed, RULE_TYPE_DETECTION_PATTERNS.KQL.YAML_FIELDS)
        ) {
          return RuleType.KQL;
        }
        if (
          this.hasAnyField(parsed, RULE_TYPE_DETECTION_PATTERNS.SPL.YAML_FIELDS)
        ) {
          return RuleType.SPL;
        }
      }

      // Fallback to content-based detection
      return this.detectRuleTypeFromContent(content, fileName);
    } catch (e) {
      // If YAML parsing fails, try content-based detection
      const detectedType = this.detectRuleTypeFromContent(content, fileName);

      if (e instanceof yaml.YAMLException) {
        this.logger.debug(
          `YAML parsing error in ${fileName} during type detection: ${e.message}. Falling back to content-based detection.`,
        );
      }

      return detectedType;
    }
  }

  /**
   * Detect rule type from content patterns when YAML parsing fails or doesn't provide clear indicators
   * @param content The rule content to analyze
   * @param fileName The filename for logging purposes
   * @returns Detected rule type or UNKNOWN if no patterns match
   */
  private detectRuleTypeFromContent(
    content: string,
    fileName: string,
  ): RuleType {
    // Check for SIGMA patterns
    if (
      this.hasAnyPattern(
        content,
        RULE_TYPE_DETECTION_PATTERNS.SIGMA.CONTENT_PATTERNS,
      )
    ) {
      return RuleType.SIGMA;
    }

    // Check for KQL patterns
    if (
      this.hasAnyPattern(
        content,
        RULE_TYPE_DETECTION_PATTERNS.KQL.CONTENT_PATTERNS,
      )
    ) {
      return RuleType.KQL;
    }

    // Check for SPL patterns
    if (
      this.hasAnyPattern(
        content,
        RULE_TYPE_DETECTION_PATTERNS.SPL.CONTENT_PATTERNS,
      )
    ) {
      return RuleType.SPL;
    }

    this.logger.debug(
      `Could not detect rule type from content patterns in ${fileName}, using UNKNOWN`,
    );
    return RuleType.UNKNOWN;
  }

  /**
   * Check if a parsed YAML object has any of the specified fields
   * @param parsed The parsed YAML object
   * @param fields Array of field names to check for
   * @returns True if any field is found
   */
  private hasAnyField(
    parsed: Record<string, any>,
    fields: readonly string[],
  ): boolean {
    return fields.some((field) => field in parsed);
  }

  /**
   * Check if content contains any of the specified patterns
   * @param content The content to search
   * @param patterns Array of patterns to search for
   * @returns True if any pattern is found
   */
  private hasAnyPattern(content: string, patterns: readonly string[]): boolean {
    return patterns.some((pattern) => content.includes(pattern));
  }

  /**
   * Validates that the parser returned by the factory is valid and implements the required interface
   * @param parser The parser to validate
   * @param ruleType The rule type for error reporting
   * @param fileName The filename for error reporting
   * @throws RuleValidationError if parser is invalid
   */
  private validateParser(
    parser: unknown,
    ruleType: RuleType,
    fileName: string,
  ): asserts parser is IRuleParser {
    if (!parser) {
      throw new RuleValidationError(
        `Parser factory returned null/undefined for rule type: ${ruleType}`,
        fileName,
      );
    }

    if (typeof parser !== 'object') {
      throw new RuleValidationError(
        `Parser factory returned invalid parser type for rule type: ${ruleType}. Expected object, got ${typeof parser}`,
        fileName,
      );
    }

    if (!('parseRule' in parser) || typeof parser.parseRule !== 'function') {
      throw new RuleValidationError(
        `Parser for rule type ${ruleType} does not implement required parseRule method`,
        fileName,
      );
    }

    this.logger.debug(
      `Parser validation passed for rule type ${ruleType} (${fileName})`,
    );
  }
}
