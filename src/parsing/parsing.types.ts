import { RuleType, TestCase } from '../rules/models/rule.model';
import {
  MitreAttackObject,
  RuleMetadata,
} from '../rules/models/rule-metadata.model';

// Interface for Sigma logsource structure
export interface LogSource {
  category?: string;
  product?: string;
  service?: string;
}

// Interface for KQL data connector structure
export interface DataConnector {
  dataTypes?: string[];
  [key: string]: any;
}

// Define a type for the raw rule data shared across different rule parsers
export interface CommonRuleData {
  author?: string;
  status?: string;
  id?: string;
  references?: string[];
  date?: any;
  modified?: any;
  level?: string; // For Sigma
  severity?: string; // For KQL
  title?: string; // Common for title extraction
  description?: string; // Common for description extraction
  tags?: string[] | string | Record<string, unknown>; // For tag extraction
  relevantTechniques?: string[]; // For MITRE
  tactics?: string[]; // For MITRE (KQL)
  logsource?: LogSource; // For Sigma data_sources
  requiredDataConnectors?: DataConnector[]; // For KQL data_sources
  kind?: string; // KQL specific
  queryFrequency?: string; // KQL specific
  queryPeriod?: string; // KQL specific
  triggerOperator?: string; // KQL specific
  triggerThreshold?: number; // KQL specific
  entityMappings?: any[]; // KQL specific
  query?: string; // KQL query
  name?: string; // KQL name (alternative to title)
  metadata?: RuleMetadata; // Added for consistency, KQL-in-YAML might have it
}

/**
 * Input interface for parsing rule content
 */
export interface ParseRuleInput {
  /**
   * Raw content of the rule file
   */
  content: string;

  /**
   * Name of the file being parsed
   */
  fileName: string;

  /**
   * User-supplied MITRE ATT&CK objects
   */
  existingMitreAttack?: MitreAttackObject[];

  /**
   * Rule type (SIGMA, KQL, etc.)
   * If not provided, will default to UNKNOWN and attempt basic parsing
   */
  ruleType?: RuleType;
}

/**
 * Result of parsing a rule file
 */
export interface ParseRuleResult {
  /**
   * Rule title
   */
  title: string;

  /**
   * Rule description
   */
  description: string;

  /**
   * Rule type (SIGMA, KQL, etc.)
   */
  rule_type: RuleType;

  /**
   * Original rule content
   */
  content: string;

  /**
   * Tags associated with the rule
   */
  tags: string[];

  /**
   * Metadata for the rule
   */
  metadata: RuleMetadata;

  /**
   * Test cases for the rule
   */
  test_cases: TestCase[];

  /**
   * Parsing success status
   */
  success: boolean;

  /**
   * Error message if parsing failed
   */
  error?: string;
}
