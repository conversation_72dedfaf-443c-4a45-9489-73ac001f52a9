import { Module } from '@nestjs/common';
import { ParsingService } from './parsing.service';
import { MitreDataProcessor } from './services/mitre-data-processor.service';
import { MetadataBuilder } from './services/metadata-builder.service';
import { TagProcessor } from './services/tag-processor.service';
import { TestCaseFactory } from './services/test-case-factory.service';
import { ErrorHandlingService } from './services/error-handling.service';
import { RuleParserFactory } from './services/rule-parser-factory.service';
import { SigmaRuleParser } from './ruleparsers/sigma-rule-parser.service';
import { KqlRuleParser } from './ruleparsers/kql-rule-parser.service';
import { SplRuleParser } from './ruleparsers/spl-rule-parser.service';
import { GenericRuleParser } from './ruleparsers/generic-rule-parser.service';
import { MitreDataModule } from '../mitre/mitre-data.module';

/**
 * Module for parsing rule files
 */
@Module({
  imports: [MitreDataModule],
  providers: [
    // Main service
    ParsingService,
    // Core services
    MitreDataProcessor,
    MetadataBuilder,
    TagProcessor,
    TestCaseFactory,
    ErrorHandlingService,
    // Parser factory and strategies
    RuleParserFactory,
    SigmaRuleParser,
    KqlRuleParser,
    SplRuleParser,
    GenericRuleParser,
  ],
  exports: [ParsingService],
})
export class ParsingModule {}
