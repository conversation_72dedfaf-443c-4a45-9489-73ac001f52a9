import { Test } from '@nestjs/testing';
import { ParsingModule } from './parsing.module';
import { ParsingService } from './parsing.service';
import { MitreSearchService } from '../mitre/services/mitre-search.service';

describe('ParsingModule', () => {
  const mockMitreSearchService = {
    getObjectByUniqueMitreId: jest.fn(),
    getObjectByName: jest.fn(),
    getObjectByUniqueName: jest.fn(),
    searchMitreData: jest.fn(),
  };

  it('should compile the module', async () => {
    const module = await Test.createTestingModule({
      imports: [ParsingModule],
    })
      .overrideProvider(MitreSearchService)
      .useValue(mockMitreSearchService)
      .compile();

    expect(module).toBeDefined();
  });

  it('should provide ParsingService', async () => {
    const module = await Test.createTestingModule({
      imports: [ParsingModule],
    })
      .overrideProvider(MitreSearchService)
      .useValue(mockMitreSearchService)
      .compile();

    const parsingService = module.get<ParsingService>(ParsingService);
    expect(parsingService).toBeDefined();
    expect(parsingService).toBeInstanceOf(ParsingService);
  });
});
