import {
  RuleMetadata,
  MitreAttackObject,
} from '../../rules/models/rule-metadata.model';

/**
 * Clean MITRE attack metadata to only include allowed fields
 */
export function cleanMitreAttackMetadata(
  metadata?: RuleMetadata,
): RuleMetadata | undefined {
  if (!metadata) return metadata;

  const ALLOWED_MITRE_FIELDS = [
    'id',
    'mitre_id',
    'name',
    'parent_name',
    'type',
  ];

  if (metadata.mitre_attack && Array.isArray(metadata.mitre_attack)) {
    metadata.mitre_attack = metadata.mitre_attack.map((obj: any) => {
      const cleaned: Record<string, any> = {};

      for (const field of ALLOWED_MITRE_FIELDS) {
        if (
          obj &&
          field in obj &&
          (obj as Record<string, any>)[field] !== undefined
        ) {
          cleaned[field] = (obj as Record<string, any>)[field] as unknown;
        }
      }

      return cleaned as MitreAttackObject;
    });
  }

  return metadata;
}
