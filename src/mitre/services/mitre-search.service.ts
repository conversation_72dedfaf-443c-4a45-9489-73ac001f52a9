import {
  Injectable,
  Logger,
  OnApplicationBootstrap,
  InternalServerErrorException,
  ServiceUnavailableException,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { OpenSearchService } from '../../opensearch/opensearch.service';
import { OpenSearchConfigService } from '../../opensearch/config/opensearch.config';
import { SearchMitreDataDto } from '../dto/search-mitre-data.dto';
import {
  MitreAttackObject,
  MitreAttackObjectType,
} from '../../rules/models/rule-metadata.model';
import { Client } from '@opensearch-project/opensearch';
import { MitreDataIngestionService } from './mitre-data-ingestion.service';
import { MitreDataMigrationService } from './mitre-data-migration.service';
import { NormalizedMitreDocument } from '../mitre-data.interfaces';
import {
  OpenSearchSearchBody,
  OpenSearchBoolQuery,
  OpenSearchQueryClause,
  OpenSearchSearchResponse,
  OpenSearchHit,
  isOpenSearchClientErrorWithMetaBody,
  isConnectionError,
  OpenSearchErrorDetail,
} from '../../opensearch/opensearch.interfaces';
import { GetTacticsDto } from '../dto/get-tactics.dto';
import { GetTechniquesDto } from '../dto/get-techniques.dto';
import { GetSubtechniquesDto } from '../dto/get-subtechniques.dto';
import { GetMitigationsDto } from '../dto/get-mitigations.dto';
import { GetGroupsDto } from '../dto/get-groups.dto';
import { GetSoftwareDto } from '../dto/get-software.dto';
import { GetCampaignsDto } from '../dto/get-campaigns.dto';
import { GetDatasourcesDto } from '../dto/get-datasources.dto';
import { GetDatacomponentsDto } from '../dto/get-datacomponents.dto';
import { MitreDomain } from '../dto/get-techniques-by-tactic.dto';
import { STIX_ID_REGEX } from '../../common/constants/stix-mitre-regexes';

@Injectable()
export class MitreSearchService implements OnApplicationBootstrap {
  private readonly logger = new Logger(MitreSearchService.name);

  public static readonly DEFAULT_MITRE_OBJECT_SOURCE_FIELDS: string[] = [
    'id',
    'name',
    'mitre_id',
    'description',
    'type',
    'mitre_url',
    'shortname',
    'parent_name',
    'aliases',
    'platforms',
    'first_seen',
    'last_seen',
    'collection_layers',
    'data_source_ref',
    'is_subtechnique',
    'parent_id',
    'kill_chain_phases',
  ];

  private readonly KNOWN_TYPES: Set<string> = new Set([
    'attack-pattern',
    'campaign',
    'course-of-action',
    'data-source',
    'data-component',
    'group',
    'identity',
    'indicator',
    'intrusion-set',
    'location',
    'malware',
    'marking-definition',
    'mitigation',
    'note',
    'observed-data',
    'opinion',
    'relationship',
    'report',
    'tactic',
    'technique',
    'subtechnique',
    'threat-actor',
    'tool',
    'vulnerability',
    'x-mitre-collection',
    'x-mitre-data-source',
    'x-mitre-data-component',
    'x-mitre-matrix',
    'x-mitre-tactic',
  ]);

  private readonly ID_PATTERN: RegExp =
    /^(T|DS|M|S|G|C|TA|CAPEC-|ATT&CK-|PRE-)\d+(\.\d+)?$/i;

  constructor(
    private readonly openSearchService: OpenSearchService,
    private readonly opensearchConfigService: OpenSearchConfigService,
    private readonly ingestionService: MitreDataIngestionService,
    @Inject(forwardRef(() => MitreDataMigrationService))
    private readonly migrationService: MitreDataMigrationService,
  ) {}

  async onApplicationBootstrap(): Promise<void> {
    this.logger.log(
      'MitreSearchService: Orchestrating MITRE data processes on startup...',
    );
    try {
      this.logger.log('Attempting MITRE data ingestion...');
      await this.ingestionService.ingestMitreData();
      this.logger.log(
        'MITRE data ingestion process completed successfully via MitreDataIngestionService.',
      );
    } catch (error: unknown) {
      this.logger.error(
        'Failed to ingest MITRE data during startup via MitreDataIngestionService.',
        error instanceof Error ? error.stack : String(error),
      );
    }

    try {
      this.logger.log('Attempting rule migration for MITRE data...');
      const migrationStats = await this.migrationService.migrateRules();
      this.logger.log(
        `Rule migration process completed via MitreDataMigrationService. Stats: ${JSON.stringify(migrationStats)}`,
      );
    } catch (error: unknown) {
      this.logger.error(
        'Failed to migrate rules for MITRE data during startup via MitreDataMigrationService.',
        error instanceof Error ? error.stack : String(error),
      );
    }
    this.logger.log(
      'MitreSearchService: MITRE data processes orchestration finished.',
    );
  }

  public async executeOpenSearchQuery(
    searchBody: OpenSearchSearchBody,
    operationDescription: string,
  ): Promise<NormalizedMitreDocument[]> {
    const osClient: Client = this.openSearchService.getClient();
    const targetIndexAlias =
      this.opensearchConfigService.getIndexName('mitre-attack');

    this.logger.debug(
      `Executing OpenSearch query for ${operationDescription}: ${JSON.stringify(searchBody)}`,
    );

    try {
      const response: OpenSearchSearchResponse<NormalizedMitreDocument> =
        await osClient.search({
          index: targetIndexAlias,
          body: searchBody,
        });
      return response.body?.hits?.hits.map((hit) => hit._source) || [];
    } catch (error: unknown) {
      this.logger.error(
        `OpenSearch error during ${operationDescription}: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );

      if (isOpenSearchClientErrorWithMetaBody(error)) {
        const esError: OpenSearchErrorDetail = error.meta.body.error;
        if (esError.type === 'index_not_found_exception') {
          this.logger.warn(
            `Index '${targetIndexAlias}' not found during ${operationDescription}. Returning empty results.`,
          );
          return [];
        }
        if (esError.reason || esError.type) {
          throw new InternalServerErrorException(
            `OpenSearch query failed during ${operationDescription}: ${
              esError.reason || esError.type
            }`,
          );
        }
      }

      if (isConnectionError(error)) {
        throw new ServiceUnavailableException(
          `Could not connect to OpenSearch during ${operationDescription}`,
        );
      }

      throw new InternalServerErrorException(
        `An unexpected error occurred during ${operationDescription}.`,
      );
    }
  }

  public mapToMitreAttackObject(
    doc: NormalizedMitreDocument,
    requestedFields?: string[],
  ): MitreAttackObject {
    const typeValue = doc.type;

    // Handle missing or invalid type values
    if (!typeValue || typeValue === 'undefined') {
      this.logger.warn(
        `Document ID '${doc.id}' has missing or invalid type value: '${typeValue}'. ` +
          `This may indicate incomplete data retrieval from OpenSearch. ` +
          `Please ensure the 'type' field is included in requested fields for proper mapping.`,
      );
      // Log additional context for debugging
      this.logger.debug(
        `Document details for ID '${doc.id}': name='${doc.name}', mitre_id='${doc.mitre_id}', ` +
          `requested_fields=[${requestedFields?.join(', ') || 'defaults'}]`,
      );
    } else if (
      !Object.values(MitreAttackObjectType).includes(
        typeValue as MitreAttackObjectType,
      )
    ) {
      this.logger.warn(
        `Document ID '${doc.id}' has type string '${typeValue}' which is not a direct member of MitreAttackObjectType enum. ` +
          `Ensure that the upstream data normalization correctly maps STIX types to the expected enum string values. ` +
          `Proceeding with the cast, but this could lead to type inconsistencies if '${typeValue}' is truly unexpected.`,
      );
    }

    const baseObject: MitreAttackObject = {
      id: doc.id,
      name: doc.name || '',
      mitre_id: doc.mitre_id || '',
      description: doc.description || '',
      type: typeValue as MitreAttackObjectType,
      url: doc.mitre_url || '',
      shortname: doc.shortname,
      parent_name: doc.parent_name,
      aliases: doc.aliases,
      platforms: doc.platforms,
      first_seen: doc.first_seen,
      last_seen: doc.last_seen,
      collection_layers: doc.collection_layers,
      data_source_ref: doc.data_source_ref,
      is_subtechnique: doc.is_subtechnique,
      parent_id: doc.parent_id,
      source_id: doc.source_id,
      target_id: doc.target_id,
      data_sources: doc.data_sources,
      detection: doc.detection,
      external_references: doc.external_references
        ?.map((ref) => ref.url)
        .filter((url) => typeof url === 'string') as string[],
      kill_chain_phases: doc.kill_chain_phases,
    };

    const finalObject: Partial<MitreAttackObject> = {
      id: baseObject.id,
      type: baseObject.type,
    };

    const fieldsToInclude =
      requestedFields && requestedFields.length > 0
        ? requestedFields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    fieldsToInclude.forEach((field) => {
      if (field === 'id' || field === 'type') return;

      const key = field as keyof MitreAttackObject;

      if (key in baseObject) {
        const value = baseObject[key];
        if (value !== undefined) {
          (finalObject as Record<keyof MitreAttackObject, any>)[key] = value;
        }
      } else {
        this.logger.debug(
          `Field '${field}' requested but not found on baseObject for document ID '${doc.id}'.`,
        );
      }
    });

    return finalObject as MitreAttackObject;
  }

  private static getQueryTypes(
    objectType: MitreAttackObjectType | MitreAttackObjectType[],
    options?: { includeSubtechniques?: boolean },
  ): string[] {
    const types = Array.isArray(objectType) ? [...objectType] : [objectType];
    if (
      options?.includeSubtechniques &&
      types.includes(MitreAttackObjectType.TECHNIQUE) &&
      !types.includes(MitreAttackObjectType.SUBTECHNIQUE)
    ) {
      types.push(MitreAttackObjectType.SUBTECHNIQUE);
    }
    return types.map((t) => String(t));
  }

  private async _executeAndMapQuery(
    query: OpenSearchQueryClause,
    requestedSourceFields: string[] | undefined,
    operationDescription: string,
    size: number = 1000,
    sortOptions?: any[],
  ): Promise<MitreAttackObject[]> {
    const sourceFieldsToFetch =
      requestedSourceFields && requestedSourceFields.length > 0
        ? requestedSourceFields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    const searchBody: OpenSearchSearchBody = {
      size,
      _source: sourceFieldsToFetch,
      query: query,
    };

    if (sortOptions) {
      searchBody.sort = sortOptions;
    }

    const rawResults = await this.executeOpenSearchQuery(
      searchBody,
      operationDescription,
    );

    const results = rawResults.map((doc) =>
      this.mapToMitreAttackObject(doc, sourceFieldsToFetch),
    );
    this.logger.log(
      `Found ${results.length} ${operationDescription} via _executeAndMapQuery.`,
    );
    return results;
  }

  private async _getMitreObjects(
    objectTypes: string[],
    queryDto: { fields?: string[] } | undefined,
    operationDescription: string,
    maxSize = 1000,
    sortOptions: any[] = [{ 'name.keyword': 'asc' }],
  ): Promise<MitreAttackObject[]> {
    this.logger.log(
      `Fetching ${operationDescription}. Types: ${objectTypes.join(', ')}. Fields: '${
        queryDto?.fields?.join(', ') || 'defaults'
      }'`,
    );

    const sourceFieldsToFetch =
      queryDto?.fields && queryDto.fields.length > 0
        ? queryDto.fields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    const boolQuery: OpenSearchBoolQuery = {
      filter: [
        {
          terms: {
            type: objectTypes,
          },
        },
      ],
    };

    const searchBody: OpenSearchSearchBody = {
      size: maxSize,
      _source: sourceFieldsToFetch,
      query: { bool: boolQuery },
      sort: sortOptions,
    };

    const rawResults = await this.executeOpenSearchQuery(
      searchBody,
      operationDescription,
    );
    const results = rawResults.map((doc) =>
      this.mapToMitreAttackObject(doc, sourceFieldsToFetch),
    );
    this.logger.log(`Found ${results.length} ${operationDescription}.`);
    return results;
  }

  async getTactics(queryDto?: GetTacticsDto): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching tactic by ID: '${queryDto.id}'`);
      const tactic = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.TACTIC,
        queryDto.fields,
        `MITRE tactic by ID '${queryDto.id}'`,
      );
      return tactic ? [tactic] : [];
    }

    this.logger.log(`Fetching all MITRE tactics`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: [
            {
              terms: {
                type: [MitreAttackObjectType.TACTIC],
              },
            },
          ],
        },
      },
      queryDto?.fields,
      'MITRE tactics',
      100,
      [{ 'name.keyword': 'asc' }],
    );
  }

  async getTechniques(
    queryDto: GetTechniquesDto,
  ): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching technique by ID: '${queryDto.id}'`);
      const technique = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.TECHNIQUE,
        queryDto.fields,
        `MITRE technique by ID '${queryDto.id}'`,
      );
      return technique ? [technique] : [];
    }

    this.logger.log(`Fetching MITRE techniques`);
    const { include_subtechniques = true, fields } = queryDto || {};
    const typesToQuery = [MitreAttackObjectType.TECHNIQUE];
    if (include_subtechniques) {
      typesToQuery.push(MitreAttackObjectType.SUBTECHNIQUE);
    }

    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: typesToQuery,
            },
          },
        },
      },
      fields,
      'MITRE techniques (and subtechniques if included)',
      2000,
      [{ mitre_id: 'asc' }, { 'name.keyword': 'asc' }],
    );
  }

  async getSubtechniques(
    queryDto?: GetSubtechniquesDto,
  ): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching sub-technique by ID: '${queryDto.id}'`);
      const subtechnique = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.SUBTECHNIQUE,
        queryDto.fields,
        `MITRE sub-technique by ID '${queryDto.id}'`,
      );
      return subtechnique ? [subtechnique] : [];
    }

    this.logger.log(`Fetching all MITRE sub-techniques`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: [MitreAttackObjectType.SUBTECHNIQUE],
            },
          },
        },
      },
      queryDto?.fields,
      'MITRE sub-techniques',
      2000,
      [{ mitre_id: 'asc' }, { 'name.keyword': 'asc' }],
    );
  }

  async getMitigations(
    queryDto?: GetMitigationsDto,
  ): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching mitigation by ID: '${queryDto.id}'`);
      const mitigation = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.MITIGATION,
        queryDto.fields,
        `MITRE mitigation by ID '${queryDto.id}'`,
      );
      return mitigation ? [mitigation] : [];
    }

    this.logger.log(`Fetching all MITRE mitigations`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: [MitreAttackObjectType.MITIGATION],
            },
          },
        },
      },
      queryDto?.fields,
      'MITRE mitigations',
      200,
      [{ 'name.keyword': 'asc' }],
    );
  }

  async getGroups(queryDto?: GetGroupsDto): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching group by ID: '${queryDto.id}'`);
      const group = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.GROUP,
        queryDto.fields,
        `MITRE group by ID '${queryDto.id}'`,
      );
      return group ? [group] : [];
    }

    this.logger.log(`Fetching all MITRE groups`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: [MitreAttackObjectType.GROUP],
            },
          },
        },
      },
      queryDto?.fields,
      'MITRE groups',
      500,
      [{ 'name.keyword': 'asc' }],
    );
  }

  async getSoftware(queryDto?: GetSoftwareDto): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching software by ID: '${queryDto.id}'`);
      const software = await this._getObjectByIdentifierInternal(
        queryDto.id,
        undefined,
        queryDto.fields,
        `MITRE software by ID '${queryDto.id}'`,
      );
      return software ? [software] : [];
    }

    this.logger.log(`Fetching all MITRE software (tools and malware)`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: [MitreAttackObjectType.TOOL, MitreAttackObjectType.MALWARE],
            },
          },
        },
      },
      queryDto?.fields,
      'MITRE software (tools and malware)',
      1000,
      [{ 'name.keyword': 'asc' }],
    );
  }

  async getCampaigns(queryDto?: GetCampaignsDto): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching campaign by ID: '${queryDto.id}'`);
      const campaign = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.CAMPAIGN,
        queryDto.fields,
        `MITRE campaign by ID '${queryDto.id}'`,
      );
      return campaign ? [campaign] : [];
    }

    this.logger.log(`Fetching all MITRE campaigns`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: [MitreAttackObjectType.CAMPAIGN],
            },
          },
        },
      },
      queryDto?.fields,
      'MITRE campaigns',
      500,
      [{ 'name.keyword': 'asc' }],
    );
  }

  async getDatasources(
    queryDto?: GetDatasourcesDto,
  ): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching data source by ID: '${queryDto.id}'`);
      const datasource = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.DATA_SOURCE,
        queryDto.fields,
        `MITRE data source by ID '${queryDto.id}'`,
      );
      return datasource ? [datasource] : [];
    }

    this.logger.log(`Fetching all MITRE data sources`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: [MitreAttackObjectType.DATA_SOURCE],
            },
          },
        },
      },
      queryDto?.fields,
      'MITRE data sources',
      500,
      [{ 'name.keyword': 'asc' }],
    );
  }

  async getDatacomponents(
    queryDto?: GetDatacomponentsDto,
  ): Promise<MitreAttackObject[]> {
    if (queryDto?.id) {
      this.logger.log(`Fetching data component by ID: '${queryDto.id}'`);
      const datacomponent = await this._getObjectByIdentifierInternal(
        queryDto.id,
        MitreAttackObjectType.DATA_COMPONENT,
        queryDto.fields,
        `MITRE data component by ID '${queryDto.id}'`,
      );
      return datacomponent ? [datacomponent] : [];
    }

    this.logger.log(`Fetching all MITRE data components`);
    return this._executeAndMapQuery(
      {
        bool: {
          filter: {
            terms: {
              type: [MitreAttackObjectType.DATA_COMPONENT],
            },
          },
        },
      },
      queryDto?.fields,
      'MITRE data components',
      500,
      [{ 'name.keyword': 'asc' }],
    );
  }

  async searchMitreData(
    queryDto: SearchMitreDataDto,
  ): Promise<MitreAttackObject[]> {
    const { q, exclude_types, fields } = queryDto;
    this.logger.log(
      `Searching MITRE data with q: '${q}', exclude_types: '${exclude_types?.join(', ') || 'none'}', fields: '${fields?.join(', ') || 'defaults'}'`,
    );

    if (!q || q.trim() === '') {
      return [];
    }

    const osClient: Client = this.openSearchService.getClient();
    const baseIndexName = 'mitre-attack';
    const targetIndexAlias =
      this.opensearchConfigService.getIndexName(baseIndexName);

    const should_clauses: OpenSearchQueryClause[] = [];
    const cleaned_query = q.trim();

    if (this.KNOWN_TYPES.has(cleaned_query.toLowerCase())) {
      should_clauses.push({
        term: {
          type: {
            value: cleaned_query,
            boost: 10.0,
          },
        },
      });
      this.logger.debug(
        `Query '${cleaned_query}' matches known type. Adding boosted type term query.`,
      );
    }

    const is_id_query = this.ID_PATTERN.test(cleaned_query);
    if (is_id_query) {
      const exact_id = cleaned_query.toUpperCase();
      should_clauses.push({
        term: {
          mitre_id: {
            value: exact_id,
            boost: 8.0,
          },
        },
      });
      this.logger.debug(
        `Query '${cleaned_query}' detected as ID pattern. Adding boosted mitre_id term query for '${exact_id}'.`,
      );
    }

    // Add high-priority exact name match query for better relevance
    should_clauses.push({
      match: {
        name: {
          query: cleaned_query,
          operator: 'and',
          boost: 20.0,
        },
      },
    });
    this.logger.debug(
      `Adding high-priority exact match for 'name' with query '${cleaned_query}' (boost: 20.0).`,
    );

    // Add type-prioritized name matching
    // Tactics - highest priority
    should_clauses.push({
      bool: {
        must: [
          { term: { type: 'tactic' } },
          {
            match: {
              name: {
                query: cleaned_query,
                operator: 'and',
                boost: 100.0,
              },
            },
          },
        ],
      },
    });

    // Techniques - second priority
    should_clauses.push({
      bool: {
        must: [
          { term: { type: 'technique' } },
          {
            match: {
              name: {
                query: cleaned_query,
                operator: 'and',
                boost: 50.0,
              },
            },
          },
        ],
      },
    });

    // Subtechniques - third priority
    should_clauses.push({
      bool: {
        must: [
          { term: { type: 'subtechnique' } },
          {
            match: {
              name: {
                query: cleaned_query,
                operator: 'and',
                boost: 25.0,
              },
            },
          },
        ],
      },
    });

    this.logger.debug(
      `Adding type-prioritized name matching for tactics (100.0), techniques (50.0), and subtechniques (25.0).`,
    );

    should_clauses.push({
      match_phrase_prefix: {
        name: {
          query: cleaned_query,
          boost: 12.0,
        },
      },
    });
    this.logger.debug(
      `Adding match_phrase_prefix for 'name' with query '${cleaned_query}'.`,
    );

    should_clauses.push({
      match_phrase_prefix: {
        parent_name: {
          query: cleaned_query,
          boost: 8.0,
        },
      },
    });
    this.logger.debug(
      `Adding match_phrase_prefix for 'parent_name' with query '${cleaned_query}'.`,
    );

    should_clauses.push({
      multi_match: {
        query: cleaned_query,
        fields: [
          'name^3',
          'description',
          'aliases',
          'mitre_id^1',
          'platforms^1',
          'type^1',
          'parent_name^2',
        ],
        type: 'best_fields',
        fuzziness: 'AUTO',
      },
    });

    const must_not_clauses: OpenSearchQueryClause[] = [];
    if (exclude_types && exclude_types.length > 0) {
      const valid_exclude_types = exclude_types
        .map((t) => t.trim())
        .filter((t) => t);
      if (valid_exclude_types.length > 0) {
        must_not_clauses.push({
          terms: {
            type: valid_exclude_types,
          },
        });
        this.logger.debug(`Excluding types: ${valid_exclude_types.join(', ')}`);
      }
    }

    const bool_query: OpenSearchBoolQuery = {
      should: should_clauses,
      minimum_should_match: 1,
    };

    if (must_not_clauses.length > 0) {
      bool_query.must_not = must_not_clauses;
    }

    const sourceFieldsToFetch =
      fields && fields.length > 0
        ? fields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    const search_body: OpenSearchSearchBody = {
      size: 20,
      _source: sourceFieldsToFetch,
      query: {
        bool: bool_query,
      },
    };

    this.logger.debug(
      `Constructed OpenSearch Query: ${JSON.stringify(search_body)}`,
    );

    try {
      const response: OpenSearchSearchResponse<NormalizedMitreDocument> =
        await osClient.search({
          index: targetIndexAlias,
          body: search_body,
        });

      const hits: OpenSearchHit<NormalizedMitreDocument>[] =
        response.body?.hits?.hits || [];
      const results: MitreAttackObject[] = hits.map(
        (hit: OpenSearchHit<NormalizedMitreDocument>) => {
          const source = hit._source;
          return this.mapToMitreAttackObject(source, sourceFieldsToFetch);
        },
      );

      this.logger.log(`Found ${results.length} results for query: '${q}'`);
      return results;
    } catch (error: unknown) {
      this.logger.error(
        `OpenSearch search error for query '${q}': ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );

      if (isOpenSearchClientErrorWithMetaBody(error)) {
        const esError: OpenSearchErrorDetail = error.meta.body.error;
        if (esError.type === 'index_not_found_exception') {
          this.logger.warn(
            `Index '${targetIndexAlias}' not found for query: '${q}'. Returning empty results.`,
          );
          return [];
        }
        if (esError.reason || esError.type) {
          throw new InternalServerErrorException(
            `OpenSearch query failed: ${esError.reason || esError.type}`,
          );
        }
      }

      if (isConnectionError(error)) {
        throw new ServiceUnavailableException(
          'Could not connect to OpenSearch',
        );
      }

      throw new InternalServerErrorException(
        'An unexpected error occurred during MITRE search.',
      );
    }
  }

  // Based on the Python FastAPI example
  // TODO: Check what the official STIX types are for these. Some might be x-mitre types.
  // These are used by the frontend to filter search results.
  // Used to map STIX object types to a display name for the frontend.
  // And for filtering by type in the backend.
  // Should align with MitreAttackObjectType values as much as possible.
  // Some STIX types (e.g. 'observed-data') are not directly represented as MitreAttackObjectType values.
  public static readonly STIX_TYPE_TO_DISPLAY_NAME: Record<string, string> = {
    'attack-pattern': 'Technique',
    campaign: 'Campaign',
    'course-of-action': 'Mitigation',
    'data-source': 'Data Source', // Placeholder, may need x-mitre specific
    'data-component': 'Data Component', // Placeholder, may need x-mitre specific
    group: 'Group', // Placeholder, STIX is intrusion-set
    identity: 'Identity',
    indicator: 'Indicator',
    'intrusion-set': 'Group',
    location: 'Location',
    malware: 'Malware',
    'marking-definition': 'Marking Definition',
    mitigation: 'Mitigation', // Placeholder, STIX is course-of-action
    note: 'Note',
    'observed-data': 'Observed Data',
    opinion: 'Opinion',
    relationship: 'Relationship',
    report: 'Report',
    tactic: 'Tactic', // Placeholder, STIX is x-mitre-tactic
    technique: 'Technique', // Placeholder, STIX is attack-pattern
    subtechnique: 'Sub-technique', // Placeholder, STIX is attack-pattern with is_subtechnique: true
    'threat-actor': 'Group', // Another possible mapping for Group
    tool: 'Tool',
    vulnerability: 'Vulnerability',
    'x-mitre-collection': 'Collection',
    'x-mitre-data-source': 'Data Source',
    'x-mitre-data-component': 'Data Component',
    'x-mitre-matrix': 'Matrix',
    'x-mitre-tactic': 'Tactic',
  };

  // Method to get a display name for a given STIX type string
  public static getDisplayNameForStixType(type: string): string {
    return MitreSearchService.STIX_TYPE_TO_DISPLAY_NAME[type] || type;
  }

  private async _getObjectByNameInternal(
    name: string,
    objectType: string | undefined,
    requestedFields?: string[],
    operationDescriptionPrefix = 'MITRE object',
  ): Promise<MitreAttackObject | null> {
    const opDesc = `${operationDescriptionPrefix} by name '${name}'${objectType ? ` and type '${objectType.toLowerCase()}'` : ''}`;
    this.logger.log(
      `Fetching ${opDesc}. Fields: '${requestedFields?.join(', ') || 'defaults'}'`,
    );

    const sourceFieldsToFetch =
      requestedFields && requestedFields.length > 0
        ? requestedFields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    const queryClauses: OpenSearchQueryClause[] = [];

    if (objectType) {
      queryClauses.push({ term: { type: objectType.toLowerCase() } });
    }

    // Search for exact matches in both 'name' and 'parent_name' fields
    // Case insensitivity is now handled by the text analyzers
    this.logger.debug(
      `Name search: Looking for '${name}' in 'name' and 'parent_name' fields.`,
    );

    const nameSearchClause: OpenSearchQueryClause = {
      bool: {
        should: [
          // Tactics - highest priority (boost: 1000.0 for name, 100.0 for parent_name)
          {
            bool: {
              must: [
                { term: { type: 'tactic' } },
                {
                  match: {
                    name: {
                      query: name,
                      operator: 'and',
                      boost: 1000.0,
                    },
                  },
                },
              ],
            },
          },
          {
            bool: {
              must: [
                { term: { type: 'tactic' } },
                {
                  match: {
                    parent_name: {
                      query: name,
                      operator: 'and',
                      boost: 100.0,
                    },
                  },
                },
              ],
            },
          },
          // Techniques - second priority (boost: 500.0 for name, 50.0 for parent_name)
          {
            bool: {
              must: [
                { term: { type: 'technique' } },
                {
                  match: {
                    name: {
                      query: name,
                      operator: 'and',
                      boost: 500.0,
                    },
                  },
                },
              ],
            },
          },
          {
            bool: {
              must: [
                { term: { type: 'technique' } },
                {
                  match: {
                    parent_name: {
                      query: name,
                      operator: 'and',
                      boost: 50.0,
                    },
                  },
                },
              ],
            },
          },
          // Subtechniques - third priority (boost: 250.0 for name, 25.0 for parent_name)
          {
            bool: {
              must: [
                { term: { type: 'subtechnique' } },
                {
                  match: {
                    name: {
                      query: name,
                      operator: 'and',
                      boost: 250.0,
                    },
                  },
                },
              ],
            },
          },
          {
            bool: {
              must: [
                { term: { type: 'subtechnique' } },
                {
                  match: {
                    parent_name: {
                      query: name,
                      operator: 'and',
                      boost: 25.0,
                    },
                  },
                },
              ],
            },
          },
          // All other types - lowest priority (boost: 10.0 for name, 1.0 for parent_name)
          {
            bool: {
              must_not: [
                { terms: { type: ['tactic', 'technique', 'subtechnique'] } },
              ],
              must: [
                {
                  match: {
                    name: {
                      query: name,
                      operator: 'and',
                      boost: 10.0,
                    },
                  },
                },
              ],
            },
          },
          {
            bool: {
              must_not: [
                { terms: { type: ['tactic', 'technique', 'subtechnique'] } },
              ],
              must: [
                {
                  match: {
                    parent_name: {
                      query: name,
                      operator: 'and',
                      boost: 1.0,
                    },
                  },
                },
              ],
            },
          },
        ],
        minimum_should_match: 1,
      },
    };

    queryClauses.push(nameSearchClause);

    if (queryClauses.length === 0) {
      this.logger.warn(
        `_getObjectByNameInternal: No query clauses generated for name '${name}' and type '${objectType}'. Returning null.`,
      );
      return null;
    }

    const searchBody: OpenSearchSearchBody = {
      size: 1,
      _source: sourceFieldsToFetch,
      query: { bool: { must: queryClauses } },
    };

    if (!objectType) {
      // When no object type is specified, search across all types
      searchBody.query = nameSearchClause;
      this.logger.debug(
        `_getObjectByNameInternal: No objectType provided, searching by name '${name}' across all types.`,
      );
    }

    this.logger.debug(
      `_getObjectByNameInternal: Constructed OpenSearch Query: ${JSON.stringify(searchBody.query)}`,
    );

    const rawResults = await this.executeOpenSearchQuery(searchBody, opDesc);

    if (rawResults && rawResults.length > 0) {
      if (rawResults.length > 1) {
        this.logger.warn(
          `Found multiple results for ${opDesc}. Returning the first one. Data may need deduplication or review. Name: ${name}, Type: ${objectType}`,
        );
      }
      const result = this.mapToMitreAttackObject(
        rawResults[0],
        requestedFields,
      );
      this.logger.log(
        `Found ${opDesc}: ${result.name} (STIX ID: ${result.id}, MITRE ID: ${
          result.mitre_id || 'N/A'
        })`,
      );
      return result;
    }

    this.logger.log(`${opDesc} not found.`);
    return null;
  }

  private async _getObjectByIdentifierInternal(
    identifier: string,
    objectType: string | undefined,
    requestedFields?: string[],
    operationDescriptionPrefix = 'MITRE object',
  ): Promise<MitreAttackObject | null> {
    const opDesc = `${operationDescriptionPrefix} by identifier '${identifier}'${objectType ? ` and type '${objectType.toLowerCase()}'` : ''}`;
    this.logger.log(
      `Fetching ${opDesc}. Fields: '${requestedFields?.join(', ') || 'defaults'}'`,
    );

    const sourceFieldsToFetch =
      requestedFields && requestedFields.length > 0
        ? requestedFields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    const queryClauses: OpenSearchQueryClause[] = [];
    const isStixId = STIX_ID_REGEX.test(identifier);

    if (objectType) {
      // Use simple term query - case insensitivity handled by normalizer
      queryClauses.push({
        term: {
          type: objectType,
        },
      });
    }

    if (isStixId) {
      this.logger.debug(
        `Identifier '${identifier}' detected as STIX ID. Querying 'id' field.`,
      );
      queryClauses.push({
        bool: {
          should: [
            // Direct ID match
            {
              term: {
                id: identifier,
              },
            },
            {
              term: {
                'id.keyword': identifier,
              },
            },
          ],
          minimum_should_match: 1,
        },
      });
    } else {
      this.logger.debug(
        `Identifier '${identifier}' not detected as STIX ID. Querying 'mitre_id' field.`,
      );
      queryClauses.push({
        term: {
          mitre_id: identifier,
        },
      });
    }

    if (queryClauses.length === 0) {
      this.logger.warn(
        `_getObjectByIdentifierInternal: No query clauses generated for identifier '${identifier}' and type '${objectType}'. Returning null.`,
      );
      return null;
    }

    const searchBody: OpenSearchSearchBody = {
      size: 1,
      _source: sourceFieldsToFetch,
      query: { bool: { must: queryClauses } }, // Default construction
    };

    if (!objectType) {
      if (isStixId) {
        searchBody.query = {
          bool: {
            should: [
              {
                term: {
                  id: identifier,
                },
              },
              {
                term: {
                  'id.keyword': identifier,
                },
              },
            ],
            minimum_should_match: 1,
          },
        };
      } else {
        searchBody.query = {
          term: {
            mitre_id: identifier,
          },
        };
      }
    }

    this.logger.debug(
      `_getObjectByIdentifierInternal: Constructed OpenSearch Query: ${JSON.stringify(searchBody.query)}`,
    );

    const rawResults = await this.executeOpenSearchQuery(searchBody, opDesc);

    if (rawResults && rawResults.length > 0) {
      if (rawResults.length > 1) {
        this.logger.warn(
          `Found multiple results for ${opDesc}. Returning the first one. Data may need deduplication or review. Identifier: ${identifier}, Type: ${objectType}`,
        );
      }
      const result = this.mapToMitreAttackObject(
        rawResults[0],
        requestedFields,
      );
      this.logger.log(
        `Found ${opDesc}: ${result.name} (STIX ID: ${result.id}, MITRE ID: ${
          result.mitre_id || 'N/A'
        })`,
      );
      return result;
    }

    this.logger.log(`${opDesc} not found.`);
    return null;
  }

  async getObjectByMitreId(
    objectType: string,
    mitreId: string,
    requestedFields?: string[],
  ): Promise<MitreAttackObject | null> {
    return this._getObjectByIdentifierInternal(
      mitreId,
      objectType,
      requestedFields,
      `MITRE object (via getObjectByMitreId)`,
    );
  }

  async getObjectByName(
    objectType: string,
    name: string,
    requestedFields?: string[],
  ): Promise<MitreAttackObject | null> {
    return this._getObjectByNameInternal(
      name,
      objectType,
      requestedFields,
      `MITRE object (via getObjectByName)`,
    );
  }

  async getObjectByStixId(
    stixId: string,
    requestedFields?: string[],
  ): Promise<MitreAttackObject | null> {
    return this._getObjectByIdentifierInternal(
      stixId,
      undefined, // Type is not known by this specific function signature, searches across all types
      requestedFields,
      `MITRE object (via getObjectByStixId)`,
    );
  }

  async getTechniquesByPlatform(
    platform: string,
    includeSubtechniques = true,
    requestedFields?: string[],
  ): Promise<MitreAttackObject[]> {
    const normalizedPlatform = platform.toLowerCase();
    const operationDescription = `MITRE techniques for platform '${normalizedPlatform}' (subtechniques included: ${includeSubtechniques})`;
    this.logger.log(
      `Fetching ${operationDescription}. Fields: '${
        requestedFields?.join(', ') || 'defaults'
      }'`,
    );

    const typesToFetch = ['technique'];
    if (includeSubtechniques) {
      typesToFetch.push('subtechnique');
    }

    const sourceFieldsToFetch =
      requestedFields && requestedFields.length > 0
        ? requestedFields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    const boolQuery: OpenSearchBoolQuery = {
      filter: [
        {
          terms: {
            type: typesToFetch,
          },
        },
        {
          term: {
            platforms: normalizedPlatform,
          },
        },
      ],
    };

    const searchBody: OpenSearchSearchBody = {
      size: 1000,
      _source: sourceFieldsToFetch,
      query: { bool: boolQuery },
      sort: [{ mitre_id: 'asc' }, { 'name.keyword': 'asc' }],
    };

    const rawResults = await this.executeOpenSearchQuery(
      searchBody,
      operationDescription,
    );
    const results = rawResults.map((doc) => this.mapToMitreAttackObject(doc));
    this.logger.log(`Found ${results.length} ${operationDescription}.`);
    return results;
  }

  private mapDomainToKillChainName(domain: MitreDomain): string {
    switch (domain) {
      case MitreDomain.ENTERPRISE_ATTACK:
        return 'mitre-attack';
      case MitreDomain.MOBILE_ATTACK:
        return 'mitre-mobile-attack';
      case MitreDomain.ICS_ATTACK:
        return 'mitre-ics-attack';
      default: {
        this.logger.warn(
          `Unknown MITRE domain value encountered: '${String(domain)}'. Defaulting to 'mitre-attack'.`,
        );
        return 'mitre-attack';
      }
    }
  }

  async getTechniquesByTactic(
    tacticShortname: string,
    domain: MitreDomain,
    includeSubtechniques = true,
    requestedFields?: string[],
  ): Promise<MitreAttackObject[]> {
    const normalizedTacticShortname = tacticShortname.toLowerCase();
    const mappedKillChainName = this.mapDomainToKillChainName(domain);
    const operationDescription = `MITRE techniques for tactic '${normalizedTacticShortname}' in domain '${domain}' (subtechniques included: ${includeSubtechniques})`;

    this.logger.log(
      `Fetching ${operationDescription}. Fields: '${requestedFields?.join(', ') || 'defaults'}'`,
    );

    const typesToFetch = ['technique'];
    if (includeSubtechniques) {
      typesToFetch.push('subtechnique');
    }

    const sourceFieldsToFetch =
      requestedFields && requestedFields.length > 0
        ? requestedFields
        : MitreSearchService.DEFAULT_MITRE_OBJECT_SOURCE_FIELDS;

    const boolQuery: OpenSearchBoolQuery = {
      filter: [
        {
          terms: {
            type: typesToFetch,
          },
        },
        {
          term: {
            'kill_chain_phases.phase_name': normalizedTacticShortname,
          },
        },
        {
          term: {
            'kill_chain_phases.kill_chain_name': mappedKillChainName,
          },
        },
      ],
    };

    const searchBody: OpenSearchSearchBody = {
      size: 1000,
      _source: sourceFieldsToFetch,
      query: { bool: boolQuery },
      sort: [{ mitre_id: 'asc' }, { 'name.keyword': 'asc' }],
    };

    const rawResults = await this.executeOpenSearchQuery(
      searchBody,
      operationDescription,
    );
    const results = rawResults.map((doc) => this.mapToMitreAttackObject(doc));
    this.logger.log(`Found ${results.length} ${operationDescription}.`);
    return results;
  }

  public async getObjectByUniqueMitreId(
    mitreId: string,
    requestedFields?: string[],
  ): Promise<MitreAttackObject | null> {
    this.logger.debug(
      `Service: Attempting to get object by unique MITRE ID or STIX ID: ${mitreId}`,
    );
    // ID_PATTERN is for MITRE IDs like T1234, STIX_ID_REGEX for STIX IDs
    if (!this.ID_PATTERN.test(mitreId) && !STIX_ID_REGEX.test(mitreId)) {
      this.logger.warn(
        `Invalid or unrecognized ID format for getObjectByUniqueMitreId: ${mitreId}. Neither MITRE ID nor STIX ID pattern matched.`,
      );
      return null;
    }

    // Call _getObjectByIdentifierInternal with objectType as undefined
    // This allows searching by MITRE ID or STIX ID without specifying type
    const result = await this._getObjectByIdentifierInternal(
      mitreId,
      undefined, // Key: objectType is undefined for a type-agnostic search
      requestedFields,
      `MITRE object by unique ID '${mitreId}'`,
    );

    if (result) {
      this.logger.debug(
        `Service: Found object for unique ID: ${mitreId}, Name: ${result.name}, Type: ${result.type}`,
      );
    } else {
      this.logger.debug(`Service: No object found for unique ID: ${mitreId}`);
    }
    return result;
  }

  public async getObjectByUniqueName(
    name: string,
    requestedFields?: string[],
  ): Promise<MitreAttackObject | null> {
    this.logger.debug(
      `Service: Attempting to get object by unique name: ${name}`,
    );

    // Call _getObjectByNameInternal with objectType as undefined
    // This allows searching by name across all object types
    const result = await this._getObjectByNameInternal(
      name,
      undefined, // Key: objectType is undefined for a type-agnostic search
      requestedFields,
      `MITRE object by unique name '${name}'`,
    );

    if (result) {
      this.logger.debug(
        `Service: Found object for unique name: ${name}, MITRE ID: ${result.mitre_id}, Type: ${result.type}`,
      );
    } else {
      this.logger.debug(`Service: No object found for unique name: ${name}`);
    }
    return result;
  }
}
