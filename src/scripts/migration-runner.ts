// IMPORTANT: Environment setup MUST happen before any NestJS modules are imported,
// especially AppModule, to ensure ConfigModule picks up these settings.

import { config as dotenvConfig } from 'dotenv';
// We use 'process' directly as it's a global in Node.js

// Helper function for argument parsing - defined early
// This handles arguments of the form --key=value
function getArgValue(args: string[], key: string): string | undefined {
  const arg = args.find((a) => a.startsWith(`${key}=`));
  if (arg) {
    return arg.split('=')[1];
  }
  return undefined;
}

// --- BEGIN IMMEDIATE ENVIRONMENT SETUP ---
const earlyArgs = process.argv.slice(2);
const envFileArg = getArgValue(earlyArgs, '--env-file');

if (envFileArg) {
  dotenvConfig({ path: envFileArg, override: true });
  console.log(
    `[MigrationRunner:PreNest] Loaded environment from: ${envFileArg}`,
  );
  console.log(
    `[MigrationRunner:PreNest] Initial OPENSEARCH_NODE from .env: ${process.env.OPENSEARCH_NODE}`,
  );
} else {
  console.log(
    `[MigrationRunner:PreNest] No --env-file specified. Using existing environment or defaults.`,
  );
}

// Always disable OpenSearch auto-migrations for the migration runner itself.
// This prevents the runner from attempting schema changes during NestJS app initialization
// if the main application's default configuration would otherwise enable them.
process.env.OPENSEARCH_AUTO_MIGRATIONS = 'false';

// Consolidate and ensure critical OpenSearch environment variables are set as intended for the script.
// OPENSEARCH_NODE should be taken from the loaded .env file (if specified) or pre-existing environment.
// OPENSEARCH_AUTO_MIGRATIONS is explicitly forced to 'false'.
const scriptControlledEnvValues = {
  OPENSEARCH_NODE: process.env.OPENSEARCH_NODE,
  OPENSEARCH_USERNAME: process.env.OPENSEARCH_USERNAME,
  OPENSEARCH_PASSWORD: process.env.OPENSEARCH_PASSWORD,
  OPENSEARCH_SSL_VERIFY: process.env.OPENSEARCH_SSL_VERIFY,
  OPENSEARCH_INDEX_PREFIX: process.env.OPENSEARCH_INDEX_PREFIX,
  OPENSEARCH_AUTO_MIGRATIONS: 'false', // Explicitly ensure this is 'false'
};

// Apply these controlled values to process.env. This is the state NestJS will see.
Object.assign(process.env, scriptControlledEnvValues);

console.log(
  `[MigrationRunner:PreNest] Effective OPENSEARCH_AUTO_MIGRATIONS for NestJS: ${process.env.OPENSEARCH_AUTO_MIGRATIONS}`,
);
console.log(
  `[MigrationRunner:PreNest] Effective OPENSEARCH_NODE for NestJS: ${process.env.OPENSEARCH_NODE}`,
);
// --- END IMMEDIATE ENVIRONMENT SETUP ---

// Now, import NestJS and other modules AFTER the environment setup
import { NestFactory } from '@nestjs/core';
import { Logger, INestApplicationContext } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// AppModule is imported now, after process.env is configured
import { AppModule } from '../app.module';
import { FixTooManyMitreAttackTagsMigration } from './migrations/0001-fix-too-many-mitre-attack-tags';
import { UpdateMitreAttackMetadataMigration } from './migrations/0002-update-mitre-attack-metadata';
import { RulesService } from '../rules/rules.service';
import { RuleRepository } from '../rules/repositories/rule.repository';
import { ParsingService } from '../parsing/parsing.service';
import { MitreSearchService } from '../mitre/services/mitre-search.service';
import { BaseMigration, MigrationOptions } from './migrations/base-migration';

/**
 * Migration runner script
 * Usage examples:
 * npm run migrate:rules -- --migration=0001-fix-too-many-mitre-attack-tags --dry-run
 * npm run migrate:rules -- --migration=0001-fix-too-many-mitre-attack-tags --batch-size=25
 * npm run migrate:rules -- --migration=0001-fix-too-many-mitre-attack-tags --env-file=.env.production
 * npm run migrate:rules -- --migration=0001-fix-too-many-mitre-attack-tags --env-file=/path/to/custom.env
 */

async function runMigration() {
  // Args for migration logic (envFileArg was handled in pre-Nest setup)
  const args = process.argv.slice(2);

  // Environment variables that the script intended to set.
  // These are read from the already-modified process.env for logging and confirmation.
  const effectiveEnvValuesForNest = {
    OPENSEARCH_NODE: process.env.OPENSEARCH_NODE,
    OPENSEARCH_USERNAME: process.env.OPENSEARCH_USERNAME,
    OPENSEARCH_PASSWORD: process.env.OPENSEARCH_PASSWORD,
    OPENSEARCH_SSL_VERIFY: process.env.OPENSEARCH_SSL_VERIFY,
    OPENSEARCH_INDEX_PREFIX: process.env.OPENSEARCH_INDEX_PREFIX,
    OPENSEARCH_AUTO_MIGRATIONS: process.env.OPENSEARCH_AUTO_MIGRATIONS,
  };

  const logger = new Logger('MigrationRunner');
  let app: INestApplicationContext | undefined; // Declare and type app here

  try {
    const migrationName = getArgValue(args, '--migration');
    const dryRun = args.includes('--dry-run');
    const batchSize = parseInt(getArgValue(args, '--batch-size') || '50', 10);
    const logProgress = !args.includes('--no-progress');

    // Log environment info using NestJS logger
    if (envFileArg) {
      // envFileArg is from the pre-Nest setup scope
      logger.log(`Environment loaded from: ${envFileArg}`);
    } else {
      logger.log(
        `No --env-file specified by user. Using existing environment or defaults.`,
      );
    }

    if (!migrationName) {
      logger.error(
        'Migration name is required. Use --migration=<migration-name>',
      );
      process.exit(1);
    }

    logger.log('🚀 Starting migration runner...');
    logger.log(`Migration: ${migrationName}`);
    logger.log(`Dry run: ${dryRun}`);
    logger.log(`Batch size: ${batchSize}`);

    logger.log(`🔧 Environment variables intended for NestJS init:`);
    logger.log(`OPENSEARCH_NODE: ${effectiveEnvValuesForNest.OPENSEARCH_NODE}`);
    logger.log(
      `OPENSEARCH_AUTO_MIGRATIONS: ${effectiveEnvValuesForNest.OPENSEARCH_AUTO_MIGRATIONS}`,
    );

    // Create NestJS application context. process.env is already fully prepared.
    logger.log(
      '[MigrationRunner:Debug] About to create NestJS application context...',
    );
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error', 'warn', 'log'], // Configure as needed
    });
    logger.log('[MigrationRunner:Debug] NestJS application context CREATED.');

    // Validate what ConfigService actually loaded
    logger.log('[MigrationRunner:Debug] About to get ConfigService...');
    const configService = app.get<ConfigService>(ConfigService);
    logger.log('[MigrationRunner:Debug] ConfigService INSTANCE OBTAINED.');

    const actualOpenSearchNode = configService.get<string>('OPENSEARCH_NODE');
    const actualAutoMigrations = configService.get<string>(
      'OPENSEARCH_AUTO_MIGRATIONS',
    );

    logger.log(
      `ConfigService effective OPENSEARCH_NODE: ${actualOpenSearchNode}`,
    );
    logger.log(
      `ConfigService effective OPENSEARCH_AUTO_MIGRATIONS: ${actualAutoMigrations}`,
    );

    if (actualOpenSearchNode !== effectiveEnvValuesForNest.OPENSEARCH_NODE) {
      logger.warn(
        `⚠️ ConfigService OPENSEARCH_NODE ('${actualOpenSearchNode}') differs from script's intended value ('${effectiveEnvValuesForNest.OPENSEARCH_NODE}'). This might indicate an unexpected override.`,
      );
    } else {
      logger.log(`✅ ConfigService is using the correct OpenSearch URL.`);
    }

    if (actualAutoMigrations === 'false') {
      logger.log(
        `✅ ConfigService confirms OPENSEARCH_AUTO_MIGRATIONS is 'false'.`,
      );
    } else {
      logger.warn(
        `⚠️ ConfigService reports OPENSEARCH_AUTO_MIGRATIONS as '${actualAutoMigrations}', expected 'false'. Review NestJS ConfigModule setup if this is not intended.`,
      );
    }

    // Get required services
    const rulesService = app.get(RulesService);
    const ruleRepository = app.get(RuleRepository);
    const parsingService = app.get(ParsingService);
    const mitreSearchService = app.get(MitreSearchService);

    // Create migration instance based on name
    let migration: BaseMigration;
    switch (migrationName) {
      case '0001-fix-too-many-mitre-attack-tags':
        migration = new FixTooManyMitreAttackTagsMigration(
          rulesService,
          ruleRepository,
          parsingService,
        );
        break;
      case '0002-update-mitre-attack-metadata':
        migration = new UpdateMitreAttackMetadataMigration(
          rulesService,
          ruleRepository,
          mitreSearchService,
          parsingService,
        );
        break;
      default:
        logger.error(`Unknown migration: ${migrationName}`);
        if (app) await app.close();
        process.exit(1);
    }

    // Execute migration
    const options: MigrationOptions = {
      dryRun,
      batchSize,
      logProgress,
    };

    logger.log(`\n📋 Migration Details:`);
    logger.log(`Name: ${migration.getName()}`);
    logger.log(`Description: ${migration.getDescription()}`);
    logger.log('');

    const startTime = Date.now();
    const result = await migration.execute(options);
    const duration = Date.now() - startTime;

    // Log results
    logger.log('\n✅ Migration completed!');
    logger.log(`Duration: ${duration}ms`);
    logger.log(`Total processed: ${result.totalProcessed}`);
    logger.log(`Successfully affected: ${result.affectedCount}`);
    logger.log(`Errors: ${result.errors.length}`);

    if (result.errors.length > 0) {
      logger.log('\n❌ Errors encountered:');
      result.errors.forEach((error, index) => {
        logger.error(`${index + 1}. Rule ${error.id}: ${error.error}`);
      });
    }

    if (dryRun) {
      logger.log(
        '\n🔍 This was a dry run - no changes were made to the database.',
      );
    }

    // Close the application
    if (app) await app.close();

    if (result.errors.length > 0) {
      process.exit(1);
    }
    process.exit(0); // Explicitly exit with 0 on success
  } catch (error) {
    // Use the NestJS logger if available, otherwise console.error
    const log = logger || console; // logger might not be initialized if error is very early
    log.error('❌ Migration failed catastrophically:', error);
    // Attempt to close app if it exists and error happened after its creation
    if (app && typeof app.close === 'function') {
      // check app before calling close
      try {
        await app.close();
        log.log('NestJS application context closed after error.');
      } catch (closeError) {
        log.error(
          'Error closing NestJS application context after failure:',
          closeError,
        );
      }
    }
    process.exit(1);
  }
}

// Execute migration if this file is run directly
if (require.main === module) {
  runMigration().catch((err) => {
    // This global catch is for unhandled promise rejections from runMigration itself,
    // though the try/catch within runMigration should handle most execution errors.
    console.error('[MigrationRunner:GlobalCatch] CRITICAL FAILURE:', err);
    process.exit(1);
  });
}
