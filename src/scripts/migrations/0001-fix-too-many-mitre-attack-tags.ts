import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ommand, ScanCommandInput } from '@aws-sdk/lib-dynamodb';
import {
  BaseMigration,
  MigrationOptions,
  MigrationResult,
} from './base-migration';
import { RulesService } from '../../rules/rules.service';
import { RuleRepository } from '../../rules/repositories/rule.repository';
import { ParsingService } from '../../parsing/parsing.service';
import { Rule, UpdateRuleDto, RuleStatus } from '../../rules/models/rule.model';
import { StoredRule } from '../../rules/schemas/rule.schema';
import { MitreAttackObject } from '../../rules/models/rule-metadata.model';

@Injectable()
export class FixTooManyMitreAttackTagsMigration extends BaseMigration {
  private readonly MITRE_ATTACK_THRESHOLD = 8;
  protected readonly logger: Logger;
  private exampleLogged = false;

  constructor(
    private readonly rulesService: RulesService,
    private readonly ruleRepository: RuleRepository,
    private readonly parsingService: ParsingService,
  ) {
    super(new Logger(FixTooManyMitreAttackTagsMigration.name));
  }

  getName(): string {
    return '0001-fix-too-many-mitre-attack-tags';
  }

  getDescription(): string {
    return 'Fix rules that have 9 or more MITRE attack objects by re-parsing them through ParsingService';
  }

  async execute(options: MigrationOptions = {}): Promise<MigrationResult> {
    const { dryRun = false, batchSize = 50, logProgress = true } = options;

    this.logger.log(`Starting migration: ${this.getName()}`);
    this.logger.log(`Description: ${this.getDescription()}`);
    this.logger.log(`Options: dryRun=${dryRun}, batchSize=${batchSize}`);

    try {
      // Step 1: Find all rules that need migration
      const rulesToMigrate = await this.findRulesWithTooManyMitreAttacks();

      if (rulesToMigrate.length === 0) {
        this.logger.log('No rules found that need migration');
        return this.createResult(0, 0);
      }

      this.logger.log(
        `Found ${rulesToMigrate.length} rules that need migration`,
      );

      if (dryRun) {
        this.logDryRun(`Would process ${rulesToMigrate.length} rules:`);
        for (const rule of rulesToMigrate) {
          this.logDryRun(
            `- ${rule.id}: "${rule.title}" (${rule.metadata?.mitre_attack?.length || 0} MITRE objects)`,
          );
        }

        // Log example for dry run
        if (rulesToMigrate.length > 0) {
          const exampleRuleForDryRun = rulesToMigrate[0];
          this.logDryRun(
            `--- Dry Run: Example Rule Transformation (ID: ${exampleRuleForDryRun.id}, Title: "${exampleRuleForDryRun.title}") ---`,
          );
          this.logDryRun('Before migration (Current MITRE Attack details):');
          this.logDryRun(
            JSON.stringify(
              exampleRuleForDryRun.metadata?.mitre_attack || [],
              null,
              2,
            ),
          );

          try {
            const parseResult = await this.parsingService.parseRule({
              content: exampleRuleForDryRun.content,
              fileName:
                exampleRuleForDryRun.title || `rule-${exampleRuleForDryRun.id}`,
              ruleType: exampleRuleForDryRun.rule_type,
            });
            const newMitreDataForDryRun =
              parseResult.success && parseResult.metadata
                ? parseResult.metadata.mitre_attack
                : undefined;
            this.logDryRun(
              'After migration (Simulated MITRE Attack details if processed):',
            );
            this.logDryRun(
              JSON.stringify(newMitreDataForDryRun || [], null, 2),
            );
            if (!parseResult.success) {
              this.logDryRun(
                `Note: Parsing simulation failed for this rule: ${parseResult.error || 'Unknown parsing error'}`,
              );
            }
          } catch (error) {
            this.logDryRun(
              `Note: Error during parsing simulation for this rule: ${error instanceof Error ? error.message : String(error)}`,
            );
          }
          this.logDryRun('--- End of Dry Run Example ---');
        }

        return this.createResult(rulesToMigrate.length, rulesToMigrate.length);
      }

      // Step 2: Process rules in batches
      const errors: Array<{ id: string; error: string }> = [];
      let affectedCount = 0;
      let processedCount = 0;

      for (let i = 0; i < rulesToMigrate.length; i += batchSize) {
        const batch = rulesToMigrate.slice(i, i + batchSize);

        if (logProgress) {
          this.logProgress(
            processedCount,
            rulesToMigrate.length,
            'Migrating rules',
          );
        }

        for (const rule of batch) {
          try {
            const wasUpdated = await this.migrateRule(rule);
            if (wasUpdated) {
              affectedCount++;
            }
            processedCount++;
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            errors.push({ id: rule.id, error: errorMessage });
            this.logError(rule.id, error);
            processedCount++;
          }
        }
      }

      if (logProgress) {
        this.logProgress(
          processedCount,
          rulesToMigrate.length,
          'Migration completed',
        );
      }

      this.logger.log(
        `Migration completed: ${affectedCount} rules updated, ${errors.length} errors`,
      );

      return this.createResult(affectedCount, processedCount, errors);
    } catch (error) {
      this.logger.error('Migration failed with error:', error);
      throw error;
    }
  }

  /**
   * Find all rules that have 9 or more MITRE attack objects
   */
  private async findRulesWithTooManyMitreAttacks(): Promise<Rule[]> {
    this.logger.log('Scanning all rules for MITRE attack objects...');

    // Get all rules from the repository using scan
    const allStoredRules = await this.scanAllRules();

    // Convert to Rule DTOs and filter
    const allRules = allStoredRules.map((rule) =>
      this.mapStoredRuleToRule(rule),
    );

    const filteredRules = allRules.filter((rule) => {
      const mitreAttackCount = rule.metadata?.mitre_attack?.length || 0;
      return mitreAttackCount >= this.MITRE_ATTACK_THRESHOLD;
    });

    this.logger.log(
      `Found ${filteredRules.length} rules with ${this.MITRE_ATTACK_THRESHOLD}+ MITRE attack objects out of ${allRules.length} total rules`,
    );

    return filteredRules;
  }

  /**
   * Scan all rules from DynamoDB using pagination
   */
  private async scanAllRules(): Promise<StoredRule[]> {
    const scanParams: ScanCommandInput = {
      TableName: this.ruleRepository['tableName'], // Access protected property
    };

    let allRules: StoredRule[] = [];
    let lastEvaluatedKey: Record<string, any> | undefined = undefined;

    do {
      // If we have a LastEvaluatedKey from a previous scan, use it to continue
      if (lastEvaluatedKey) {
        scanParams.ExclusiveStartKey = lastEvaluatedKey;
      }

      const command = new ScanCommand(scanParams);
      const response =
        await this.ruleRepository['getDocumentClient']().send(command);

      // Append the new items to our accumulated results
      const items = (response.Items || []) as StoredRule[];
      allRules = [...allRules, ...items];

      // Update the LastEvaluatedKey for the next iteration
      lastEvaluatedKey = response.LastEvaluatedKey;

      this.logger.debug(
        `Scanned ${items.length} rules, total so far: ${allRules.length}`,
      );
    } while (lastEvaluatedKey);

    this.logger.log(`Completed scan: found ${allRules.length} total rules`);
    return allRules;
  }

  /**
   * Migrate a single rule by re-parsing its content
   */
  private async migrateRule(rule: Rule): Promise<boolean> {
    try {
      this.logger.debug(`Migrating rule ${rule.id}: "${rule.title}"`);

      const originalMitreCount = rule.metadata?.mitre_attack?.length || 0;
      const originalMitreDataForExample: MitreAttackObject[] | null =
        !this.exampleLogged && rule.metadata?.mitre_attack
          ? (JSON.parse(
              JSON.stringify(rule.metadata.mitre_attack),
            ) as MitreAttackObject[])
          : null;

      // Re-parse the rule using ParsingService
      const parseResult = await this.parsingService.parseRule({
        content: rule.content,
        fileName: rule.title || `rule-${rule.id}`,
        ruleType: rule.rule_type,
      });

      if (!parseResult.success || !parseResult.metadata) {
        this.logger.warn(
          `Failed to parse rule ${rule.id}: ${parseResult.error || 'No metadata returned'}`,
        );
        return false;
      }

      const newMitreCount = parseResult.metadata.mitre_attack?.length || 0;

      if (newMitreCount === originalMitreCount) {
        this.logger.debug(
          `Rule ${rule.id}: MITRE count unchanged (${originalMitreCount}), skipping update`,
        );
        return false;
      }

      if (!this.exampleLogged) {
        this.logger.log(
          `--- Example Rule Transformation (ID: ${rule.id}, Title: "${rule.title}") ---`,
        );
        this.logger.log('Before migration (MITRE Attack details):');
        this.logger.log(
          JSON.stringify(originalMitreDataForExample || [], null, 2),
        );
        this.logger.log(
          'After migration - New MITRE Attack data from parsing (to be applied):',
        );
        this.logger.log(
          JSON.stringify(parseResult.metadata.mitre_attack || [], null, 2),
        );
        this.logger.log('--- End of Example ---');
        this.exampleLogged = true;
      }

      this.logger.log(
        `Rule ${rule.id}: MITRE data changed. Original count: ${originalMitreCount}, New count: ${newMitreCount}. Proceeding with update.`,
        undefined,
      );

      const updateDto: UpdateRuleDto = {
        metadata: {
          ...rule.metadata,
          ...parseResult.metadata,
          mitre_attack: parseResult.metadata.mitre_attack,
        },
      };

      const groupNameForUpdate =
        rule.status === RuleStatus.PUBLISHED ? rule.group_name : undefined;

      await this.rulesService.update(rule.id, updateDto, groupNameForUpdate);

      this.logger.log(
        `Successfully updated rule ${rule.id} via RulesService: MITRE objects changed from ${originalMitreCount} to ${newMitreCount}`,
      );

      return true;
    } catch (error) {
      this.logger.error(`Error migrating rule ${rule.id}:`, error);
      throw error;
    }
  }

  /**
   * Map StoredRule to Rule DTO (simplified version of the mapping in RulesService)
   */
  private mapStoredRuleToRule(storedRule: StoredRule): Rule {
    return {
      id: storedRule.id,
      title: storedRule.title || '',
      description: storedRule.description || '',
      ai_generated: {
        description: storedRule.ai_generated?.description || false,
        title: storedRule.ai_generated?.title || false,
        content: storedRule.ai_generated?.content || false,
        tags: storedRule.ai_generated?.tags || false,
      },
      rule_type: storedRule.rule_type,
      content: storedRule.content,
      tags: storedRule.tags,
      metadata: storedRule.metadata,
      test_cases: storedRule.test_cases,
      created_at: new Date(Number(storedRule.created_at)).toISOString(),
      updated_at: new Date(Number(storedRule.updated_at)).toISOString(),
      published_at: storedRule.published_at
        ? new Date(Number(storedRule.published_at)).toISOString()
        : undefined,
      group_id:
        storedRule.owner_id !== storedRule.created_by
          ? storedRule.owner_id
          : undefined,
      group_name: storedRule.group_name ?? undefined,
      status: storedRule.status,
      created_by: storedRule.created_by,
      version: storedRule.version,
      contributor: storedRule.contributor,
      likes: storedRule.likes || 0,
      downloads: storedRule.downloads || 0,
      dislikes: storedRule.dislikes || 0,
      bookmarks: storedRule.bookmarks || 0,
    };
  }
}
