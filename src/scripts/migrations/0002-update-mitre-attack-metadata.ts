import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>an<PERSON>ommand, ScanCommandInput } from '@aws-sdk/lib-dynamodb';
import {
  BaseMigration,
  MigrationOptions,
  MigrationResult,
} from './base-migration';
import { RulesService } from '../../rules/rules.service';
import { RuleRepository } from '../../rules/repositories/rule.repository';
import { ParsingService } from '../../parsing/parsing.service';
import { MitreSearchService } from '../../mitre/services/mitre-search.service';
import { Rule, UpdateRuleDto, RuleStatus } from '../../rules/models/rule.model';
import { StoredRule } from '../../rules/schemas/rule.schema';
import { MitreAttackObject } from '../../rules/models/rule-metadata.model';
import { cleanMitreAttackMetadata } from '../../mitre/utils/mitre-metadata.utils';

type RuleAnalysis = {
  needsCleaning: boolean;
  needsValidation: boolean;
  needsGeneration: boolean;
  cleanedData?: MitreAttackObject[];
  validatedData?: MitreAttackObject[];
  generatedData?: MitreAttackObject[];
};

@Injectable()
export class UpdateMitreAttackMetadataMigration extends BaseMigration {
  protected readonly logger: Logger;
  private exampleLogged = false;
  private readonly ALLOWED_MITRE_FIELDS = [
    'id',
    'mitre_id',
    'name',
    'parent_name',
    'type',
  ];

  constructor(
    private readonly rulesService: RulesService,
    private readonly ruleRepository: RuleRepository,
    private readonly mitreSearchService: MitreSearchService,
    private readonly parsingService: ParsingService,
  ) {
    super(new Logger(UpdateMitreAttackMetadataMigration.name));
  }

  getName(): string {
    return '0002-update-mitre-attack-metadata';
  }

  getDescription(): string {
    return 'Update and validate MITRE attack metadata for all rules: clean fields, validate STIX IDs, repair invalid data, and generate missing MITRE data using parsing service';
  }

  async execute(options: MigrationOptions = {}): Promise<MigrationResult> {
    const { dryRun = false, batchSize = 50, logProgress = true } = options;

    this.logger.log(`Starting migration: ${this.getName()}`);
    this.logger.log(`Description: ${this.getDescription()}`);
    this.logger.log(`Options: dryRun=${dryRun}, batchSize=${batchSize}`);

    try {
      // Step 1: Find all rules
      const allRules = await this.scanAllRules();

      if (allRules.length === 0) {
        this.logger.log('No rules found in the database');
        return this.createResult(0, 0);
      }

      this.logger.log(`Found ${allRules.length} total rules to process`);

      if (dryRun) {
        await this.performDryRun(allRules);
        return this.createResult(allRules.length, allRules.length);
      }

      // Step 2: Process rules in batches
      const errors: Array<{ id: string; error: string }> = [];
      let affectedCount = 0;
      let processedCount = 0;

      for (let i = 0; i < allRules.length; i += batchSize) {
        const batch = allRules.slice(i, i + batchSize);

        if (logProgress) {
          this.logProgress(processedCount, allRules.length, 'Processing rules');
        }

        for (const rule of batch) {
          try {
            const wasUpdated = await this.migrateRule(rule);
            if (wasUpdated) {
              affectedCount++;
            }
            processedCount++;
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            errors.push({ id: rule.id, error: errorMessage });
            this.logError(rule.id, error);
            processedCount++;
          }
        }
      }

      if (logProgress) {
        this.logProgress(
          processedCount,
          allRules.length,
          'Migration completed',
        );
      }

      this.logger.log(
        `Migration completed: ${affectedCount} rules updated, ${errors.length} errors`,
      );

      return this.createResult(affectedCount, processedCount, errors);
    } catch (error) {
      this.logger.error('Migration failed with error:', error);
      throw error;
    }
  }

  /**
   * Perform dry run analysis
   */
  private async performDryRun(allRules: Rule[]): Promise<void> {
    this.logDryRun(`Would process ${allRules.length} rules`);

    let rulesNeedingCleaning = 0;
    let rulesNeedingValidation = 0;
    let rulesNeedingGeneration = 0;
    let exampleShown = false;

    for (const rule of allRules.slice(0, Math.min(100, allRules.length))) {
      const analysis = await this.analyzeRuleForChanges(rule);

      if (analysis.needsCleaning) rulesNeedingCleaning++;
      if (analysis.needsValidation) rulesNeedingValidation++;
      if (analysis.needsGeneration) rulesNeedingGeneration++;

      // Show first example
      if (
        !exampleShown &&
        (analysis.needsCleaning ||
          analysis.needsValidation ||
          analysis.needsGeneration)
      ) {
        this.logDryRunExample(rule, analysis);
        exampleShown = true;
      }
    }

    this.logDryRun(`Summary from sample analysis:`);
    this.logDryRun(`- Rules needing field cleaning: ${rulesNeedingCleaning}`);
    this.logDryRun(
      `- Rules needing STIX ID validation: ${rulesNeedingValidation}`,
    );
    this.logDryRun(
      `- Rules needing MITRE data generation: ${rulesNeedingGeneration}`,
    );
  }

  /**
   * Analyze what changes a rule needs
   */
  private async analyzeRuleForChanges(rule: Rule): Promise<RuleAnalysis> {
    const mitreAttack = rule.metadata?.mitre_attack || [];

    // Check if generation is needed
    const needsGeneration = mitreAttack.length === 0;

    // Check if cleaning is needed
    const needsCleaning = mitreAttack.some((obj) =>
      Object.keys(obj).some((key) => !this.ALLOWED_MITRE_FIELDS.includes(key)),
    );

    // Check if validation is needed
    const needsValidation = mitreAttack.some((obj) => obj.id);

    let cleanedData: MitreAttackObject[] | undefined;
    let validatedData: MitreAttackObject[] | undefined;
    let generatedData: MitreAttackObject[] | undefined;

    if (needsGeneration) {
      try {
        const parseResult = await this.parsingService.parseRule({
          content: rule.content,
          fileName: rule.title || `rule-${rule.id}`,
          ruleType: rule.rule_type,
        });
        if (parseResult.success && parseResult.metadata?.mitre_attack) {
          generatedData = parseResult.metadata.mitre_attack;
        }
      } catch (error) {
        // Log error and rule content during dry run analysis
        this.logger.warn(
          `Failed to parse rule during analysis (ID: ${rule.id}, Title: "${rule.title}"): ${error instanceof Error ? error.message : String(error)}`,
        );
        this.logger.debug(`Problematic rule content for ID ${rule.id}:`);
        this.logger.debug(rule.content);
        // Ignore errors during dry run analysis for now, but log them
      }
    }

    if (needsCleaning) {
      cleanedData = mitreAttack.map((obj) => this.cleanMitreAttackObject(obj));
    }

    if (needsValidation) {
      validatedData = await this.validateAndRepairMitreData(mitreAttack);
    }

    return {
      needsCleaning,
      needsValidation,
      needsGeneration,
      cleanedData,
      validatedData,
      generatedData,
    };
  }

  /**
   * Create a simulated rule object after migration would be applied
   */
  private createRuleAfterMigration(rule: Rule, analysis: RuleAnalysis): Rule {
    let finalMitreAttack: MitreAttackObject[] = [];

    if (analysis.needsGeneration && analysis.generatedData) {
      // Rule would get generated MITRE data
      finalMitreAttack = analysis.generatedData;
    } else {
      // Start with existing data and apply cleaning/validation
      finalMitreAttack = rule.metadata?.mitre_attack || [];

      if (analysis.needsCleaning && analysis.cleanedData) {
        finalMitreAttack = analysis.cleanedData;
      }

      if (analysis.needsValidation && analysis.validatedData) {
        finalMitreAttack = analysis.validatedData;
      }
    }

    // Return a copy of the rule with updated MITRE data
    return {
      ...rule,
      metadata: {
        ...rule.metadata,
        mitre_attack: finalMitreAttack,
      },
    };
  }

  /**
   * Log dry run example
   */
  private logDryRunExample(rule: Rule, analysis: RuleAnalysis): void {
    this.logDryRun(
      `--- Example Rule Transformation (ID: ${rule.id}, Title: "${rule.title}") ---`,
    );

    // Log complete rule data BEFORE changes
    this.logDryRun('COMPLETE RULE DATA BEFORE MIGRATION:');
    this.logDryRun(JSON.stringify(rule, null, 2));

    if (analysis.needsGeneration) {
      this.logDryRun('GENERATION NEEDED: Rule has no MITRE attack data');
      if (analysis.generatedData) {
        this.logDryRun('Generated data preview:');
        this.logDryRun(JSON.stringify(analysis.generatedData, null, 2));
      }
    }

    if (analysis.needsCleaning) {
      this.logDryRun('CLEANING NEEDED: Rule has extra fields in MITRE objects');
      this.logDryRun('Before cleaning:');
      this.logDryRun(
        JSON.stringify(rule.metadata?.mitre_attack || [], null, 2),
      );
      if (analysis.cleanedData) {
        this.logDryRun('After cleaning:');
        this.logDryRun(JSON.stringify(analysis.cleanedData, null, 2));
      }
    }

    if (analysis.needsValidation) {
      this.logDryRun(
        'VALIDATION NEEDED: Rule has STIX IDs that need verification',
      );
      if (analysis.validatedData) {
        this.logDryRun('After validation/repair:');
        this.logDryRun(JSON.stringify(analysis.validatedData, null, 2));
      }
    }

    // Log complete rule data AFTER changes would be applied
    if (
      analysis.needsGeneration ||
      analysis.needsCleaning ||
      analysis.needsValidation
    ) {
      const ruleAfterMigration = this.createRuleAfterMigration(rule, analysis);
      this.logDryRun('COMPLETE RULE DATA AFTER MIGRATION:');
      this.logDryRun(JSON.stringify(ruleAfterMigration, null, 2));
    }

    this.logDryRun('--- End of Example ---');
  }

  /**
   * Scan all rules from DynamoDB using pagination
   */
  private async scanAllRules(): Promise<Rule[]> {
    const scanParams: ScanCommandInput = {
      TableName: this.ruleRepository['tableName'], // Access protected property
    };

    let allRules: StoredRule[] = [];
    let lastEvaluatedKey: Record<string, any> | undefined = undefined;

    do {
      if (lastEvaluatedKey) {
        scanParams.ExclusiveStartKey = lastEvaluatedKey;
      }

      const command = new ScanCommand(scanParams);
      const response =
        await this.ruleRepository['getDocumentClient']().send(command);

      const items = (response.Items || []) as StoredRule[];
      allRules = [...allRules, ...items];
      lastEvaluatedKey = response.LastEvaluatedKey;

      this.logger.debug(
        `Scanned ${items.length} rules, total so far: ${allRules.length}`,
      );
    } while (lastEvaluatedKey);

    this.logger.log(`Completed scan: found ${allRules.length} total rules`);
    return allRules.map((rule) => this.mapStoredRuleToRule(rule));
  }

  /**
   * Migrate a single rule
   */
  private async migrateRule(rule: Rule): Promise<boolean> {
    try {
      this.logger.debug(`Migrating rule ${rule.id}: "${rule.title}"`);

      const originalMitreAttack = rule.metadata?.mitre_attack || [];
      let updatedMitreAttack: MitreAttackObject[] = [];
      let hasChanges = false;

      // Case 1: No MITRE data - try to generate it
      if (originalMitreAttack.length === 0) {
        this.logger.debug(
          `Rule ${rule.id}: No MITRE data, attempting to generate`,
        );

        const parseResult = await this.parsingService.parseRule({
          content: rule.content,
          fileName: rule.title || `rule-${rule.id}`,
          ruleType: rule.rule_type,
        });

        if (
          parseResult.success &&
          parseResult.metadata?.mitre_attack &&
          parseResult.metadata.mitre_attack.length > 0
        ) {
          updatedMitreAttack = parseResult.metadata.mitre_attack;
          hasChanges = true;

          this.logger.log(
            `Rule ${rule.id}: Generated ${updatedMitreAttack.length} MITRE objects from parsing`,
          );
        } else {
          this.logger.debug(
            `Rule ${rule.id}: Could not generate MITRE data from parsing`,
          );
          return false;
        }
      } else {
        // Case 2: Has MITRE data - clean and validate it
        this.logger.debug(
          `Rule ${rule.id}: Processing existing ${originalMitreAttack.length} MITRE objects`,
        );

        // Step 1: Clean objects (remove extra fields)
        const cleanedObjects = originalMitreAttack.map((obj) =>
          this.cleanMitreAttackObject(obj),
        );

        // Step 2: Validate and repair STIX IDs
        const validatedObjects =
          await this.validateAndRepairMitreData(cleanedObjects);

        // Check if anything changed
        if (
          JSON.stringify(originalMitreAttack) !==
          JSON.stringify(validatedObjects)
        ) {
          updatedMitreAttack = validatedObjects;
          hasChanges = true;
        }
      }

      if (!hasChanges) {
        this.logger.debug(`Rule ${rule.id}: No changes needed`);
        return false;
      }

      // Log example transformation
      if (!this.exampleLogged) {
        this.logger.log(
          `--- Example Rule Transformation (ID: ${rule.id}, Title: "${rule.title}") ---`,
        );
        this.logger.log('Before migration:');
        this.logger.log(JSON.stringify(originalMitreAttack, null, 2));
        this.logger.log('After migration:');
        this.logger.log(JSON.stringify(updatedMitreAttack, null, 2));
        this.logger.log('--- End of Example ---');
        this.exampleLogged = true;
      }

      // Update the rule
      const updateDto: UpdateRuleDto = {
        metadata: {
          ...rule.metadata,
          mitre_attack: updatedMitreAttack,
        },
      };

      const groupNameForUpdate =
        rule.status === RuleStatus.PUBLISHED ? rule.group_name : undefined;

      await this.rulesService.update(rule.id, updateDto, groupNameForUpdate);

      this.logger.log(
        `Successfully updated rule ${rule.id}: MITRE objects changed from ${originalMitreAttack.length} to ${updatedMitreAttack.length}`,
      );

      return true;
    } catch (error) {
      this.logger.error(`Error migrating rule ${rule.id}:`, error);
      throw error;
    }
  }

  /**
   * Clean a MITRE attack object by keeping only allowed fields
   */
  private cleanMitreAttackObject(obj: any): MitreAttackObject {
    // Use the utility function to clean a single object by wrapping it in metadata
    const tempMetadata = { mitre_attack: [obj] };
    const cleaned = cleanMitreAttackMetadata(tempMetadata);
    return (cleaned?.mitre_attack?.[0] || {}) as MitreAttackObject;
  }

  /**
   * Validate and repair MITRE data by checking STIX IDs
   */
  private async validateAndRepairMitreData(
    objects: MitreAttackObject[],
  ): Promise<MitreAttackObject[]> {
    const validatedObjects: MitreAttackObject[] = [];

    for (const obj of objects) {
      let validatedObj = { ...obj };

      if (obj.id) {
        try {
          // Try to validate the STIX ID
          const foundObject = await this.mitreSearchService.getObjectByStixId(
            obj.id,
            this.ALLOWED_MITRE_FIELDS,
          );

          if (foundObject) {
            // STIX ID is valid, use the found data to ensure consistency
            validatedObj = this.cleanMitreAttackObject(foundObject);
            this.logger.debug(
              `Validated STIX ID ${obj.id}: ${foundObject.name}`,
            );
          } else if (obj.mitre_id) {
            // STIX ID is invalid, try to find by MITRE ID
            this.logger.debug(
              `Invalid STIX ID ${obj.id}, trying fallback lookup by MITRE ID ${obj.mitre_id}`,
            );

            const fallbackObject =
              await this.mitreSearchService.getObjectByUniqueMitreId(
                obj.mitre_id,
                this.ALLOWED_MITRE_FIELDS,
              );

            if (fallbackObject) {
              validatedObj = this.cleanMitreAttackObject(fallbackObject);
              this.logger.log(
                `Repaired MITRE object: ${obj.id} -> ${fallbackObject.id} (${fallbackObject.name})`,
              );
            } else {
              this.logger.warn(
                `Could not validate or repair MITRE object with STIX ID ${obj.id} and MITRE ID ${obj.mitre_id}`,
              );
            }
          } else {
            this.logger.warn(
              `Invalid STIX ID ${obj.id} and no MITRE ID available for repair`,
            );
          }
        } catch (error) {
          this.logger.warn(
            `Error validating STIX ID ${obj.id}: ${error instanceof Error ? error.message : String(error)}`,
          );
        }
      }

      validatedObjects.push(validatedObj);
    }

    return validatedObjects;
  }

  /**
   * Map StoredRule to Rule DTO (simplified version of the mapping in RulesService)
   */
  private mapStoredRuleToRule(storedRule: StoredRule): Rule {
    return {
      id: storedRule.id,
      title: storedRule.title || '',
      description: storedRule.description || '',
      ai_generated: {
        description: storedRule.ai_generated?.description || false,
        title: storedRule.ai_generated?.title || false,
        content: storedRule.ai_generated?.content || false,
        tags: storedRule.ai_generated?.tags || false,
      },
      rule_type: storedRule.rule_type,
      content: storedRule.content,
      tags: storedRule.tags,
      metadata: storedRule.metadata,
      test_cases: storedRule.test_cases,
      created_at: new Date(Number(storedRule.created_at)).toISOString(),
      updated_at: new Date(Number(storedRule.updated_at)).toISOString(),
      published_at: storedRule.published_at
        ? new Date(Number(storedRule.published_at)).toISOString()
        : undefined,
      group_id:
        storedRule.owner_id !== storedRule.created_by
          ? storedRule.owner_id
          : undefined,
      group_name: storedRule.group_name ?? undefined,
      status: storedRule.status,
      created_by: storedRule.created_by,
      version: storedRule.version,
      contributor: storedRule.contributor,
      likes: storedRule.likes || 0,
      downloads: storedRule.downloads || 0,
      dislikes: storedRule.dislikes || 0,
      bookmarks: storedRule.bookmarks || 0,
    };
  }
}
