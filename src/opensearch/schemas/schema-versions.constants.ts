/**
 * Constants for OpenSearch schema versions
 *
 * Version history:
 * - v1: Initial schema
 * - v2: Added published_at field, fixed downloads/likes field mapping
 * - v3: Added analyzer fix
 * - v4: Added ai_description field to track AI-generated descriptions
 * - v5: Added ai_title field to track AI-generated titles
 * - v6: Updated metadata.mitre_attack mapping (relationships, external_references).
 * - v7: Added metadata.date, metadata.modified fields; enabled new sort options.
 * - v8: Enhanced text field mappings (title, description, content, all_text) with .keyword for exact search; updated data_sources and logsource mappings.
 * - v13: Made all metadata.mitre_attack keyword fields case insensitive by adding keyword_normalizer.
 */
export const OPENSEARCH_SCHEMA_VERSIONS = {
  /** Initial schema version */
  INITIAL: 1,

  /** Previous schema version */
  PREVIOUS: 12,

  /** Current schema version - update this when making schema changes */
  CURRENT: 13,

  /** TRACK VERSION HISTORY */
  /** Added published_at field, fixed downloads/likes field mapping */
  PUBLISHED_AT: 2,
  /** Added analyzer fix */
  ANALYZER_FIX: 3,
  /** Added ai_description field to track AI-generated descriptions */
  AI_DESCRIPTION_TRACKING: 4,
  /** Added ai_title field to track AI-generated titles */
  AI_TITLE_TRACKING: 5,
  /** Updated metadata.mitre_attack mapping (relationships, external_references) */
  MITRE_ATTACK_MAPPING_V6: 6,
  /** Added metadata.date, metadata.modified; new sort options */
  METADATA_DATES_AND_SORTING_V7: 7,
  /** Enhanced text field mappings with .keyword for exact search; updated data_sources and logsource mappings. */
  ENHANCED_TEXT_SEARCH_V8: 8,
  /** Removed content.keyword sub-field to prevent immense term errors. */
  CONTENT_KEYWORD_REMOVAL_V9: 9,
  /** Aligned all_text search with content by removing its .keyword sub-field and using match query for identifiers. */
  ALL_TEXT_CONTENT_ALIGNMENT_V10: 10,
  /** Added metadata.categories field to track rule categories, platforms, product_services */
  CATEGORIES_PLATFORMS_PRODUCT_SERVICES_TRACKING_V11: 11,
  /** Added edge n-gram filter and analyzer for partial word matching. */
  EDGE_NGRAM_PARTIAL_MATCH_V12: 12,
  /** Made all metadata.mitre_attack keyword fields case insensitive by adding keyword_normalizer. */
  MITRE_ATTACK_CASE_INSENSITIVE_V13: 13,
};

export const MITRE_OPENSEARCH_SCHEMA_VERSIONS = {
  INITIAL: 1,

  PREVIOUS: 2,

  CURRENT: 3,

  MITRE_ATTACK_MAPPING_V17_1: 1,
  FIXED_MITIGATION_MAPPING: 2,
  CASE_INSENSITIVE_ANALYZERS_V3: 3,
};
