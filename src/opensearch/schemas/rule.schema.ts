import { OpenSearchIndexSchema, IndexSchemaDefinition } from './schema.base';
import { Injectable } from '@nestjs/common';
import { OpenSearchConfigService } from '../config/opensearch.config';
import { getAllSynonyms } from '../config/detection-synonyms';
import { OPENSEARCH_SCHEMA_VERSIONS } from './schema-versions.constants';

/**
 * Schema for the detection rules index
 */
@Injectable()
export class RuleIndexSchema extends OpenSearchIndexSchema {
  constructor(private readonly configService: OpenSearchConfigService) {
    super();
  }

  /**
   * Get the base index name without prefix
   */
  private get baseIndexName(): string {
    return 'detection_rules';
  }

  /**
   * Get the base alias name without prefix
   */
  private get baseAliasName(): string {
    return 'detection_rules';
  }

  /**
   * Get the index name without environment prefix
   */
  get indexName(): string {
    return `${this.baseIndexName}_v${this.version}`;
  }

  /**
   * Get the index alias without environment prefix
   */
  get indexAlias(): string {
    return this.baseAliasName;
  }

  /**
   * Get the schema version
   */
  get version(): number {
    return OPENSEARCH_SCHEMA_VERSIONS.CURRENT;
  }

  /**
   * Get the versioned index name without environment prefix
   */
  getVersionedIndexName(): string {
    return `${this.baseIndexName}_v${this.version}`;
  }

  /**
   * Get the index schema definition
   */
  get schema(): IndexSchemaDefinition {
    return {
      settings: {
        index: {
          number_of_shards: 3,
          number_of_replicas: 1,
          refresh_interval: '5s',
          'mapping.total_fields.limit': 2000,
          analysis: {
            char_filter: {
              html_strip: {
                type: 'html_strip',
              },
            },
            filter: {
              english_stop: {
                type: 'stop',
                stopwords: '_english_',
              },
              english_stemmer: {
                type: 'stemmer',
                language: 'english',
              },
              english_possessive_stemmer: {
                type: 'stemmer',
                language: 'possessive_english',
              },
              detection_synonyms: {
                type: 'synonym_graph',
                synonyms: getAllSynonyms(),
                lenient: true,
                expand: false,
              },
              partial_match_edge_ngram: {
                type: 'edge_ngram',
                min_gram: 3,
                max_gram: 15,
              },
              security_word_delimiter: {
                type: 'word_delimiter_graph',
                split_on_numerics: true,
                split_on_case_change: true,
                preserve_original: true,
                generate_word_parts: true,
                generate_number_parts: true,
                catenate_words: false,
                catenate_numbers: false,
                catenate_all: false,
              },
              shingles_filter: {
                type: 'shingle',
                min_shingle_size: 2,
                max_shingle_size: 3,
                output_unigrams: true,
              },
            },
            analyzer: {
              default_search: {
                type: 'custom',
                tokenizer: 'standard',
                filter: [
                  'lowercase',
                  'english_possessive_stemmer',
                  'english_stop',
                  'english_stemmer',
                  'security_word_delimiter',
                ],
              },
              text_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                char_filter: ['html_strip'],
                filter: [
                  'lowercase',
                  'english_possessive_stemmer',
                  'english_stop',
                  'english_stemmer',
                  'security_word_delimiter',
                ],
              },
              simple_text_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                char_filter: ['html_strip'],
                filter: [
                  'lowercase',
                  'english_possessive_stemmer',
                  'english_stop',
                  'english_stemmer',
                  'security_word_delimiter',
                ],
              },
              query_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                char_filter: ['html_strip'],
                filter: [
                  'lowercase',
                  'english_possessive_stemmer',
                  'english_stop',
                  'english_stemmer',
                ],
              },
              synonym_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                char_filter: ['html_strip'],
                filter: [
                  'lowercase',
                  'english_possessive_stemmer',
                  'english_stop',
                  'english_stemmer',
                  'detection_synonyms',
                ],
              },
              shingle_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'english_stop', 'shingles_filter'],
              },
              edge_ngram_analyzer_custom: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'partial_match_edge_ngram'],
              },
            },
            normalizer: {
              keyword_normalizer: {
                type: 'custom',
                filter: ['lowercase'],
              },
            },
          },
        },
      },
      mappings: {
        dynamic: 'strict',
        properties: {
          id: {
            type: 'keyword',
          },
          title: {
            type: 'object',
            properties: {
              value: {
                type: 'text',
                analyzer: 'simple_text_analyzer',
                search_analyzer: 'synonym_analyzer',
                position_increment_gap: 100,
                fields: {
                  keyword: {
                    type: 'keyword',
                    normalizer: 'keyword_normalizer',
                  },
                  shingles: {
                    type: 'text',
                    analyzer: 'shingle_analyzer',
                  },
                  edge_ngram: {
                    type: 'text',
                    analyzer: 'edge_ngram_analyzer_custom',
                    search_analyzer: 'standard',
                  },
                },
                boost: 3.0,
              },
              suggest: {
                type: 'completion',
                contexts: [
                  {
                    name: 'rule_type',
                    type: 'category',
                  },
                  {
                    name: 'owner_id',
                    type: 'category',
                  },
                ],
              },
            },
          },
          description: {
            type: 'text',
            analyzer: 'simple_text_analyzer',
            search_analyzer: 'synonym_analyzer',
            fields: {
              shingles: {
                type: 'text',
                analyzer: 'shingle_analyzer',
              },
              edge_ngram: {
                type: 'text',
                analyzer: 'edge_ngram_analyzer_custom',
                search_analyzer: 'standard',
              },
              keyword: {
                type: 'keyword',
                normalizer: 'keyword_normalizer',
              },
            },
            boost: 2.0,
          },
          ai_generated: {
            type: 'object',
            properties: {
              description: {
                type: 'boolean',
              },
              title: {
                type: 'boolean',
              },
              content: {
                type: 'boolean',
              },
              tags: {
                type: 'boolean',
              },
            },
          },
          content: {
            type: 'text',
            analyzer: 'simple_text_analyzer',
            search_analyzer: 'synonym_analyzer',
            fields: {
              shingles: {
                type: 'text',
                analyzer: 'shingle_analyzer',
              },
              edge_ngram: {
                type: 'text',
                analyzer: 'edge_ngram_analyzer_custom',
                search_analyzer: 'standard',
              },
            },
            boost: 1.0,
          },
          contributor: {
            type: 'text',
            analyzer: 'simple_text_analyzer',
            search_analyzer: 'default_search',
            fields: {
              keyword: {
                type: 'keyword',
                normalizer: 'keyword_normalizer',
              },
            },
            boost: 1.0,
          },
          group_name: {
            type: 'text',
            analyzer: 'simple_text_analyzer',
            search_analyzer: 'default_search',
            fields: {
              keyword: {
                type: 'keyword',
                normalizer: 'keyword_normalizer',
              },
            },
          },
          likes: {
            type: 'integer',
          },
          downloads: {
            type: 'integer',
          },
          dislikes: {
            type: 'integer',
          },
          rule_type: {
            type: 'keyword',
          },
          version: {
            type: 'keyword',
          },
          stage: {
            type: 'keyword',
          },
          status: {
            type: 'keyword',
          },
          validation_status: {
            type: 'keyword',
          },
          created_by: {
            type: 'keyword',
          },
          created_at: {
            type: 'date',
            format: 'strict_date_time||epoch_millis',
          },
          updated_at: {
            type: 'date',
            format: 'strict_date_time||epoch_millis',
          },
          published_at: {
            type: 'date',
            format: 'strict_date_time||epoch_millis',
          },
          owner_id: {
            type: 'keyword',
          },
          metadata: {
            type: 'object',
            dynamic: 'true',
            properties: {
              mitre_attack: {
                type: 'nested',
                properties: {
                  id: { type: 'keyword', normalizer: 'keyword_normalizer' },
                  mitre_id: {
                    type: 'keyword',
                    normalizer: 'keyword_normalizer',
                  },
                  name: {
                    type: 'text',
                    analyzer: 'simple_text_analyzer',
                    fields: {
                      keyword: { type: 'keyword' },
                    },
                  },
                  description: {
                    type: 'text',
                    analyzer: 'simple_text_analyzer',
                  },
                  type: { type: 'keyword', normalizer: 'keyword_normalizer' },
                  parent_name: {
                    type: 'keyword',
                    normalizer: 'keyword_normalizer',
                  },
                  url: { type: 'keyword', normalizer: 'keyword_normalizer' },
                  external_references: {
                    type: 'keyword',
                    normalizer: 'keyword_normalizer',
                  },
                  relationships: {
                    type: 'nested',
                    properties: {
                      id: { type: 'keyword', normalizer: 'keyword_normalizer' },
                      description: {
                        type: 'text',
                        analyzer: 'simple_text_analyzer',
                      },
                      relationship_type: {
                        type: 'keyword',
                        normalizer: 'keyword_normalizer',
                      },
                      source_id: {
                        type: 'keyword',
                        normalizer: 'keyword_normalizer',
                      },
                      target_id: {
                        type: 'keyword',
                        normalizer: 'keyword_normalizer',
                      },
                    },
                  },
                },
              },
              mitre_tactics: {
                type: 'keyword',
                normalizer: 'keyword_normalizer',
              },
              mitre_techniques: {
                type: 'keyword',
                normalizer: 'keyword_normalizer',
              },
              severity: {
                type: 'keyword',
              },
              tags: {
                type: 'keyword',
              },
              data_sources: {
                type: 'keyword',
              },
              platforms: {
                type: 'keyword',
              },
              author: {
                type: 'text',
                analyzer: 'simple_text_analyzer',
                search_analyzer: 'synonym_analyzer',
                fields: {
                  keyword: {
                    type: 'keyword',
                    normalizer: 'keyword_normalizer',
                  },
                },
              },
              date: {
                type: 'date',
                format:
                  'strict_date_time||epoch_millis||yyyy/MM/dd||yyyy-MM-dd',
              },
              modified: {
                type: 'date',
                format:
                  'strict_date_time||epoch_millis||yyyy/MM/dd||yyyy-MM-dd',
              },
            },
          },
          all_text: {
            type: 'text',
            analyzer: 'simple_text_analyzer',
            search_analyzer: 'default_search',
            fields: {
              edge_ngram: {
                type: 'text',
                analyzer: 'edge_ngram_analyzer_custom',
                search_analyzer: 'standard',
              },
            },
          },
        },
      },
    };
  }
}
