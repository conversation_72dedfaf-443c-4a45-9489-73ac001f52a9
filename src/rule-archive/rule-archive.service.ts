import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Service } from '../s3/s3.service';
import { S3SignedUrlOperation } from '../s3/s3.types';
import {
  UploadUrlResponseDto,
  UpdateUploadStatusDto,
  UpdateUploadStatusResponseDto,
  UploadStatus,
  FileProcessingResult,
  FileType,
} from './rule-archive.types';
import { v4 as uuidv4 } from 'uuid';
import { DecompressService } from '../decompress/decompress.service';
import { RulesService } from '../rules/rules.service';
import { ParsingService } from '../parsing/parsing.service';
import { RuleUploadStatusService } from '../rule-upload-status/rule-upload-status.service';
import {
  RuleUploadProcessingStatus,
  CreateRuleUploadStatusDto,
} from '../rule-upload-status/models/rule-upload-status.model';
import { DecompressedFile } from '../decompress/decompress.types';
import {
  AllowedFileType,
  mapFileTypeToAllowedFileType,
} from '../utils/file-filtering.types';
import { Rule, RuleType } from '../rules/models/rule.model';
import { AiGenerationService } from '../ai-generation/ai-generation.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { timeout } from 'rxjs/operators';
import { LanguageDetectionResponse } from './interfaces/language-detection.interface';

@Injectable()
export class RuleArchiveService {
  private readonly logger = new Logger(RuleArchiveService.name);
  private readonly translationServiceUrl: string;
  private readonly timeoutMs: number;

  constructor(
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
    private readonly decompressService: DecompressService,
    private readonly rulesService: RulesService,
    private readonly parsingService: ParsingService,
    private readonly ruleUploadStatusService: RuleUploadStatusService,
    private readonly aiGenerationService: AiGenerationService,
    private readonly httpService: HttpService,
  ) {
    // Get translation service URL from configuration
    const serviceUrl = this.configService.get<string>(
      'TRANSLATION_SERVICE_URL',
    );
    if (!serviceUrl) {
      throw new Error(
        'TRANSLATION_SERVICE_URL environment variable is not defined',
      );
    }
    this.translationServiceUrl = serviceUrl;

    // Get timeout value from configuration with default
    this.timeoutMs =
      this.configService.get<number>('LANGUAGE_DETECTION_TIMEOUT_MS') || 10000;
  }

  /**
   * Generate a signed URL for PUT operation with environment configuration
   * @param fileType Type of file to upload (ZIP, YAML, YML, MD, TXT, JSON, or KQL)
   * @param extUserId External user ID who initiated the request
   */
  async generateUploadUrl(
    fileType: FileType = FileType.ZIP,
    extUserId: string,
  ): Promise<UploadUrlResponseDto> {
    try {
      this.logger.log(
        `Generating upload URL for file type: ${fileType}, user: ${extUserId}`,
      );

      // Get configuration from environment variables
      const bucket = this.configService.get<string>(
        'RULES_UPLOAD_S3_BUCKET_NAME',
      );
      if (!bucket) {
        this.logger.error(
          'RULES_UPLOAD_S3_BUCKET_NAME environment variable is not defined',
        );
        throw new InternalServerErrorException(
          'Upload S3 bucket configuration is missing',
        );
      }

      const expiresIn = parseInt(
        this.configService.get<string>('RULES_DOWNLOAD_URL_EXPIRY') || '3600',
        10,
      );

      // Generate a random UUID for the key
      const uuid = uuidv4();

      // Set the file extension based on the file type
      const extension = fileType.toLowerCase();
      const key = `rules/${uuid}.${extension}`;

      this.logger.log(`Generated S3 key: ${key} for bucket: ${bucket}`);

      const signedUrl = await this.s3Service.getSignedUrl(
        bucket,
        key,
        S3SignedUrlOperation.PUT,
        expiresIn,
      );

      this.logger.log(`Generated signed URL with expiry: ${expiresIn} seconds`);

      const expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + expiresIn);

      // Create a rule upload status entry with PENDING status
      const createDto: CreateRuleUploadStatusDto = {
        request_id: uuid,
        status: RuleUploadProcessingStatus.PENDING,
        metadata: {
          file_type: fileType,
          key: key,
          bucket: bucket,
          created_at: new Date().toISOString(),
        },
      };

      await this.ruleUploadStatusService.create(createDto, extUserId);
      this.logger.log(
        `Created rule upload status for ${uuid} with PENDING status`,
      );

      return {
        signed_url: signedUrl,
        expiresIn,
        expiresAt: expiresAt.toISOString(),
        id: uuid,
        file_type: fileType,
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate upload URL: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update the status of a rule upload and process the uploaded file if status is success
   * @param updateStatusDto The upload status update data
   * @param internalUserId Internal user ID who processed the upload
   * @param username Username or null
   * @param authHeader The authorization header for API calls
   * @returns The updated status information
   */
  async updateUploadStatus(
    updateStatusDto: UpdateUploadStatusDto,
    internalUserId: string,
    username: string | null,
    authHeader: string,
  ): Promise<UpdateUploadStatusResponseDto> {
    try {
      const { id, status } = updateStatusDto;

      this.logger.log(
        `Updating upload status for ID: ${id}, status: ${status}`,
      );
      this.logger.log(`Request from user: ${username} (${internalUserId})`);

      // Fetch the upload status record to get the file type
      const uploadStatus =
        await this.ruleUploadStatusService.findByRequestId(id);
      if (!uploadStatus) {
        this.logger.error(`Upload status record not found for ID: ${id}`);
        throw new NotFoundException(
          `Upload status record not found for ID: ${id}`,
        );
      }

      // Get file type from metadata
      const fileType = uploadStatus.metadata?.file_type as FileType;
      if (!fileType) {
        this.logger.error(`File type not found in metadata for ID: ${id}`);
        throw new InternalServerErrorException(
          `File type not found in metadata for ID: ${id}`,
        );
      }

      // Log metadata and file type details for debugging
      this.logger.debug(
        `Upload metadata for ID ${id}: ${JSON.stringify(uploadStatus.metadata)}`,
      );
      this.logger.debug(
        `Processing file of type: ${fileType} (exact value from metadata)`,
      );

      this.logger.log(`Found file type: ${fileType} for upload ID: ${id}`);

      // Only process for 'success' status
      if (status === UploadStatus.SUCCESS) {
        // Update DynamoDB status to PROCESSING
        await this.ruleUploadStatusService.update(id, {
          status: RuleUploadProcessingStatus.PROCESSING,
        });
        this.logger.log(`Updated rule upload status for ${id} to PROCESSING`);

        // Get the S3 bucket and key for the uploaded file
        const bucket = this.configService.get<string>(
          'RULES_UPLOAD_S3_BUCKET_NAME',
        );
        if (!bucket) {
          this.logger.error(
            'RULES_UPLOAD_S3_BUCKET_NAME environment variable is not defined',
          );
          throw new InternalServerErrorException(
            'Upload S3 bucket configuration is missing',
          );
        }

        // Set the file extension based on the file type from metadata
        const extension = fileType.toLowerCase();
        const key = `rules/${id}.${extension}`;

        this.logger.log(
          `Processing uploaded file from bucket: ${bucket}, key: ${key}`,
        );

        try {
          // Download the file from S3
          this.logger.log(`Downloading file from S3: ${bucket}/${key}`);
          const fileBuffer = (await this.s3Service.downloadFile(
            bucket,
            key,
          )) as Buffer;

          let processingResults: FileProcessingResult[] = [];

          // Process file based on the file type
          if (fileType === FileType.ZIP) {
            // Process ZIP file as before
            processingResults = await this.processZipFile(
              fileBuffer,
              internalUserId,
              username,
              authHeader,
            );
          } else {
            // Process any other file type
            processingResults = await this.processFile(
              fileBuffer,
              id,
              fileType,
              internalUserId,
              username,
              authHeader,
            );
          }

          // Create enhanced response with processing results
          const updatedAt = new Date().toISOString();

          // Update DynamoDB status to COMPLETED
          await this.ruleUploadStatusService.update(id, {
            status: RuleUploadProcessingStatus.COMPLETED,
            metadata: {
              processing_results: processingResults,
              processed_at: updatedAt,
              processed_files_count: processingResults.length,
              successful_files_count: processingResults.filter((r) => r.success)
                .length,
              failed_files_count: processingResults.filter((r) => !r.success)
                .length,
            },
          });
          this.logger.log(
            `Updated rule upload status for ${id} to COMPLETED with ${processingResults.length} processed files`,
          );

          this.logger.log(
            `Upload processing completed for ID: ${id}. Processed ${processingResults.length} files.`,
          );

          return {
            id,
            status,
            updatedAt,
            processingResults,
          };
        } catch (error) {
          this.logger.error(
            `Failed to process upload: ${error.message}`,
            error.stack,
          );

          // Update DynamoDB status to FAILED
          await this.ruleUploadStatusService.update(id, {
            status: RuleUploadProcessingStatus.FAILED,
            error_message: `Failed to process upload: ${error.message}`,
          });
          this.logger.log(`Updated rule upload status for ${id} to FAILED`);

          // Return failed status with error information
          const updatedAt = new Date().toISOString();
          return {
            id,
            status: UploadStatus.FAILED,
            updatedAt,
            processingResults: [
              {
                success: false,
                fileName: `upload.${fileType}`,
                error: `Failed to process upload: ${error.message}`,
              },
            ],
          };
        }
      } else if (status === UploadStatus.FAILED) {
        this.logger.warn(`Upload marked as failed by client for ID: ${id}`);
        // If client explicitly reports failure, update DynamoDB status to FAILED
        await this.ruleUploadStatusService.update(id, {
          status: RuleUploadProcessingStatus.FAILED,
          error_message: 'Upload marked as failed by client',
        });
        this.logger.log(
          `Updated rule upload status for ${id} to FAILED (marked by client)`,
        );
      } else if (status === UploadStatus.CANCELLED) {
        this.logger.warn(`Upload cancelled by user for ID: ${id}`);
        // For cancelled uploads, update DynamoDB with a FAILED status and cancelled message
        await this.ruleUploadStatusService.update(id, {
          status: RuleUploadProcessingStatus.FAILED,
          error_message: 'Upload cancelled by user',
        });
        this.logger.log(
          `Updated rule upload status for ${id} to FAILED (cancelled by user)`,
        );
      }

      // If not success status, just return the basic response
      const updatedAt = new Date().toISOString();
      this.logger.log(`Upload status updated for ID: ${id}, status: ${status}`);

      return {
        id,
        status,
        updatedAt,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update upload status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process a ZIP file containing rules
   * @param fileBuffer Buffer containing the ZIP file
   * @param internalUserId Internal user ID
   * @param username Username or null
   * @param authHeader The authorization header for API calls
   * @returns Array of file processing results
   */
  private async processZipFile(
    fileBuffer: Buffer,
    internalUserId: string,
    username: string | null,
    authHeader: string,
  ): Promise<FileProcessingResult[]> {
    // Decompress the file in memory
    this.logger.log('Decompressing the ZIP file');
    const decompressResult =
      await this.decompressService.decompressFromBuffer(fileBuffer);

    // Process each file
    const processingResults: FileProcessingResult[] = [];

    for (const file of decompressResult.files) {
      try {
        this.logger.log(`Processing file: ${file.entryName}`);

        // Read the file content from memory
        const fileContent = await this.decompressService.readFileContent(file);

        let ruleData;
        if (
          file.fileType === AllowedFileType.TEXT ||
          file.fileType === AllowedFileType.KQL ||
          file.fileType === AllowedFileType.JSON
        ) {
          // For TXT and KQL files, use simplified rule structure
          this.logger.log(`Processing as simple text file: ${file.entryName}`);
          ruleData = await this.buildSimpleTextRule(file, fileContent);
        } else if (file.fileType === AllowedFileType.YAML) {
          // For YAML and other files, use the parsing service
          this.logger.log(
            `Processing as structured rule file: ${file.entryName}`,
          );
          const detectedRuleTypeForParse =
            await this.detectRuleTypeWithTranslation(fileContent);
          const parseResult = await this.parsingService.parseRule({
            content: fileContent,
            fileName: file.name,
            ruleType: detectedRuleTypeForParse,
          });

          if (!parseResult.success) {
            // Instead of throwing error, fall back to simple text rule
            this.logger.log(
              `YAML parsing failed for ${file.entryName}, treating as text file: ${parseResult.error}`,
            );
            ruleData = await this.buildSimpleTextRule(file, fileContent);
          } else {
            ruleData = {
              title: parseResult.title,
              description: parseResult.description,
              rule_type: parseResult.rule_type,
              content: parseResult.content,
              tags: parseResult.tags,
              metadata: parseResult.metadata,
              test_cases: parseResult.test_cases,
            };
          }
        } else {
          // Handle unknown file type
          const errorMessage = `Unsupported file type: ${file.fileType || 'UNKNOWN'} for file: ${file.entryName}`;
          this.logger.error(errorMessage);
          throw new Error(errorMessage);
        }

        // Create the rule using the built data
        const rule = await this.rulesService.create(
          ruleData,
          internalUserId,
          username,
        );

        // Add to processing results
        processingResults.push({
          success: true,
          fileName: file.entryName,
          ruleId: rule.id,
        });
      } catch (error) {
        // Log and add to processing results
        this.logger.error(
          `Failed to process file ${file.entryName}: ${error.message}`,
          error.stack,
        );
        processingResults.push({
          success: false,
          fileName: file.entryName,
          error: error.message,
        });
      }
    }

    return processingResults;
  }

  /**
   * Builds a rule object for simple text files (KQL, TXT, JSON)
   * @param file The decompressed file
   * @param content The file content as string
   * @returns A rule data object ready for creation
   */
  private async buildSimpleTextRule(file: DecompressedFile, content: string) {
    // Use Promise.all to generate title, description, and detect rule type in parallel
    const [title, description, detectedRuleType] = await Promise.all([
      this.generateRuleTitleWithAI(content, file.name),
      this.generateRuleDescriptionWithAI(content),
      this.detectRuleTypeWithTranslation(content),
    ]);

    this.logger.debug(
      `buildSimpleTextRule: for file "${file.name}" detected rule type="${detectedRuleType}", fileType="${file.fileType}"`,
    );

    return {
      title: title,
      description: description,
      rule_type: detectedRuleType, // Use detected rule type instead of hardcoded UNKNOWN
      content: content,
      tags: [], // Empty tags
      metadata: {}, // Empty metadata
      test_cases: [], // Empty test cases
      ai_generated: {
        title: true,
        description: true,
        content: false,
        tags: false,
      },
    };
  }

  /**
   * Process a single file as a rule
   * @param fileBuffer Buffer containing the file
   * @param fileId The file ID
   * @param fileType The type of file being processed
   * @param internalUserId Internal user ID
   * @param username Username or null
   * @param authHeader The authorization header for API calls
   * @returns Array with a single file processing result
   */
  private async processFile(
    fileBuffer: Buffer,
    fileId: string,
    fileType: FileType,
    internalUserId: string,
    username: string | null,
    authHeader: string,
  ): Promise<FileProcessingResult[]> {
    try {
      // Convert buffer to string
      const fileContent = fileBuffer.toString('utf-8');
      const fileName = `rule_${fileId}.${fileType}`;

      this.logger.log(`Processing ${fileType} file: ${fileName}`);

      // Map FileType to AllowedFileType using the utility function
      const allowedFileType = mapFileTypeToAllowedFileType(fileType);

      this.logger.debug(
        `processFile: mapped FileType="${fileType}" to AllowedFileType="${allowedFileType}"`,
      );

      // Create a simple DecompressedFile-like object for consistency
      const fileObj: DecompressedFile = {
        name: fileName,
        entryName: fileName,
        isDirectory: false,
        content: fileBuffer,
        fileType: allowedFileType,
      };

      let ruleData;
      // Determine which processing logic to use based on file type
      if (
        fileObj.fileType === AllowedFileType.TEXT ||
        fileObj.fileType === AllowedFileType.KQL ||
        fileObj.fileType === AllowedFileType.JSON
      ) {
        this.logger.log(
          `Processing as simple text file: ${fileName} with fileType=${fileObj.fileType}`,
        );
        ruleData = await this.buildSimpleTextRule(fileObj, fileContent);
      } else if (fileObj.fileType === AllowedFileType.YAML) {
        // Process as structured rule file
        const detectedRuleTypeForParse =
          await this.detectRuleTypeWithTranslation(fileContent);
        const parseResult = await this.parsingService.parseRule({
          content: fileContent,
          fileName: fileName,
          ruleType: detectedRuleTypeForParse,
        });

        if (!parseResult.success) {
          // Instead of throwing error, fall back to simple text rule
          this.logger.log(
            `YAML parsing failed for ${fileName}, treating as text file: ${parseResult.error}`,
          );
          ruleData = await this.buildSimpleTextRule(fileObj, fileContent);
        } else {
          ruleData = {
            title: parseResult.title,
            description: parseResult.description,
            rule_type: parseResult.rule_type,
            content: parseResult.content,
            tags: parseResult.tags,
            metadata: parseResult.metadata,
            test_cases: parseResult.test_cases,
          };
        }
      } else {
        // Handle unknown file type
        const errorMessage = `Unsupported file type: ${fileObj.fileType || 'UNKNOWN'} for file: ${fileName}`;
        this.logger.error(errorMessage);
        throw new Error(errorMessage);
      }

      // Create the rule using the built data
      const rule: Rule = await this.rulesService.create(
        ruleData,
        internalUserId,
        username,
      );

      // Return processing result
      return [
        {
          success: true,
          fileName: fileName,
          ruleId: rule.id,
        },
      ];
    } catch (error) {
      this.logger.error(
        `Failed to process ${fileType} file: ${error.message}`,
        error.stack,
      );
      return [
        {
          success: false,
          fileName: `rule_${fileId}.${fileType}`,
          error: error.message,
        },
      ];
    }
  }

  /**
   * Generate a title for a rule using AI
   * @param content The rule content
   * @param fallbackTitle The fallback title to use if AI generation fails
   * @returns Generated title or fallback title
   */
  private async generateRuleTitleWithAI(
    content: string,
    fallbackTitle: string,
  ): Promise<string> {
    try {
      this.logger.log('Generating rule title using AI');
      const title = await this.aiGenerationService.generateRuleTitle(content);
      this.logger.log(`Generated AI title: ${title}`);
      return title;
    } catch (error) {
      this.logger.error(
        `Failed to generate AI title: ${error.message}`,
        error.stack,
      );
      return fallbackTitle;
    }
  }

  /**
   * Generate a description for a rule using AI
   * @param content The rule content
   * @param fallbackDescription The fallback description to use if AI generation fails
   * @returns Generated description or fallback description
   */
  private async generateRuleDescriptionWithAI(
    content: string,
    fallbackDescription: string = '',
  ): Promise<string> {
    try {
      this.logger.log('Generating rule description using AI');
      const description =
        await this.aiGenerationService.generateRuleDescription(content);
      this.logger.log(`Generated AI description: ${description}`);
      return description;
    } catch (error) {
      this.logger.error(
        `Failed to generate AI description: ${error.message}`,
        error.stack,
      );
      return fallbackDescription;
    }
  }

  /**
   * Maps detected language to a rule type
   * @param language Detected language from translation service
   * @returns Mapped rule type
   */
  private mapLanguageToRuleType(language: string): RuleType {
    const languageMap = {
      sigma: RuleType.SIGMA,
      spl: RuleType.SPL,
      kql: RuleType.KQL,
      // Add additional mappings as needed
    };

    // Debug log for input and output
    this.logger.debug(
      `mapLanguageToRuleType: input language="${language}", lowercase="${language.toLowerCase()}"`,
    );
    const result = languageMap[language.toLowerCase()] || RuleType.UNKNOWN;
    this.logger.debug(`mapLanguageToRuleType: mapped to rule type="${result}"`);

    return result;
  }

  /**
   * Detects the language of the provided text using the translation service
   * @param query The text to analyze
   * @returns Language detection result
   */
  async detectLanguage(query: string): Promise<LanguageDetectionResponse> {
    try {
      this.logger.debug(
        `Detecting language for text of length ${query.length}, first 100 chars: "${query.substring(0, 100)}"`,
      );

      const response = await firstValueFrom(
        this.httpService
          .post(
            `${this.translationServiceUrl}/api/v1/detect-language`,
            { query },
            {
              headers: {
                'Content-Type': 'application/json',
              },
            },
          )
          .pipe(timeout(this.timeoutMs)),
      );

      // Log raw response for debugging
      this.logger.debug(
        `Language detection raw response: ${JSON.stringify(response.data)}`,
      );

      // Ensure response has expected format
      if (
        !response.data ||
        !response.data.data ||
        !response.data.data.detected_language
      ) {
        this.logger.error(
          `Invalid response format from translation service: ${JSON.stringify(
            response.data,
          )}`,
        );
        throw new InternalServerErrorException(
          'Invalid response from translation service',
        );
      }

      // Transform top_predictions to match our interface
      const topPredictions = response.data.data.top_predictions || [];
      const formattedPredictions = topPredictions.map((pred) => {
        if (Array.isArray(pred) && pred.length === 2) {
          return {
            language: pred[0],
            confidence: pred[1],
          };
        }
        return pred;
      });

      // Map to our response format
      const result = {
        language: response.data.data.detected_language,
        confidence: response.data.data.confidence || 0,
        top_predictions: formattedPredictions,
        error: response.data.data.error,
      };

      // Log formatted result
      this.logger.debug(
        `Language detection formatted result: ${JSON.stringify(result)}`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error detecting language: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to detect language');
    }
  }

  /**
   * Tries to detect rule type using translation service
   * @param content Rule content
   * @returns Detected rule type or UNKNOWN if detection fails
   */
  private async detectRuleTypeWithTranslation(
    content: string,
  ): Promise<RuleType> {
    try {
      this.logger.log(
        `Attempting to detect rule type using translation service for content length ${content.length}`,
      );

      // Update to use the local detectLanguage method
      const languageResult = await this.detectLanguage(content);

      this.logger.debug(
        `Translation service detected language: "${languageResult.language}" (exact case as returned by API)`,
      );

      // Get confidence threshold from environment variable with fallback to 0.7
      const confidenceThreshold =
        this.configService.get<number>(
          'LANGUAGE_DETECTION_CONFIDENCE_THRESHOLD',
        ) || 0.7;

      this.logger.debug(
        `Confidence threshold: ${confidenceThreshold}, detected confidence: ${languageResult.confidence}`,
      );

      // Only use language detection if confidence is above threshold
      if (languageResult.confidence >= confidenceThreshold) {
        const ruleType = this.mapLanguageToRuleType(languageResult.language);
        this.logger.log(
          `Translation service detected language: ${languageResult.language} (confidence: ${languageResult.confidence}), mapped to rule type: ${ruleType}`,
        );
        return ruleType;
      } else {
        this.logger.warn(
          `Translation service confidence too low (${languageResult.confidence}) for reliable detection`,
        );
        return RuleType.UNKNOWN;
      }
    } catch (error) {
      this.logger.error(
        `Failed to detect rule type via translation service: ${error.message}`,
        error.stack,
      );
      return RuleType.UNKNOWN;
    }
  }
}
