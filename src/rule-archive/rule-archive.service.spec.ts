import { Test, TestingModule } from '@nestjs/testing';
import { InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RuleArchiveService } from './rule-archive.service';
import { S3Service } from '../s3/s3.service';
import { DecompressService } from '../decompress/decompress.service';
import { RulesService } from '../rules/rules.service';
import { ParsingService } from '../parsing/parsing.service';
import { S3SignedUrlOperation } from '../s3/s3.types';
import { UploadStatus, FileType } from './rule-archive.types';
import {
  DecompressedFile,
  DecompressResult,
} from '../decompress/decompress.types';
import { ParseRuleResult } from '../parsing/parsing.types';
import { Rule, RuleStatus, RuleType } from '../rules/models/rule.model';
import { v4 as uuidv4 } from 'uuid';
import { RuleUploadStatusService } from '../rule-upload-status/rule-upload-status.service';
import {
  RuleUploadProcessingStatus,
  UpdateRuleUploadStatusDto,
} from '../rule-upload-status/models/rule-upload-status.model';
import {
  AllowedFileType,
  mapFileTypeToAllowedFileType,
} from '../utils/file-filtering.types';
import { AiGenerationService } from '../ai-generation/ai-generation.service';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { AxiosResponse } from 'axios';
import { Logger } from '@nestjs/common';

// Mock the uuid module
jest.mock('uuid');
// Mock the mapFileTypeToAllowedFileType function
jest.mock('../utils/file-filtering.types', () => ({
  ...jest.requireActual('../utils/file-filtering.types'),
  mapFileTypeToAllowedFileType: jest.fn(),
  AllowedFileType: jest.requireActual('../utils/file-filtering.types')
    .AllowedFileType,
}));

describe('RuleArchiveService', () => {
  let service: RuleArchiveService;
  let s3Service: S3Service;
  let configService: ConfigService;
  let decompressService: DecompressService;
  let rulesService: RulesService;
  let parsingService: ParsingService;
  let ruleUploadStatusService: RuleUploadStatusService;
  let aiGenerationService: AiGenerationService;
  let httpService: jest.Mocked<Pick<HttpService, 'post'>>;

  const mockUuid = 'mock-uuid-1234';
  const mockAuthHeader = 'Bearer test-token';

  beforeEach(async () => {
    // Set up the mock for uuidv4
    (uuidv4 as jest.Mock).mockReturnValue(mockUuid);

    const httpServiceMock = {
      post: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RuleArchiveService,
        {
          provide: S3Service,
          useValue: {
            getSignedUrl: jest.fn(),
            getObject: jest.fn(),
            downloadFile: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key) => {
              if (key === 'RULES_UPLOAD_S3_BUCKET_NAME') return 'test-bucket';
              if (key === 'RULES_DOWNLOAD_URL_EXPIRY') return '3600';
              if (key === 'TRANSLATION_SERVICE_URL')
                return 'http://translation-service';
              if (key === 'LANGUAGE_DETECTION_CONFIDENCE_THRESHOLD') return 0.7;
              return undefined;
            }),
          },
        },
        {
          provide: DecompressService,
          useValue: {
            decompressFromBuffer: jest.fn(),
            readFileContent: jest.fn(),
          },
        },
        {
          provide: RulesService,
          useValue: {
            create: jest.fn(),
          },
        },
        {
          provide: ParsingService,
          useValue: {
            parseRule: jest.fn(),
          },
        },
        {
          provide: RuleUploadStatusService,
          useValue: {
            create: jest.fn().mockResolvedValue({
              request_id: mockUuid,
              ext_user_id: 'mock-user-id',
              status: RuleUploadProcessingStatus.PENDING,
              metadata: {},
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }),
            update: jest.fn().mockResolvedValue({
              request_id: mockUuid,
              ext_user_id: 'mock-user-id',
              status: RuleUploadProcessingStatus.COMPLETED,
              metadata: {},
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }),
            findByRequestId: jest.fn().mockResolvedValue({
              request_id: mockUuid,
              ext_user_id: 'mock-user-id',
              status: RuleUploadProcessingStatus.PENDING,
              started_at: new Date().toISOString(),
              metadata: {
                file_type: FileType.ZIP,
                key: `rules/${mockUuid}.zip`,
                bucket: 'test-bucket',
              },
            }),
          },
        },
        {
          provide: AiGenerationService,
          useValue: {
            generateRuleTitle: jest
              .fn()
              .mockResolvedValue('AI-Generated Title'),
            generateRuleDescription: jest
              .fn()
              .mockResolvedValue('AI-Generated Description'),
          },
        },
        {
          provide: HttpService,
          useValue: httpServiceMock,
        },
      ],
    }).compile();

    service = module.get<RuleArchiveService>(RuleArchiveService);
    s3Service = module.get<S3Service>(S3Service);
    configService = module.get<ConfigService>(ConfigService);
    decompressService = module.get<DecompressService>(DecompressService);
    rulesService = module.get<RulesService>(RulesService);
    parsingService = module.get<ParsingService>(ParsingService);
    ruleUploadStatusService = module.get<RuleUploadStatusService>(
      RuleUploadStatusService,
    );
    aiGenerationService = module.get<AiGenerationService>(AiGenerationService);
    httpService = module.get(HttpService);

    // Set up mock responses for configService
    jest.spyOn(configService, 'get').mockImplementation((key: string) => {
      if (key === 'RULES_UPLOAD_S3_BUCKET_NAME') return 'test-bucket';
      if (key === 'RULES_DOWNLOAD_URL_EXPIRY') return '3600';
      if (key === 'TRANSLATION_SERVICE_URL')
        return 'http://translation-service';
      if (key === 'LANGUAGE_DETECTION_CONFIDENCE_THRESHOLD') return 0.7;
      return undefined;
    });

    // Override the logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateUploadUrl', () => {
    it('should generate a signed URL for upload', async () => {
      // Configure the mock responses
      const mockUrl = 'https://example.com/upload-url';
      const mockBucket = 'test-bucket';
      const mockExtUserId = 'auth0|user123';

      jest.spyOn(configService, 'get').mockImplementation((key) => {
        if (key === 'RULES_UPLOAD_S3_BUCKET_NAME') {
          return mockBucket;
        }
        if (key === 'RULES_DOWNLOAD_URL_EXPIRY') {
          return '3600';
        }
        return undefined;
      });

      jest.spyOn(s3Service, 'getSignedUrl').mockResolvedValue(mockUrl);

      // Execute the method
      const result = await service.generateUploadUrl(
        FileType.ZIP,
        mockExtUserId,
      );

      // Verify the result
      expect(result).toEqual({
        signed_url: mockUrl,
        expiresIn: 3600,
        expiresAt: expect.any(String),
        id: mockUuid,
        file_type: FileType.ZIP,
      });

      // Verify the S3Service was called correctly
      expect(s3Service.getSignedUrl).toHaveBeenCalledWith(
        mockBucket,
        expect.stringContaining(mockUuid),
        S3SignedUrlOperation.PUT,
        3600,
      );

      // Verify that RuleUploadStatusService was called correctly
      expect(ruleUploadStatusService.create).toHaveBeenCalledWith(
        {
          request_id: mockUuid,
          status: RuleUploadProcessingStatus.PENDING,
          metadata: expect.objectContaining({
            file_type: FileType.ZIP,
            bucket: mockBucket,
            key: expect.stringContaining(mockUuid),
            created_at: expect.any(String),
          }),
        },
        mockExtUserId,
      );
    });

    it('should throw an error if the S3 bucket is not configured', async () => {
      // Configure the mock to return undefined for the bucket config
      jest.spyOn(configService, 'get').mockReturnValue(undefined);

      // Execute and expect an error
      await expect(
        service.generateUploadUrl(FileType.ZIP, 'auth0|user123'),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('updateUploadStatus', () => {
    const mockBucket = 'test-bucket';
    const mockRequestId = 'test-request-id';
    const mockInternalUserId = 'internal-user-id';
    const mockFileBuffer = Buffer.from('test-data');

    beforeEach(() => {
      jest.spyOn(configService, 'get').mockImplementation((key) => {
        if (key === 'RULES_UPLOAD_S3_BUCKET_NAME') {
          return mockBucket;
        }
        return undefined;
      });

      jest.spyOn(s3Service, 'downloadFile').mockResolvedValue(mockFileBuffer);

      // Update the findByRequestId mock for specific tests
      jest.spyOn(ruleUploadStatusService, 'findByRequestId').mockResolvedValue({
        request_id: mockRequestId,
        ext_user_id: 'mock-user-id',
        status: RuleUploadProcessingStatus.PENDING,
        started_at: new Date().toISOString(),
        metadata: {
          file_type: FileType.ZIP,
          key: `rules/${mockRequestId}.zip`,
          bucket: mockBucket,
        },
      });

      // Mock successful ZIP file processing
      const mockDecompressResult: DecompressResult = {
        files: [
          {
            name: 'test-rule.yml',
            entryName: 'test-rule.yml',
            isDirectory: false,
            content: Buffer.from('test rule content'),
            fileType: AllowedFileType.YAML,
          },
        ],
      };

      jest
        .spyOn(decompressService, 'decompressFromBuffer')
        .mockResolvedValue(mockDecompressResult);
      jest
        .spyOn(decompressService, 'readFileContent')
        .mockResolvedValue('test rule content');

      const mockParseResult: ParseRuleResult = {
        success: true,
        title: 'Test Rule',
        description: 'Test rule description',
        rule_type: RuleType.SIGMA,
        content: 'test rule content',
        tags: ['test'],
        metadata: { severity: 'high' },
        test_cases: [],
      };

      jest
        .spyOn(parsingService, 'parseRule')
        .mockResolvedValue(mockParseResult);

      const mockRule: Rule = {
        id: 'test-id',
        title: 'Test Rule',
        description: 'Test Description',
        rule_type: RuleType.SIGMA,
        content: 'Test Content',
        tags: [],
        metadata: { severity: 'high' },
        test_cases: [],
        status: RuleStatus.DRAFT,
        created_by: 'test-user',
        group_id: 'test-group',
        created_at: '2021-01-01',
        updated_at: '2021-01-01',
        version: 1,
        contributor: 'test-contributor',
        likes: 0,
        downloads: 0,
        dislikes: 0,
      };

      jest.spyOn(rulesService, 'create').mockResolvedValue(mockRule);
    });

    it('should update DynamoDB to PROCESSING and COMPLETED during successful processing', async () => {
      // Setup
      const updateStatusDto = {
        id: mockRequestId,
        status: UploadStatus.SUCCESS,
      };

      // Execute
      const result = await service.updateUploadStatus(
        updateStatusDto,
        mockInternalUserId,
        'test-username',
        mockAuthHeader,
      );

      // Verify DynamoDB updates
      // First, verify the PROCESSING status update
      expect(ruleUploadStatusService.update).toHaveBeenCalledWith(
        mockRequestId,
        expect.objectContaining({
          status: RuleUploadProcessingStatus.PROCESSING,
        }),
      );

      // Then verify the COMPLETED status update
      expect(ruleUploadStatusService.update).toHaveBeenCalledWith(
        mockRequestId,
        expect.objectContaining({
          status: RuleUploadProcessingStatus.COMPLETED,
          metadata: expect.objectContaining({
            processing_results: expect.any(Array),
            processed_files_count: expect.any(Number),
            successful_files_count: expect.any(Number),
            failed_files_count: expect.any(Number),
          }),
        }),
      );

      // Verify that the update was called twice (PROCESSING and COMPLETED)
      expect(ruleUploadStatusService.update).toHaveBeenCalledTimes(2);

      // Verify the result
      expect(result).toEqual({
        id: mockRequestId,
        status: UploadStatus.SUCCESS,
        updatedAt: expect.any(String),
        processingResults: expect.arrayContaining([
          expect.objectContaining({
            success: true,
            fileName: expect.any(String),
            ruleId: expect.any(String),
          }),
        ]),
      });
    });

    it('should update DynamoDB to FAILED when processing fails', async () => {
      // Setup
      const updateStatusDto = {
        id: mockRequestId,
        status: UploadStatus.SUCCESS,
      };

      // Force a processing error
      const processingError = new Error('Test processing error');
      jest.spyOn(s3Service, 'downloadFile').mockRejectedValue(processingError);

      // Execute
      const result = await service.updateUploadStatus(
        updateStatusDto,
        mockInternalUserId,
        'test-username',
        mockAuthHeader,
      );

      // Verify DynamoDB updates
      // First, verify the PROCESSING status update
      expect(ruleUploadStatusService.update).toHaveBeenCalledWith(
        mockRequestId,
        expect.objectContaining({
          status: RuleUploadProcessingStatus.PROCESSING,
        }),
      );

      // Then verify the FAILED status update with error message
      expect(ruleUploadStatusService.update).toHaveBeenCalledWith(
        mockRequestId,
        expect.objectContaining({
          status: RuleUploadProcessingStatus.FAILED,
          error_message: expect.stringContaining('Test processing error'),
        }),
      );

      // Verify that the update was called twice (PROCESSING and FAILED)
      expect(ruleUploadStatusService.update).toHaveBeenCalledTimes(2);

      // Verify the result
      expect(result).toEqual({
        id: mockRequestId,
        status: UploadStatus.FAILED,
        updatedAt: expect.any(String),
        processingResults: expect.arrayContaining([
          expect.objectContaining({
            success: false,
            error: expect.stringContaining('Test processing error'),
          }),
        ]),
      });
    });

    it('should update DynamoDB to FAILED for cancelled uploads', async () => {
      // Setup
      const updateStatusDto = {
        id: mockRequestId,
        status: UploadStatus.CANCELLED,
      };

      // Execute
      const result = await service.updateUploadStatus(
        updateStatusDto,
        mockInternalUserId,
        'test-username',
        mockAuthHeader,
      );

      // Verify DynamoDB update
      expect(ruleUploadStatusService.update).toHaveBeenCalledWith(
        mockRequestId,
        expect.objectContaining({
          status: RuleUploadProcessingStatus.FAILED,
          error_message: 'Upload cancelled by user',
        }),
      );

      // Verify that the update was called once
      expect(ruleUploadStatusService.update).toHaveBeenCalledTimes(1);

      // Verify the result
      expect(result).toEqual({
        id: mockRequestId,
        status: UploadStatus.CANCELLED,
        updatedAt: expect.any(String),
      });
    });

    it('should update DynamoDB to FAILED for client-reported failed uploads', async () => {
      // Setup
      const updateStatusDto = {
        id: mockRequestId,
        status: UploadStatus.FAILED,
      };

      // Execute
      const result = await service.updateUploadStatus(
        updateStatusDto,
        mockInternalUserId,
        'test-username',
        mockAuthHeader,
      );

      // Verify DynamoDB update
      expect(ruleUploadStatusService.update).toHaveBeenCalledWith(
        mockRequestId,
        expect.objectContaining({
          status: RuleUploadProcessingStatus.FAILED,
          error_message: 'Upload marked as failed by client',
        }),
      );

      // Verify that the update was called once
      expect(ruleUploadStatusService.update).toHaveBeenCalledTimes(1);

      // Verify the result
      expect(result).toEqual({
        id: mockRequestId,
        status: UploadStatus.FAILED,
        updatedAt: expect.any(String),
      });
    });
  });

  describe('buildSimpleTextRule', () => {
    const file: DecompressedFile = {
      name: 'test-file.txt',
      entryName: 'test-file.txt',
      isDirectory: false,
      content: Buffer.from('test content'),
      fileType: AllowedFileType.TEXT,
    };
    const content = 'test content';
    const aiTitle = 'AI-Generated Test Title';
    const aiDescription = 'AI-Generated Test Description';
    const mockAuthHeader = 'Bearer test-token';

    beforeEach(() => {
      jest.clearAllMocks();

      // Always mock detectRuleTypeWithTranslation to return KQL
      jest
        .spyOn(service as any, 'detectRuleTypeWithTranslation')
        .mockResolvedValue(RuleType.KQL);
    });

    it('should build a rule object for simple text files with AI-generated title and description', async () => {
      // Set up the test data
      const file: DecompressedFile = {
        name: 'test-file.txt',
        entryName: 'test-file.txt',
        isDirectory: false,
        content: Buffer.from('test content'),
        fileType: AllowedFileType.TEXT,
      };
      const content = 'test content';

      // Set up the AI generation mocks
      const aiTitle = 'AI-Generated Test Title';
      const aiDescription = 'AI-Generated Test Description';

      jest
        .spyOn(aiGenerationService, 'generateRuleTitle')
        .mockResolvedValue(aiTitle);

      jest
        .spyOn(aiGenerationService, 'generateRuleDescription')
        .mockResolvedValue(aiDescription);

      // Call the method
      const result = await (service as any).buildSimpleTextRule(file, content);

      // Verify the result
      expect(result).toEqual({
        title: aiTitle,
        description: aiDescription,
        rule_type: RuleType.KQL,
        content: 'test content',
        tags: [],
        metadata: {},
        test_cases: [],
        ai_generated: {
          title: true,
          description: true,
          content: false,
          tags: false,
        },
      });

      // Verify AI generation was called for both title and description
      expect(aiGenerationService.generateRuleTitle).toHaveBeenCalledWith(
        content,
      );
      expect(aiGenerationService.generateRuleDescription).toHaveBeenCalledWith(
        content,
      );
    });

    it('should fall back to defaults if AI generation fails', async () => {
      // Set up the test data
      const file: DecompressedFile = {
        name: 'test-file.txt',
        entryName: 'test-file.txt',
        isDirectory: false,
        content: Buffer.from('test content'),
        fileType: AllowedFileType.TEXT,
      };
      const content = 'test content';

      // Set up the AI generation mocks to fail
      jest
        .spyOn(aiGenerationService, 'generateRuleTitle')
        .mockRejectedValue(new Error('AI title generation failed'));

      jest
        .spyOn(aiGenerationService, 'generateRuleDescription')
        .mockRejectedValue(new Error('AI description generation failed'));

      // Call the method
      const result = await (service as any).buildSimpleTextRule(file, content);

      // Verify the result
      expect(result).toEqual({
        title: file.name,
        description: '',
        rule_type: RuleType.KQL,
        content: 'test content',
        tags: [],
        metadata: {},
        test_cases: [],
        ai_generated: {
          title: true,
          description: true,
          content: false,
          tags: false,
        },
      });

      // Verify AI generation was called
      expect(aiGenerationService.generateRuleTitle).toHaveBeenCalledWith(
        content,
      );
      expect(aiGenerationService.generateRuleDescription).toHaveBeenCalledWith(
        content,
      );
    });

    it('should handle mixed success/failure of AI generation', async () => {
      // Set up the test data
      const file: DecompressedFile = {
        name: 'test-file.txt',
        entryName: 'test-file.txt',
        isDirectory: false,
        content: Buffer.from('test content'),
        fileType: AllowedFileType.TEXT,
      };
      const content = 'test content';

      // Set up the AI generation mocks with mixed results
      const aiTitle = 'AI-Generated Test Title';
      jest
        .spyOn(aiGenerationService, 'generateRuleTitle')
        .mockResolvedValue(aiTitle);

      jest
        .spyOn(aiGenerationService, 'generateRuleDescription')
        .mockRejectedValue(new Error('AI description generation failed'));

      // Call the method
      const result = await (service as any).buildSimpleTextRule(file, content);

      // Verify the result
      expect(result).toEqual({
        title: aiTitle,
        description: '',
        rule_type: RuleType.KQL,
        content: 'test content',
        tags: [],
        metadata: {},
        test_cases: [],
        ai_generated: {
          title: true,
          description: true,
          content: false,
          tags: false,
        },
      });

      // Verify AI generation was called
      expect(aiGenerationService.generateRuleTitle).toHaveBeenCalledWith(
        content,
      );
      expect(aiGenerationService.generateRuleDescription).toHaveBeenCalledWith(
        content,
      );
    });
  });

  describe('processZipFile', () => {
    const mockRuleIds = ['rule-1', 'rule-2', 'rule-3'];
    let mockYamlRule: Rule;
    let mockTextRule: Rule;
    let mockKqlRule: Rule;

    beforeEach(() => {
      // Reset all mocks
      jest.clearAllMocks();

      // Mock the rule creation to return different IDs for different rules
      jest.spyOn(rulesService, 'create').mockImplementation((data) => {
        if (data.title === 'YAML Rule') {
          mockYamlRule = {
            id: mockRuleIds[0],
            title: data.title,
            description: data.description,
            rule_type: data.rule_type,
            content: data.content,
            status: RuleStatus.DRAFT,
            tags: data.tags,
            metadata: data.metadata,
            test_cases: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 'test-user-id',
            created_by_username: 'test-username',
            contributor: 'test-username',
            likes: 0,
            downloads: 0,
            dislikes: 0,
            version: 1,
          } as unknown as Rule;
          return Promise.resolve(mockYamlRule);
        } else if (data.title === 'AI Generated Text Rule') {
          mockTextRule = {
            id: mockRuleIds[1],
            title: data.title,
            description: data.description,
            rule_type: data.rule_type,
            content: data.content,
            status: RuleStatus.DRAFT,
            tags: data.tags,
            metadata: data.metadata,
            test_cases: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 'test-user-id',
            created_by_username: 'test-username',
            contributor: 'test-username',
            likes: 0,
            downloads: 0,
            dislikes: 0,
            version: 1,
          } as unknown as Rule;
          return Promise.resolve(mockTextRule);
        } else {
          mockKqlRule = {
            id: mockRuleIds[2],
            title: data.title,
            description: data.description,
            rule_type: data.rule_type,
            content: data.content,
            status: RuleStatus.DRAFT,
            tags: data.tags,
            metadata: data.metadata,
            test_cases: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 'test-user-id',
            created_by_username: 'test-username',
            contributor: 'test-username',
            likes: 0,
            downloads: 0,
            dislikes: 0,
            version: 1,
          } as unknown as Rule;
          return Promise.resolve(mockKqlRule);
        }
      });

      // Mock the AI generation
      jest
        .spyOn(aiGenerationService, 'generateRuleTitle')
        .mockImplementation((content) => {
          if (content === 'text content') {
            return Promise.resolve('AI Generated Text Rule');
          } else if (content === 'kql content') {
            return Promise.resolve('AI Generated KQL Rule');
          } else {
            return Promise.resolve('AI-Generated Title');
          }
        });

      jest
        .spyOn(aiGenerationService, 'generateRuleDescription')
        .mockResolvedValue('AI-Generated Description');

      // Mock the parsing service for YAML files
      jest.spyOn(parsingService, 'parseRule').mockResolvedValue({
        success: true,
        title: 'YAML Rule',
        description: 'YAML rule description',
        rule_type: RuleType.SIGMA,
        content: 'yaml content',
        tags: ['yaml'],
        metadata: { type: 'yaml' },
        test_cases: [],
      });

      // Mock detectRuleTypeWithTranslation to simulate language detection
      jest
        .spyOn(service as any, 'detectRuleTypeWithTranslation')
        .mockImplementation((content) => {
          if (content === 'text content' || content === 'kql content') {
            return Promise.resolve(RuleType.KQL);
          } else {
            return Promise.resolve(RuleType.UNKNOWN);
          }
        });
    });

    it('should process text files differently from YAML files', async () => {
      // Mock decompression result with multiple file types
      const mockDecompressResult: DecompressResult = {
        files: [
          {
            name: 'yaml-rule.yml',
            entryName: 'yaml-rule.yml',
            isDirectory: false,
            content: Buffer.from('yaml content'),
            fileType: AllowedFileType.YAML,
          },
          {
            name: 'text-rule.txt',
            entryName: 'text-rule.txt',
            isDirectory: false,
            content: Buffer.from('text content'),
            fileType: AllowedFileType.TEXT,
          },
          {
            name: 'kql-rule.kql',
            entryName: 'kql-rule.kql',
            isDirectory: false,
            content: Buffer.from('kql content'),
            fileType: AllowedFileType.KQL,
          },
        ],
      };

      jest
        .spyOn(decompressService, 'decompressFromBuffer')
        .mockResolvedValue(mockDecompressResult);

      jest
        .spyOn(decompressService, 'readFileContent')
        .mockImplementation((file) => {
          if (file.name === 'yaml-rule.yml') {
            return Promise.resolve('yaml content');
          } else if (file.name === 'text-rule.txt') {
            return Promise.resolve('text content');
          } else {
            return Promise.resolve('kql content');
          }
        });

      // Set up parsing mock to handle YAML file
      const mockParseResult: ParseRuleResult = {
        success: true,
        title: 'YAML Rule',
        description: 'YAML rule description',
        rule_type: RuleType.SIGMA,
        content: 'yaml content',
        tags: ['yaml'],
        metadata: { type: 'yaml' },
        test_cases: [],
      };

      jest
        .spyOn(parsingService, 'parseRule')
        .mockResolvedValue(mockParseResult);

      // Set up AI generation mock
      jest
        .spyOn(aiGenerationService, 'generateRuleTitle')
        .mockResolvedValueOnce('AI Generated Text Rule')
        .mockResolvedValueOnce('AI Generated KQL Rule');

      jest
        .spyOn(aiGenerationService, 'generateRuleDescription')
        .mockResolvedValue('AI-Generated Description');

      // Set up rules service mock with a simpler approach
      const mockRuleIds = ['yaml-rule-id', 'text-rule-id', 'kql-rule-id'];
      jest.spyOn(rulesService, 'create').mockImplementation(function (
        this: any,
        ...args
      ) {
        const [data] = args;

        let ruleId = 'default-rule-id';
        if (data.title === 'YAML Rule') {
          ruleId = mockRuleIds[0];
        } else if (data.title === 'AI Generated Text Rule') {
          ruleId = mockRuleIds[1];
        } else if (data.title === 'AI Generated KQL Rule') {
          ruleId = mockRuleIds[2];
        }

        return Promise.resolve({
          id: ruleId,
          ...data,
          status: RuleStatus.DRAFT,
          created_at: '2021-01-01',
          updated_at: '2021-01-01',
          version: 1,
          likes: 0,
          downloads: 0,
          dislikes: 0,
        } as Rule);
      });

      // Process the files
      const result = await (service as any).processZipFile(
        Buffer.from('zip content'),
        'test-user-id',
        'test-username',
      );

      // Verify that all files were processed
      expect(result.length).toBe(3);
      expect(result[0].success).toBe(true);
      expect(result[0].fileName).toBe('yaml-rule.yml');
      expect(result[0].ruleId).toBe(mockRuleIds[0]);

      expect(result[1].success).toBe(true);
      expect(result[1].fileName).toBe('text-rule.txt');
      expect(result[1].ruleId).toBe(mockRuleIds[1]);

      expect(result[2].success).toBe(true);
      expect(result[2].fileName).toBe('kql-rule.kql');
      expect(result[2].ruleId).toBe(mockRuleIds[2]);

      // Verify parsing was called for YAML files
      expect(parsingService.parseRule).toHaveBeenCalledTimes(1);
      expect(parsingService.parseRule).toHaveBeenCalledWith({
        content: 'yaml content',
        fileName: 'yaml-rule.yml',
        ruleType: 'UNKNOWN',
      });

      // Verify AI generation was called for text and KQL files
      expect(aiGenerationService.generateRuleTitle).toHaveBeenCalledTimes(2);
      expect(aiGenerationService.generateRuleTitle).toHaveBeenCalledWith(
        'text content',
      );
      expect(aiGenerationService.generateRuleTitle).toHaveBeenCalledWith(
        'kql content',
      );

      // Verify rule creation was called for each file
      expect(rulesService.create).toHaveBeenCalledTimes(3);

      // Get the calls to rulesService.create
      const createCalls = (rulesService.create as jest.Mock).mock.calls;

      // Verify YAML rule data
      const yamlCall = createCalls.find(
        (call) => call[0].title === 'YAML Rule',
      );
      expect(yamlCall[0]).toEqual({
        title: 'YAML Rule',
        description: 'YAML rule description',
        rule_type: RuleType.SIGMA,
        content: 'yaml content',
        tags: ['yaml'],
        metadata: { type: 'yaml' },
        test_cases: [],
      });

      // Verify TEXT rule data
      const textCall = createCalls.find(
        (call) => call[0].title === 'AI Generated Text Rule',
      );
      expect(textCall[0]).toEqual({
        title: 'AI Generated Text Rule',
        description: 'AI-Generated Description',
        rule_type: RuleType.KQL,
        content: 'text content',
        tags: [],
        metadata: {},
        test_cases: [],
        ai_generated: {
          title: true,
          description: true,
          content: false,
          tags: false,
        },
      });

      // Verify KQL rule data
      const kqlCall = createCalls.find(
        (call) => call[0].title === 'AI Generated KQL Rule',
      );
      expect(kqlCall[0]).toEqual({
        title: 'AI Generated KQL Rule',
        description: 'AI-Generated Description',
        rule_type: RuleType.KQL,
        content: 'kql content',
        tags: [],
        metadata: {},
        test_cases: [],
        ai_generated: {
          title: true,
          description: true,
          content: false,
          tags: false,
        },
      });
    });

    it('should throw an error for unsupported file types', async () => {
      // Mock decompression result with unsupported file type
      const mockDecompressResult: DecompressResult = {
        files: [
          {
            name: 'unknown-file.xyz',
            entryName: 'unknown-file.xyz',
            isDirectory: false,
            content: Buffer.from('unknown content'),
            fileType: AllowedFileType.UNKNOWN,
          },
        ],
      };

      jest
        .spyOn(decompressService, 'decompressFromBuffer')
        .mockResolvedValue(mockDecompressResult);

      jest
        .spyOn(decompressService, 'readFileContent')
        .mockResolvedValue('unknown content');

      // Process the files and expect an error to be logged
      const result = await (service as any).processZipFile(
        Buffer.from('zip content'),
        'test-user-id',
        'test-username',
      );

      // The function should handle the error and return it in the results
      expect(result.length).toBe(1);
      expect(result[0].success).toBe(false);
      expect(result[0].fileName).toBe('unknown-file.xyz');
      expect(result[0].error).toContain('Unsupported file type: UNKNOWN');
    });

    it('should handle invalid YAML files in ZIP by treating them as simple text files', async () => {
      // Set up mocks and test data
      const mockRule = setupInvalidYamlTest();

      // Process the ZIP file
      const result = await (service as any).processZipFile(
        Buffer.from('zip content'),
        'test-user-id',
        'test-username',
      );

      // Verify the result
      expect(result.length).toBe(1);
      expect(result[0].success).toBe(true);
      expect(result[0].fileName).toBe('invalid-yaml.yml');
      expect(result[0].ruleId).toBe(mockRule.id);

      // Verify the rule was created as a simple text rule
      expect(rulesService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          rule_type: RuleType.KQL,
          content: 'invalid: yaml: content:',
        }),
        'test-user-id',
        'test-username',
      );
    });
  });

  describe('processFile', () => {
    const mockFileId = 'test-file-id';
    const mockFileContent = 'test file content';
    const mockInternalUserId = 'internal-user-id';
    const mockUsername = 'test-user';
    const mockBuffer = Buffer.from(mockFileContent);
    const mockRuleId = 'test-rule-id';

    beforeEach(() => {
      // Reset all mocks
      jest.clearAllMocks();

      // Mock the rule creation
      jest.spyOn(rulesService, 'create').mockResolvedValue({
        id: mockRuleId,
        title: 'AI-Generated Title',
        description: 'AI-Generated Description',
        rule_type: RuleType.UNKNOWN,
        content: mockFileContent,
        status: RuleStatus.DRAFT,
        tags: [],
        metadata: {},
        test_cases: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: mockInternalUserId,
        created_by_username: mockUsername,
        contributor: mockUsername,
        likes: 0,
        downloads: 0,
        dislikes: 0,
        version: 1,
      } as unknown as Rule);

      // Mock AI generation
      jest
        .spyOn(aiGenerationService, 'generateRuleTitle')
        .mockResolvedValue('AI-Generated Title');
      jest
        .spyOn(aiGenerationService, 'generateRuleDescription')
        .mockResolvedValue('AI-Generated Description');
    });

    it('should process a YAML file and use mapFileTypeToAllowedFileType', async () => {
      // Mock mapFileTypeToAllowedFileType to return YAML
      (mapFileTypeToAllowedFileType as jest.Mock).mockReturnValue(
        AllowedFileType.YAML,
      );

      // Mock successful YAML parsing
      const mockParseResult: ParseRuleResult = {
        success: true,
        title: 'Test Rule',
        description: 'Test Description',
        rule_type: RuleType.SIGMA,
        content: mockFileContent,
        tags: ['test-tag'],
        metadata: { some: 'metadata' },
        test_cases: [],
      };
      jest
        .spyOn(parsingService, 'parseRule')
        .mockResolvedValue(mockParseResult);

      // Mock ConfigService for S3 bucket
      jest.spyOn(configService, 'get').mockImplementation((key) => {
        if (key === 'RULES_UPLOAD_S3_BUCKET_NAME') {
          return 'test-bucket';
        }
        return undefined;
      });

      // Mock S3 download
      jest.spyOn(s3Service, 'downloadFile').mockResolvedValue(mockBuffer);

      // Make the private processFile method accessible for testing
      const processFile = jest.spyOn(service as any, 'processFile');

      // Update the mock to return YAML file type
      jest.spyOn(ruleUploadStatusService, 'findByRequestId').mockResolvedValue({
        request_id: mockFileId,
        ext_user_id: 'mock-user-id',
        status: RuleUploadProcessingStatus.PENDING,
        started_at: new Date().toISOString(),
        metadata: {
          file_type: FileType.YAML,
          key: `rules/${mockFileId}.yaml`,
          bucket: 'test-bucket',
        },
      });

      // Call the updateUploadStatus method with YAML file type
      await service.updateUploadStatus(
        { id: mockFileId, status: UploadStatus.SUCCESS },
        mockInternalUserId,
        mockUsername,
        mockAuthHeader,
      );

      // Verify mapFileTypeToAllowedFileType was called with YAML FileType
      expect(mapFileTypeToAllowedFileType).toHaveBeenCalledWith(FileType.YAML);
    });

    it('should process a JSON file and use mapFileTypeToAllowedFileType', async () => {
      // Mock mapFileTypeToAllowedFileType to return JSON
      (mapFileTypeToAllowedFileType as jest.Mock).mockReturnValue(
        AllowedFileType.JSON,
      );

      // Mock successful rule creation for simple text files
      jest.spyOn(rulesService, 'create').mockResolvedValue({
        id: mockRuleId,
        title: 'AI-Generated Title',
        description: 'AI-Generated Description',
        rule_type: RuleType.UNKNOWN,
        content: mockFileContent,
        status: RuleStatus.DRAFT,
        tags: [],
        metadata: {},
        test_cases: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: mockInternalUserId,
        created_by_username: mockUsername,
        contributor: mockUsername,
        likes: 0,
        downloads: 0,
        dislikes: 0,
        version: 1,
      } as unknown as Rule);

      // Mock ConfigService for S3 bucket
      jest.spyOn(configService, 'get').mockImplementation((key) => {
        if (key === 'RULES_UPLOAD_S3_BUCKET_NAME') {
          return 'test-bucket';
        }
        return undefined;
      });

      // Mock S3 download
      jest.spyOn(s3Service, 'downloadFile').mockResolvedValue(mockBuffer);

      // Update the mock to return JSON file type
      jest.spyOn(ruleUploadStatusService, 'findByRequestId').mockResolvedValue({
        request_id: mockFileId,
        ext_user_id: 'mock-user-id',
        status: RuleUploadProcessingStatus.PENDING,
        started_at: new Date().toISOString(),
        metadata: {
          file_type: FileType.JSON,
          key: `rules/${mockFileId}.json`,
          bucket: 'test-bucket',
        },
      });

      // Call updateUploadStatus with JSON file type
      await service.updateUploadStatus(
        { id: mockFileId, status: UploadStatus.SUCCESS },
        mockInternalUserId,
        mockUsername,
        mockAuthHeader,
      );

      // Verify mapFileTypeToAllowedFileType was called with JSON FileType
      expect(mapFileTypeToAllowedFileType).toHaveBeenCalledWith(FileType.JSON);
      expect(rulesService.create).toHaveBeenCalled();
    });
  });

  describe('detectLanguage', () => {
    const mockText = 'some rule content';
    const mockLanguageResponse: AxiosResponse = {
      data: {
        data: {
          detected_language: 'kql',
          confidence: 0.95,
          top_predictions: [
            ['kql', 0.95],
            ['sigma', 0.03],
            ['spl', 0.02],
          ],
        },
      },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: { headers: {} } as any,
    };

    it('should successfully detect language', async () => {
      // Arrange
      httpService.post.mockReturnValue({
        pipe: jest.fn().mockReturnValue(of(mockLanguageResponse)),
      } as any);

      // Act
      const result = await service.detectLanguage(mockText);

      // Assert
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/detect-language'),
        { query: mockText },
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        }),
      );
      expect(result).toEqual({
        language: 'kql',
        confidence: 0.95,
        top_predictions: [
          { language: 'kql', confidence: 0.95 },
          { language: 'sigma', confidence: 0.03 },
          { language: 'spl', confidence: 0.02 },
        ],
        error: undefined,
      });
    });

    it('should handle invalid response format', async () => {
      // Arrange
      const invalidResponse = {
        data: { invalid: 'format' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: { headers: {} } as any,
      };
      httpService.post.mockReturnValue({
        pipe: jest.fn().mockReturnValue(of(invalidResponse)),
      } as any);

      // Act & Assert
      await expect(service.detectLanguage(mockText)).rejects.toThrow(
        InternalServerErrorException,
      );
    });

    it('should handle request errors', async () => {
      // Arrange
      httpService.post.mockReturnValue({
        pipe: jest
          .fn()
          .mockReturnValue(throwError(() => new Error('Network error'))),
      } as any);

      // Act & Assert
      await expect(service.detectLanguage(mockText)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('detectRuleTypeWithTranslation', () => {
    const mockContent = 'rule content';

    it('should detect KQL rule type with high confidence', async () => {
      // Arrange
      const mockDetectLanguageResponse = {
        language: 'kql',
        confidence: 0.95,
        top_predictions: [
          { language: 'kql', confidence: 0.95 },
          { language: 'sigma', confidence: 0.03 },
        ],
      };

      jest
        .spyOn(service, 'detectLanguage')
        .mockResolvedValue(mockDetectLanguageResponse);

      // Act
      const result = await (service as any).detectRuleTypeWithTranslation(
        mockContent,
      );

      // Assert
      expect(service.detectLanguage).toHaveBeenCalledWith(mockContent);
      expect(result).toBe(RuleType.KQL);
    });

    it('should return UNKNOWN rule type with low confidence', async () => {
      // Arrange
      const mockDetectLanguageResponse = {
        language: 'kql',
        confidence: 0.3, // Below threshold
        top_predictions: [
          { language: 'kql', confidence: 0.3 },
          { language: 'sigma', confidence: 0.2 },
        ],
      };

      jest
        .spyOn(service, 'detectLanguage')
        .mockResolvedValue(mockDetectLanguageResponse);

      // Act
      const result = await (service as any).detectRuleTypeWithTranslation(
        mockContent,
      );

      // Assert
      expect(service.detectLanguage).toHaveBeenCalledWith(mockContent);
      expect(result).toBe(RuleType.UNKNOWN);
    });

    it('should handle errors and return UNKNOWN rule type', async () => {
      // Arrange
      jest
        .spyOn(service, 'detectLanguage')
        .mockRejectedValue(new Error('Detection failed'));

      // Act
      const result = await (service as any).detectRuleTypeWithTranslation(
        mockContent,
      );

      // Assert
      expect(service.detectLanguage).toHaveBeenCalledWith(mockContent);
      expect(result).toBe(RuleType.UNKNOWN);
    });

    it('should use custom confidence threshold if configured', async () => {
      // Arrange
      const mockDetectLanguageResponse = {
        language: 'kql',
        confidence: 0.4, // Lower than default, but higher than our custom threshold
        top_predictions: [
          { language: 'kql', confidence: 0.4 },
          { language: 'sigma', confidence: 0.3 },
        ],
      };

      jest
        .spyOn(service, 'detectLanguage')
        .mockResolvedValue(mockDetectLanguageResponse);

      // Override the threshold for this test
      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'LANGUAGE_DETECTION_CONFIDENCE_THRESHOLD') return 0.3;
        if (key === 'TRANSLATION_SERVICE_URL')
          return 'http://translation-service';
        return undefined;
      });

      // Act
      const result = await (service as any).detectRuleTypeWithTranslation(
        mockContent,
      );

      // Assert
      expect(service.detectLanguage).toHaveBeenCalledWith(mockContent);
      expect(result).toBe(RuleType.KQL);
    });
  });

  // Add helper function to set up invalid YAML test
  const setupInvalidYamlTest = () => {
    // Mock decompression result with an invalid YAML file
    const mockDecompressResult: DecompressResult = {
      files: [
        {
          name: 'invalid-yaml.yml',
          entryName: 'invalid-yaml.yml',
          isDirectory: false,
          content: Buffer.from('invalid: yaml: content:'),
          fileType: AllowedFileType.YAML,
        },
      ],
    };

    jest
      .spyOn(decompressService, 'decompressFromBuffer')
      .mockResolvedValue(mockDecompressResult);

    // Set up readFileContent mock to return invalid YAML content
    jest
      .spyOn(decompressService, 'readFileContent')
      .mockResolvedValue('invalid: yaml: content:');

    // Mock parsing service to return failure for invalid YAML
    const mockParseFailResult: ParseRuleResult = {
      success: false,
      error: 'YAML parsing error: invalid format',
      title: '',
      description: '',
      rule_type: RuleType.UNKNOWN,
      content: 'invalid: yaml: content:',
      tags: [],
      metadata: {},
      test_cases: [],
    };

    jest
      .spyOn(parsingService, 'parseRule')
      .mockResolvedValue(mockParseFailResult);

    // Mock detectRuleTypeWithTranslation to return KQL
    jest
      .spyOn(service as any, 'detectRuleTypeWithTranslation')
      .mockResolvedValue(RuleType.KQL);

    // Mock rule creation for simple text rule
    const mockRule: Rule = {
      id: 'simple-text-rule-id',
      title: 'invalid-yaml.yml',
      description: '',
      rule_type: RuleType.UNKNOWN,
      content: 'invalid: yaml: content:',
      tags: [],
      metadata: {},
      test_cases: [],
      status: RuleStatus.DRAFT,
      created_by: 'test-user-id',
      group_id: 'test-group',
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      version: 1,
      contributor: 'test-username',
      likes: 0,
      downloads: 0,
      dislikes: 0,
    };

    jest.spyOn(rulesService, 'create').mockResolvedValue(mockRule);
    return mockRule;
  };
});
