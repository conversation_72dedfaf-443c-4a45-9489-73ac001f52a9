import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { RuleSearchRepository } from '../opensearch/repositories/rule.repository';
import { OpenSearchService } from '../opensearch/opensearch.service';
import { OpenSearchConfigService } from '../opensearch/config/opensearch.config';
import { QueryUtilityService } from './query-builders/query.utility';
import { BasicSearchQueryBuilder } from './query-builders/basic-search.query-builder';
import { AdvancedSearchQueryBuilder } from './query-builders/advanced-search.query-builder';
import { SimilarRulesQueryBuilder } from './query-builders/similar-rules.query-builder';
import { SuggestQueryBuilder } from './query-builders/suggest.query-builder';
import { SearchFiltersDto } from './dtos/search-filters.dto';
import { SearchOptionsDto } from './dtos/search-options.dto';
import { SortField } from './models/sort-field.enum';
import { PaginationMode } from './models/pagination-mode.enum';
import { AdvancedSearchQueryDto } from './dtos/search-query.dto';
import {
  SearchResponseDto,
  SearchSuggestionsResponseDto,
  SearchMetadataResponseDto,
} from './dtos/search-response.dto';
import {
  SimilarRulesQueryDto,
  SimilarRulesResponseDto,
} from './dtos/similar-rules.dto';
import {
  PageSearchOptionsDto,
  PagedSearchResponseDto,
} from './dtos/page-search-options.dto';
import { RuleIndexSchema } from '../opensearch/schemas/rule.schema';
import {
  OpenSearchResponse,
  OpenSearchGetResponse,
  RuleSource,
  OpenSearchQueryClause,
  OpenSearchSortClause,
} from './models/opensearch-types';
import { SimilarRulesFieldValues } from './models/search-types';

// Pagination Strategies and Utilities
import { CursorPaginationStrategy } from './pagination/cursor-pagination.strategy';
import { OffsetPaginationStrategy } from './pagination/offset-pagination.strategy';
import {
  CursorQueryBuildOptions,
  OffsetQueryBuildOptions,
} from './pagination/pagination-strategy.interface';

// Response Formatters
import {
  SearchResponseFormatter,
  PagedSearchResponseFormatter,
  SimilarRulesResponseFormatter,
  SuggestionsResponseFormatter,
} from './response-formatters';

/**
 * Service for handling search operations
 */
@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);
  private readonly indexSchema: RuleIndexSchema;

  constructor(
    private readonly ruleSearchRepository: RuleSearchRepository,
    private readonly openSearchService: OpenSearchService,
    private readonly configService: OpenSearchConfigService,
    private readonly queryUtilityService: QueryUtilityService,
    private readonly basicSearchQueryBuilder: BasicSearchQueryBuilder,
    private readonly advancedSearchQueryBuilder: AdvancedSearchQueryBuilder,
    private readonly similarRulesQueryBuilder: SimilarRulesQueryBuilder,
    private readonly suggestQueryBuilder: SuggestQueryBuilder,
    private readonly cursorPaginationStrategy: CursorPaginationStrategy,
    private readonly offsetPaginationStrategy: OffsetPaginationStrategy,
    // Add new formatters
    private readonly searchResponseFormatter: SearchResponseFormatter,
    private readonly pagedSearchResponseFormatter: PagedSearchResponseFormatter,
    private readonly similarRulesResponseFormatter: SimilarRulesResponseFormatter,
    private readonly suggestionsResponseFormatter: SuggestionsResponseFormatter,
  ) {
    this.indexSchema = new RuleIndexSchema(this.configService);
  }

  /**
   * Check if any filters are set
   * @param filters Search filters
   * @returns true if any filter is set, false otherwise
   */
  private hasActiveFilters(filters?: SearchFiltersDto): boolean {
    if (!filters) {
      this.logger.debug('No filters object provided');
      return false;
    }

    // Log the raw filters object
    this.logger.debug('Raw filters:', JSON.stringify(filters));

    // Check each filter array/value
    const checks = {
      owner_ids:
        Array.isArray(filters.owner_ids) && filters.owner_ids.length > 0,
      rule_types:
        Array.isArray(filters.rule_types) && filters.rule_types.length > 0,
      statuses: Array.isArray(filters.statuses) && filters.statuses.length > 0,
      stages: Array.isArray(filters.stages) && filters.stages.length > 0,
      created_after: !!filters.created_after,
      created_before: !!filters.created_before,
      published_after: !!filters.published_after,
      published_before: !!filters.published_before,
      updated_after: !!filters.updated_after,
      updated_before: !!filters.updated_before,
      metadata_date_after: !!filters.metadata_date_after,
      metadata_date_before: !!filters.metadata_date_before,
      metadata_modified_after: !!filters.metadata_modified_after,
      metadata_modified_before: !!filters.metadata_modified_before,
      created_by:
        Array.isArray(filters.created_by) && filters.created_by.length > 0,
      contributors:
        Array.isArray(filters.contributors) && filters.contributors.length > 0,
      authors: Array.isArray(filters.authors) && filters.authors.length > 0,
      severities:
        Array.isArray(filters.severities) && filters.severities.length > 0,
      mitre_tactics:
        Array.isArray(filters.mitre_tactics) &&
        filters.mitre_tactics.length > 0,
      mitre_techniques:
        Array.isArray(filters.mitre_techniques) &&
        filters.mitre_techniques.length > 0,
      ids: Array.isArray(filters.ids) && filters.ids.length > 0,
    };

    // Log individual checks
    this.logger.debug('Filter checks:', checks);

    // Check if any filter is active
    const hasFilters = Object.values(checks).some((value) => value);
    this.logger.debug('Has active filters:', hasFilters);

    return hasFilters;
  }

  /**
   * Perform basic search using cursor-based pagination.
   */
  async search(
    query?: string,
    filters?: SearchFiltersDto,
    options?: SearchOptionsDto,
  ): Promise<SearchResponseDto> {
    this.logger.debug(
      'Cursor-based search (SearchService.search) called with:',
      {
        query,
        filters: JSON.stringify(filters),
        options: JSON.stringify(options),
      },
    );

    if (filters) {
      const dateFields: (keyof SearchFiltersDto)[] = [
        'created_after',
        'created_before',
        'published_after',
        'published_before',
        'updated_after',
        'updated_before',
      ];
      for (const field of dateFields) {
        const value = filters[field] as string | undefined;
        if (value && !this.queryUtilityService.isValidDateString(value)) {
          throw new BadRequestException(`Invalid date format for ${field}`);
        }
      }
    }

    const processedQuery = query
      ? this.queryUtilityService.preprocessQuery(query)
      : undefined;

    const highlightConfig = {
      fields: {
        'title.value': {},
        description: {},
        content: { fragment_size: 150, number_of_fragments: 3 },
        'title.value.edge_ngram': {},
        'description.edge_ngram': {},
        'content.edge_ngram': {},
        'all_text.edge_ngram': {},
      },
      pre_tags: ['<strong>'],
      post_tags: ['</strong>'],
    };
    const sourceFields = true;

    const tempSearchBodyFromBuilder = this.basicSearchQueryBuilder.build(
      processedQuery,
      filters,
      {
        size: (options?.size || 20) + 1,
        include_aggregations: options?.include_aggregations,
      },
    );
    const coreQueryClause = tempSearchBodyFromBuilder.query;

    const cursorBuildOptions: CursorQueryBuildOptions = {
      ...(options || {}),
      highlightConfig: tempSearchBodyFromBuilder.highlight || highlightConfig,
      sourceFields:
        tempSearchBodyFromBuilder._source !== undefined
          ? tempSearchBodyFromBuilder._source
          : sourceFields,
    };

    const searchBody = this.cursorPaginationStrategy.buildSearchBody(
      coreQueryClause,
      cursorBuildOptions,
    );
    this.logger.debug(
      'Search body from CursorPaginationStrategy:',
      JSON.stringify(searchBody),
    );

    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;

    // Use the new formatter
    return this.searchResponseFormatter.format(response, cursorBuildOptions);
  }

  /**
   * Perform advanced search, supporting both cursor and offset pagination.
   */
  async advancedSearch(
    queryDto: AdvancedSearchQueryDto,
  ): Promise<PagedSearchResponseDto> {
    try {
      const { options = {} } = queryDto;
      const {
        pagination_mode = PaginationMode.OFFSET, // Default to offset
      } = options;

      this.logger.debug(
        `Advanced Search (SearchService.advancedSearch) - Mode: ${pagination_mode}`,
        {
          query: JSON.stringify(queryDto),
          options: JSON.stringify(options),
        },
      );

      const coreQuery = this.advancedSearchQueryBuilder.build(queryDto);

      const highlightConfig = {
        fields: {
          'title.value': {},
          description: {},
          content: { fragment_size: 150, number_of_fragments: 3 },
          'metadata.mitre_tactics': {},
          'metadata.mitre_techniques': {},
          'metadata.tags': {},
          'metadata.mitre_attack.name': {},
          'metadata.mitre_attack.mitre_id': {},
          'metadata.mitre_attack.parent_name': {},
          'title.value.edge_ngram': {},
          'description.edge_ngram': {},
          'content.edge_ngram': {},
          'all_text.edge_ngram': {},
        },
        pre_tags: ['<strong>'],
        post_tags: ['</strong>'],
      };
      const sourceFields = true;

      if (pagination_mode === PaginationMode.CURSOR) {
        const cursorBuildOptions: CursorQueryBuildOptions = {
          ...options, // Spreads cursor, direction, size, sort_by, sort_order from DTO
          highlightConfig,
          sourceFields,
        };

        const searchBody = this.cursorPaginationStrategy.buildSearchBody(
          coreQuery,
          cursorBuildOptions,
        );
        this.logger.debug(
          'Advanced Search (Cursor) body:',
          JSON.stringify(searchBody),
        );

        const rawResponse = (await this.openSearchService.search(
          this.indexSchema.indexAlias,
          searchBody,
        )) as OpenSearchResponse<RuleSource>;

        // Format first as SearchResponseDto, then adapt to PagedSearchResponseDto
        const cursorResponse = this.searchResponseFormatter.format(
          rawResponse,
          cursorBuildOptions,
        );
        return this.pagedSearchResponseFormatter.formatCursorResponse(
          cursorResponse,
          {
            page: 1, // Cursor mode effectively resets page indicator for this adapter
            size: cursorBuildOptions.size || 20,
          },
        );
      } else {
        // OFFSET Pagination
        const offsetBuildOptions: OffsetQueryBuildOptions = {
          ...options, // Spreads page, size, sort_by, sort_order from DTO
          highlightConfig,
          sourceFields,
        };

        const searchBody = this.offsetPaginationStrategy.buildSearchBody(
          coreQuery,
          offsetBuildOptions,
        );
        this.logger.debug(
          'Advanced Search (Offset) body:',
          JSON.stringify(searchBody),
        );

        const rawResponse = (await this.openSearchService.search(
          this.indexSchema.indexAlias,
          searchBody,
        )) as OpenSearchResponse<RuleSource>;

        // Use the new formatter
        return this.pagedSearchResponseFormatter.formatOffsetResponse(
          rawResponse,
          offsetBuildOptions,
        );
      }
    } catch (error: unknown) {
      this.logger.error(
        `Advanced search error: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  /**
   * Perform page-based search (defaults to offset mode).
   */
  async pagedSearch(
    query?: string,
    filters?: SearchFiltersDto,
    pageOptions?: PageSearchOptionsDto,
  ): Promise<PagedSearchResponseDto> {
    this.logger.debug(
      'Offset-based pagedSearch (SearchService.pagedSearch) called with:',
      {
        query,
        filters: JSON.stringify(filters),
        options: JSON.stringify(pageOptions),
      },
    );

    const processedQuery = query
      ? this.queryUtilityService.preprocessQuery(query)
      : undefined;

    const highlightConfig = {
      fields: {
        'title.value': {},
        description: {},
        content: { fragment_size: 150, number_of_fragments: 3 },
        'title.value.edge_ngram': {},
        'description.edge_ngram': {},
        'content.edge_ngram': {},
        'all_text.edge_ngram': {},
      },
      pre_tags: ['<strong>'],
      post_tags: ['</strong>'],
    };
    const sourceFields = true;

    const tempSearchBodyFromBuilder = this.basicSearchQueryBuilder.build(
      processedQuery,
      filters,
      {
        size: pageOptions?.size || 20,
        from: ((pageOptions?.page || 1) - 1) * (pageOptions?.size || 20),
        sort_config: [],
        include_aggregations: pageOptions?.include_aggregations,
      },
    );
    const coreQueryClause = tempSearchBodyFromBuilder.query;

    const offsetBuildOptions: OffsetQueryBuildOptions = {
      ...(pageOptions || {}),
      highlightConfig: tempSearchBodyFromBuilder.highlight || highlightConfig,
      sourceFields:
        tempSearchBodyFromBuilder._source !== undefined
          ? tempSearchBodyFromBuilder._source
          : sourceFields,
    };

    const searchBody = this.offsetPaginationStrategy.buildSearchBody(
      coreQueryClause,
      offsetBuildOptions,
    );
    this.logger.debug(
      'PagedSearch body from OffsetPaginationStrategy:',
      JSON.stringify(searchBody),
    );

    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;

    // Use the new formatter
    return this.pagedSearchResponseFormatter.formatOffsetResponse(
      response,
      offsetBuildOptions,
    );
  }

  async pagedCustomSortSearch(
    sort: OpenSearchSortClause[] | OpenSearchSortClause,
    query?: string,
    filters?: SearchFiltersDto,
    options?: PageSearchOptionsDto,
  ): Promise<PagedSearchResponseDto> {
    this.logger.debug(
      'Paged custom sort search (SearchService.pagedCustomSortSearch) called with:',
      {
        sort,
        query,
        filters: JSON.stringify(filters),
        options: JSON.stringify(options),
      },
    );

    const processedQuery = query
      ? this.queryUtilityService.preprocessQuery(query)
      : undefined;

    const highlightConfig = {
      fields: {
        'title.value': {},
        description: {},
        content: { fragment_size: 150, number_of_fragments: 3 },
        'title.value.edge_ngram': {},
        'description.edge_ngram': {},
        'content.edge_ngram': {},
        'all_text.edge_ngram': {},
      },
      pre_tags: ['<strong>'],
      post_tags: ['</strong>'],
    };
    const sourceFields = true;

    const tempSearchBodyFromBuilder = this.basicSearchQueryBuilder.build(
      processedQuery,
      filters,
      {
        size: options?.size || 20,
        from: ((options?.page || 1) - 1) * (options?.size || 20),
        sort_config: Array.isArray(sort) ? sort : [sort],
        include_aggregations: options?.include_aggregations,
      },
    );
    const coreQueryClause = tempSearchBodyFromBuilder.query;

    const offsetBuildOptions: OffsetQueryBuildOptions = {
      ...(options || {}),
      sortConfig: Array.isArray(sort) ? sort : [sort],
      highlightConfig: tempSearchBodyFromBuilder.highlight || highlightConfig,
      sourceFields:
        tempSearchBodyFromBuilder._source !== undefined
          ? tempSearchBodyFromBuilder._source
          : sourceFields,
    };

    const searchBody = this.offsetPaginationStrategy.buildSearchBody(
      coreQueryClause,
      offsetBuildOptions,
    );
    this.logger.debug(
      'PagedCustomSortSearch body from OffsetPaginationStrategy:',
      JSON.stringify(searchBody),
    );

    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;

    // Use the new formatter
    return this.pagedSearchResponseFormatter.formatOffsetResponse(
      response,
      offsetBuildOptions,
    );
  }

  /**
   * Find similar rules
   * @param ruleId Reference rule ID
   * @param options Similar rules options
   * @returns Similar rules
   */
  async findSimilarRules(
    ruleId: string,
    options: SimilarRulesQueryDto,
  ): Promise<SimilarRulesResponseDto> {
    this.logger.debug(`Finding similar rules for reference rule: ${ruleId}`);

    const rule = (await this.openSearchService.get(
      this.indexSchema.indexAlias,
      ruleId,
    )) as OpenSearchGetResponse<RuleSource>;
    if (!rule || !rule.found) {
      throw new NotFoundException(`Rule with ID ${ruleId} not found`);
    }

    this.logger.debug('Reference rule:', rule);

    const fieldValues: SimilarRulesFieldValues = {
      content: rule._source.content,
      title: rule._source.title?.value,
      description: rule._source.description,
      mitre_techniques: rule._source.metadata?.mitre_techniques || [],
      mitre_tactics: rule._source.metadata?.mitre_tactics || [],
      tags: rule._source.metadata?.tags || [],
    };

    if (
      !fieldValues.content &&
      !fieldValues.title &&
      !fieldValues.description
    ) {
      throw new BadRequestException(
        'Reference rule must have content, title, or description',
      );
    }

    const searchBody = this.similarRulesQueryBuilder.build(
      ruleId,
      fieldValues,
      options,
      this.indexSchema.indexAlias,
    );

    this.logger.debug('MLT query from builder:', searchBody);

    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;
    this.logger.debug('Raw OpenSearch response:', response);

    return this.similarRulesResponseFormatter.format(
      response,
      fieldValues,
      ruleId,
      searchBody,
    );
  }

  async getSuggestions(
    prefix: string,
    size: number = 5,
    visibleGroupIds?: string[],
  ): Promise<SearchSuggestionsResponseDto> {
    try {
      let contextsForBuilder: { [key: string]: string[] } | undefined;
      if (visibleGroupIds && visibleGroupIds.length > 0) {
        contextsForBuilder = { owner_id: visibleGroupIds };
        this.logger.debug(
          'Preparing owner_id context for suggest query builder:',
          visibleGroupIds,
        );
      } else {
        this.logger.debug(
          'No visibleGroupIds provided, suggest query will not have context.',
        );
      }

      const suggestBody = this.suggestQueryBuilder.build(
        prefix,
        size,
        contextsForBuilder,
      );

      this.logger.debug(
        'Suggest query body from builder:',
        JSON.stringify(suggestBody),
      );

      const response = (await this.openSearchService.search(
        this.indexSchema.indexAlias,
        suggestBody,
      )) as OpenSearchResponse<RuleSource>;

      // Use the new formatter
      return this.suggestionsResponseFormatter.format(response);
    } catch (error: unknown) {
      this.logger.error(
        `Suggestions error: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  getMetadata(): SearchMetadataResponseDto {
    return {
      status: 'success',
      data: {
        searchable_fields: ['title', 'description', 'content'],
        filters: {
          rule_type: ['detection', 'hunting', 'compliance'],
          stage: ['development', 'testing', 'production'],
          status: ['draft', 'active', 'deprecated', 'in_review'],
        },
        sort_options: Object.values(SortField),
      },
    };
  }

  async simpleRawSearch(
    index: string,
    query: OpenSearchQueryClause,
    sort: OpenSearchSortClause[] | OpenSearchSortClause,
    size: number,
  ): Promise<OpenSearchResponse<RuleSource>> {
    const searchBody = {
      query: query,
      sort,
      size,
    };

    this.logger.log(
      `Custom paged search called with: ${JSON.stringify(searchBody)}`,
    );

    return (await this.openSearchService.search(
      index,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;
  }
}
