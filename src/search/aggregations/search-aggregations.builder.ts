import { Injectable, Logger } from '@nestjs/common';

/**
 * Interface for OpenSearch aggregation configuration
 */
export interface OpenSearchAggregationConfig {
  [aggregationName: string]: {
    terms?: {
      field: string;
      size?: number;
      order?: { [key: string]: 'asc' | 'desc' };
    };
    nested?: {
      path: string;
      aggs: {
        [nestedAggName: string]: {
          terms: {
            field: string;
            size?: number;
            order?: { [key: string]: 'asc' | 'desc' };
          };
        };
      };
    };
  };
}

/**
 * Service for building OpenSearch aggregations for search results
 */
@Injectable()
export class SearchAggregationsBuilder {
  private readonly logger = new Logger(SearchAggregationsBuilder.name);

  /**
   * Build aggregations for search results
   * @param topCount Number of top items to return for each aggregation (default: 10)
   * @returns OpenSearch aggregation configuration
   */
  buildSearchAggregations(topCount: number = 10): OpenSearchAggregationConfig {
    this.logger.debug(`Building search aggregations with topCount: ${topCount}`);

    const aggregations: OpenSearchAggregationConfig = {
      // Contributors aggregation
      contributors: {
        terms: {
          field: 'contributor.keyword',
          size: topCount,
          order: { _count: 'desc' },
        },
      },

      // Rule types aggregation
      rule_type: {
        terms: {
          field: 'rule_type',
          size: topCount,
          order: { _count: 'desc' },
        },
      },

      // Platforms aggregation (from metadata.platforms)
      platforms: {
        terms: {
          field: 'metadata.platforms',
          size: topCount,
          order: { _count: 'desc' },
        },
      },

      // Categories aggregation (from metadata.categories)
      categories: {
        terms: {
          field: 'metadata.categories',
          size: topCount,
          order: { _count: 'desc' },
        },
      },

      // Product services aggregation (from metadata.product_services)
      product_services: {
        terms: {
          field: 'metadata.product_services',
          size: topCount,
          order: { _count: 'desc' },
        },
      },

      // MITRE ATT&CK IDs aggregation (nested aggregation)
      mitre_attack_ids: {
        nested: {
          path: 'metadata.mitre_attack',
          aggs: {
            mitre_ids: {
              terms: {
                field: 'metadata.mitre_attack.mitre_id',
                size: topCount,
                order: { _count: 'desc' },
              },
            },
          },
        },
      },
    };

    this.logger.debug('Built aggregations:', JSON.stringify(aggregations, null, 2));
    return aggregations;
  }

  /**
   * Build aggregations for a specific subset of fields
   * @param fields Array of field names to include in aggregations
   * @param topCount Number of top items to return for each aggregation
   * @returns OpenSearch aggregation configuration
   */
  buildCustomAggregations(
    fields: string[],
    topCount: number = 10,
  ): OpenSearchAggregationConfig {
    this.logger.debug(
      `Building custom aggregations for fields: ${fields.join(', ')} with topCount: ${topCount}`,
    );

    const aggregations: OpenSearchAggregationConfig = {};
    const allAggregations = this.buildSearchAggregations(topCount);

    // Only include requested fields
    for (const field of fields) {
      if (allAggregations[field]) {
        aggregations[field] = allAggregations[field];
      } else {
        this.logger.warn(`Unknown aggregation field requested: ${field}`);
      }
    }

    return aggregations;
  }

  /**
   * Get the list of available aggregation field names
   * @returns Array of available aggregation field names
   */
  getAvailableAggregationFields(): string[] {
    return [
      'contributors',
      'rule_type',
      'platforms',
      'categories',
      'product_services',
      'mitre_attack_ids',
    ];
  }
}
