import { Injectable, Logger } from '@nestjs/common';
import { SearchAggregationsDto, AggregationBucketDto } from '../dtos/search-response.dto';

/**
 * Interface for OpenSearch aggregation bucket
 */
export interface OpenSearchAggregationBucket {
  key: string;
  doc_count: number;
}

/**
 * Interface for OpenSearch terms aggregation response
 */
export interface OpenSearchTermsAggregationResponse {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: OpenSearchAggregationBucket[];
}

/**
 * Interface for OpenSearch nested aggregation response
 */
export interface OpenSearchNestedAggregationResponse {
  doc_count: number;
  [nestedAggName: string]: any;
}

/**
 * Interface for OpenSearch aggregations response
 */
export interface OpenSearchAggregationsResponse {
  contributors?: OpenSearchTermsAggregationResponse;
  rule_type?: OpenSearchTermsAggregationResponse;
  platforms?: OpenSearchTermsAggregationResponse;
  categories?: OpenSearchTermsAggregationResponse;
  product_services?: OpenSearchTermsAggregationResponse;
  mitre_attack_ids?: OpenSearchNestedAggregationResponse;
}

/**
 * Service for formatting OpenSearch aggregation responses
 */
@Injectable()
export class SearchAggregationsFormatter {
  private readonly logger = new Logger(SearchAggregationsFormatter.name);

  /**
   * Format OpenSearch aggregations response into SearchAggregationsDto
   * @param aggregationsResponse Raw OpenSearch aggregations response
   * @returns Formatted SearchAggregationsDto
   */
  formatAggregations(
    aggregationsResponse: OpenSearchAggregationsResponse,
  ): SearchAggregationsDto {
    this.logger.debug('Formatting aggregations response');

    const result: SearchAggregationsDto = {
      contributors: this.formatTermsAggregation(aggregationsResponse.contributors),
      rule_type: this.formatTermsAggregation(aggregationsResponse.rule_type),
      platforms: this.formatTermsAggregation(aggregationsResponse.platforms),
      categories: this.formatTermsAggregation(aggregationsResponse.categories),
      product_services: this.formatTermsAggregation(aggregationsResponse.product_services),
      mitre_attack_ids: this.formatNestedMitreAggregation(aggregationsResponse.mitre_attack_ids),
    };

    this.logger.debug('Formatted aggregations:', JSON.stringify(result, null, 2));
    return result;
  }

  /**
   * Format a terms aggregation response
   * @param termsAgg OpenSearch terms aggregation response
   * @returns Array of AggregationBucketDto
   */
  private formatTermsAggregation(
    termsAgg?: OpenSearchTermsAggregationResponse,
  ): AggregationBucketDto[] {
    if (!termsAgg || !termsAgg.buckets) {
      return [];
    }

    return termsAgg.buckets.map((bucket) => ({
      key: bucket.key,
      doc_count: bucket.doc_count,
    }));
  }

  /**
   * Format the nested MITRE ATT&CK aggregation response
   * @param nestedAgg OpenSearch nested aggregation response
   * @returns Array of AggregationBucketDto
   */
  private formatNestedMitreAggregation(
    nestedAgg?: OpenSearchNestedAggregationResponse,
  ): AggregationBucketDto[] {
    if (!nestedAgg || !nestedAgg.mitre_ids) {
      return [];
    }

    const mitreIdsAgg = nestedAgg.mitre_ids as OpenSearchTermsAggregationResponse;
    return this.formatTermsAggregation(mitreIdsAgg);
  }

  /**
   * Check if aggregations response has any data
   * @param aggregationsResponse Raw OpenSearch aggregations response
   * @returns true if any aggregation has data, false otherwise
   */
  hasAggregationData(aggregationsResponse?: OpenSearchAggregationsResponse): boolean {
    if (!aggregationsResponse) {
      return false;
    }

    const hasContributors = (aggregationsResponse.contributors?.buckets?.length ?? 0) > 0;
    const hasRuleTypes = (aggregationsResponse.rule_type?.buckets?.length ?? 0) > 0;
    const hasPlatforms = (aggregationsResponse.platforms?.buckets?.length ?? 0) > 0;
    const hasCategories = (aggregationsResponse.categories?.buckets?.length ?? 0) > 0;
    const hasProductServices = (aggregationsResponse.product_services?.buckets?.length ?? 0) > 0;
    const hasMitreIds = (aggregationsResponse.mitre_attack_ids?.mitre_ids?.buckets?.length ?? 0) > 0;

    return hasContributors || hasRuleTypes || hasPlatforms || hasCategories || hasProductServices || hasMitreIds;
  }

  /**
   * Get aggregation statistics
   * @param aggregationsResponse Raw OpenSearch aggregations response
   * @returns Object with aggregation statistics
   */
  getAggregationStats(aggregationsResponse?: OpenSearchAggregationsResponse): {
    totalContributors: number;
    totalRuleTypes: number;
    totalPlatforms: number;
    totalCategories: number;
    totalProductServices: number;
    totalMitreIds: number;
  } {
    if (!aggregationsResponse) {
      return {
        totalContributors: 0,
        totalRuleTypes: 0,
        totalPlatforms: 0,
        totalCategories: 0,
        totalProductServices: 0,
        totalMitreIds: 0,
      };
    }

    return {
      totalContributors: aggregationsResponse.contributors?.buckets?.length ?? 0,
      totalRuleTypes: aggregationsResponse.rule_type?.buckets?.length ?? 0,
      totalPlatforms: aggregationsResponse.platforms?.buckets?.length ?? 0,
      totalCategories: aggregationsResponse.categories?.buckets?.length ?? 0,
      totalProductServices: aggregationsResponse.product_services?.buckets?.length ?? 0,
      totalMitreIds: aggregationsResponse.mitre_attack_ids?.mitre_ids?.buckets?.length ?? 0,
    };
  }
}
