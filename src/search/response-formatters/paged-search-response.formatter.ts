import { Injectable } from '@nestjs/common';
import {
  OpenSearchResponse,
  RuleSource,
  OpenSearchTermsAggregationResult,
  OpenSearchNestedAggregationResult,
} from '../models/opensearch-types';
import {
  SearchResponseDto,
  SearchResultItemDto,
} from '../dtos/search-response.dto';
import { PagedSearchResponseDto } from '../dtos/page-search-options.dto';
import {
  AggregationBucketDto,
  SearchAggregationsDto,
} from '../dtos/search-aggregations.dto';
import { OffsetQueryBuildOptions } from '../pagination/pagination-strategy.interface';
import { RuleStatus, RuleType } from '../../rules/models/rule.model';

/**
 * Formats OpenSearch responses into PagedSearchResponseDto.
 */
@Injectable()
export class PagedSearchResponseFormatter {
  /**
   * Formats the raw OpenSearch response (from offset-based pagination) into a PagedSearchResponseDto.
   * @param response The raw OpenSearch response.
   * @param options The options used for the offset-based query.
   * @returns A PagedSearchResponseDto.
   */
  formatOffsetResponse(
    response: OpenSearchResponse<RuleSource>,
    options: OffsetQueryBuildOptions,
  ): PagedSearchResponseDto {
    const { hits, aggregations: rawAggregations } = response;
    const { page = 1, size = 20 } = options;

    const totalItems =
      typeof hits.total === 'number' ? hits.total : hits.total.value;
    const totalPages = Math.ceil(totalItems / size) || 0;

    const data: SearchResultItemDto[] = hits.hits.map((hit) => {
      const processedHighlights: Record<string, string[]> | undefined =
        hit.highlight
          ? Object.entries(hit.highlight).reduce(
              (acc, [key, snippets]) => {
                let targetKey: string;
                if (key.startsWith('title.value') || key === 'title') {
                  targetKey = 'title';
                } else if (
                  key.startsWith('description.') ||
                  key === 'description'
                ) {
                  targetKey = 'description';
                } else if (
                  key.startsWith('content.') ||
                  key === 'content' ||
                  key.startsWith('all_text.') ||
                  key === 'all_text'
                ) {
                  targetKey = 'content';
                } else {
                  targetKey = key; // Pass through other keys (e.g., metadata fields)
                }

                if (!acc[targetKey]) {
                  acc[targetKey] = [];
                }
                // Deduplicate snippets for the same targetKey before pushing
                snippets.forEach((snippet) => {
                  if (!acc[targetKey].includes(snippet)) {
                    acc[targetKey].push(snippet);
                  }
                });
                return acc;
              },
              {} as Record<string, string[]>,
            )
          : undefined;

      return {
        id: hit._id,
        title: hit._source.title,
        description: hit._source.description || '',
        content: hit._source.content || '',
        created_at: hit._source.created_at,
        updated_at: hit._source.updated_at,
        owner_id: hit._source.owner_id,
        rule_type: hit._source.rule_type as RuleType,
        status: hit._source.status as RuleStatus,
        version: hit._source.version ?? 1,
        ai_generated: hit._source.ai_generated,
        created_by: hit._source.created_by,
        contributor: hit._source.contributor,
        likes: hit._source.likes ?? 0,
        downloads: hit._source.downloads ?? 0,
        dislikes: hit._source.dislikes ?? 0,
        bookmarks: hit._source.bookmarks ?? 0,
        group_id: hit._source.group_id,
        group_name: hit._source.group_name,
        tags: hit._source.metadata?.tags ?? [],
        mitre_tactics: hit._source.metadata?.mitre_tactics ?? [],
        mitre_techniques: hit._source.metadata?.mitre_techniques ?? [],
        metadata: hit._source.metadata,
        test_cases: hit._source.test_cases ?? [],
        published_at: hit._source.published_at,
        is_bookmarked: hit._source.is_bookmarked,
        is_liked: hit._source.is_liked,
        is_disliked: hit._source.is_disliked,
        highlight: processedHighlights,
        _score: hit._score || 0,
      };
    });

    // Process aggregations
    let processedAggregations: SearchAggregationsDto | undefined = undefined;
    if (rawAggregations) {
      processedAggregations = {};
      const mapBuckets = (
        aggName: string,
      ): AggregationBucketDto[] | undefined => {
        const aggResult = rawAggregations[aggName];
        if (aggName === 'mitre_attack_ids') {
          const nestedResult = aggResult as OpenSearchNestedAggregationResult;
          const termsSubAgg = nestedResult?.[
            'ids'
          ] as OpenSearchTermsAggregationResult;
          if (termsSubAgg?.buckets) {
            return termsSubAgg.buckets.map((bucket) => ({
              key: bucket.key,
              doc_count: bucket.doc_count,
            }));
          }
        } else if (aggResult && 'buckets' in aggResult) {
          const termsAgg = aggResult as OpenSearchTermsAggregationResult;
          if (termsAgg.buckets) {
            return termsAgg.buckets.map((bucket) => ({
              key: bucket.key,
              doc_count: bucket.doc_count,
            }));
          }
        }
        return undefined;
      };
      processedAggregations.contributors = mapBuckets('contributors');
      processedAggregations.platforms = mapBuckets('platforms');
      processedAggregations.categories = mapBuckets('categories');
      processedAggregations.product_services = mapBuckets('product_services');
      processedAggregations.rule_type = mapBuckets('rule_type');
      processedAggregations.mitre_attack_ids = mapBuckets('mitre_attack_ids');
    }

    return {
      status: 'success',
      meta: {
        total: totalItems,
        size: data.length, // Number of items in the current page
        current_page: page,
        total_pages: totalPages,
        page_size: size, // The requested page size
        has_next_page: page < totalPages,
        has_prev_page: page > 1,
        // next_cursor and prev_cursor are not applicable for offset pagination
      },
      data,
      aggregations: processedAggregations,
    };
  }

  /**
   * Formats a SearchResponseDto (from cursor-based pagination) into a PagedSearchResponseDto.
   * This is used by advancedSearch when in cursor mode but needs to return PagedSearchResponseDto.
   * @param cursorResponse The SearchResponseDto from a cursor-based operation.
   * @param options Options containing page and size, though page is typically 1 for this conversion.
   * @returns A PagedSearchResponseDto.
   */
  formatCursorResponse(
    cursorResponse: SearchResponseDto,
    options: { page: number; size: number },
  ): PagedSearchResponseDto {
    const { page, size } = options;
    const { meta, data, status, aggregations } = cursorResponse;

    const totalPages = meta.total > 0 ? Math.ceil(meta.total / size) : 0;

    return {
      status,
      meta: {
        total: meta.total,
        size: meta.size,
        current_page: page,
        total_pages: totalPages,
        page_size: size,
        has_next_page: !!meta.next_cursor,
        has_prev_page: !!meta.prev_cursor,
        next_cursor: meta.next_cursor,
        prev_cursor: meta.prev_cursor,
      },
      data,
      aggregations,
    };
  }
}
