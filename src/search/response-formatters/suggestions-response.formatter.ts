import { Injectable } from '@nestjs/common';
import {
  OpenSearchResponse,
  RuleSource, // Though not directly used in hits, it's part of OpenSearchResponse<T>
} from '../models/opensearch-types';
import { SearchSuggestionsResponseDto } from '../dtos/search-response.dto';

/**
 * Formats OpenSearch responses for suggestions into SearchSuggestionsResponseDto.
 */
@Injectable()
export class SuggestionsResponseFormatter {
  /**
   * Formats the raw OpenSearch suggest response into a SearchSuggestionsResponseDto.
   * @param response The raw OpenSearch response, expected to contain a `suggest` field.
   * @returns A SearchSuggestionsResponseDto.
   */
  format(
    response: OpenSearchResponse<RuleSource>, // RuleSource is generic here, actual data is in response.suggest
  ): SearchSuggestionsResponseDto {
    const uniqueSuggestions = new Map<string, string>();

    // The suggest query in SuggestQueryBuilder uses 'rule_title_suggestions'
    if (response.suggest?.rule_title_suggestions?.[0]?.options) {
      response.suggest.rule_title_suggestions[0].options.forEach((option) => {
        if (option.text) {
          const lowerCaseText = option.text.toLowerCase();
          if (!uniqueSuggestions.has(lowerCaseText)) {
            uniqueSuggestions.set(lowerCaseText, option.text);
          }
        }
      });
    }

    return {
      status: 'success',
      data: Array.from(uniqueSuggestions.values()),
    };
  }
}
