import { Injectable, BadRequestException } from '@nestjs/common';
import * as crypto from 'crypto';
import {
  OpenSearchResponse,
  OpenSearchHit,
  RuleSource,
  OpenSearchExplanation,
  OpenSearchSearchRequestBody, // To get stop_words from MLT query
} from '../models/opensearch-types';
import {
  SimilarRulesResponseDto,
  SimilarRuleDto,
} from '../dtos/similar-rules.dto';
import { SimilarRulesFieldValues } from '../models/search-types';
import { RuleType } from '../../rules/models/rule.model';

/**
 * Formats OpenSearch responses for similar rules search into SimilarRulesResponseDto.
 */
@Injectable()
export class SimilarRulesResponseFormatter {
  /**
   * Formats the raw OpenSearch response into a SimilarRulesResponseDto.
   * @param response The raw OpenSearch response from an MLT query.
   * @param originalRuleFieldValues The content/title/description fields of the original rule.
   * @param referenceRuleId The ID of the rule for which similar rules are being found.
   * @param mltQueryBody The OpenSearch query body used for the MLT search (to extract stop_words).
   * @returns A SimilarRulesResponseDto.
   */
  format(
    response: OpenSearchResponse<RuleSource>,
    originalRuleFieldValues: SimilarRulesFieldValues,
    referenceRuleId: string,
    mltQueryBody: OpenSearchSearchRequestBody, // For accessing stop_words
  ): SimilarRulesResponseDto {
    const { hits } = response;

    if (
      !originalRuleFieldValues.content &&
      !originalRuleFieldValues.title &&
      !originalRuleFieldValues.description
    ) {
      // This check should ideally happen before calling the formatter, but good for safety.
      throw new BadRequestException(
        'Original rule must have content, title, or description for similarity comparison.',
      );
    }

    let originalContentHash = '';
    if (originalRuleFieldValues.content) {
      originalContentHash = crypto
        .createHash('sha256')
        .update(originalRuleFieldValues.content)
        .digest('hex');
    }

    const maxScore = hits.max_score || 1; // Avoid division by zero if max_score is null/0
    const stopWordsForFilter =
      mltQueryBody.query?.more_like_this?.stop_words || [];

    const results: SimilarRuleDto[] = hits.hits.map(
      (hit: OpenSearchHit<RuleSource>) => {
        let isExactDuplicate = false;
        if (originalRuleFieldValues.content && hit._source.content) {
          const hitContentHash = crypto
            .createHash('sha256')
            .update(hit._source.content)
            .digest('hex');
          isExactDuplicate = originalContentHash === hitContentHash;
        }

        const matchedTerms = new Set<string>();
        const extractTerms = (
          explanation: OpenSearchExplanation | undefined,
        ) => {
          if (!explanation) return;
          if (explanation.description?.includes('weight(')) {
            const term = explanation.description.match(/weight\(([^:]+)/)?.[1];
            if (term) matchedTerms.add(term);
          }
          if (explanation.details) {
            explanation.details.forEach((detail) => extractTerms(detail));
          }
        };

        if (hit.explanation) {
          extractTerms(hit.explanation);
        }

        let similarityPercentage: number;
        if (isExactDuplicate) {
          similarityPercentage = 100;
        } else {
          const scorePercentage = ((hit._score ?? 0) / maxScore) * 100;
          let termMatchPercentage = 0;

          const getTermsFromString = (str: string | undefined): string[] => {
            if (!str) return [];
            return str
              .toLowerCase()
              .split(/[^a-z0-9-]+/)
              .filter(
                (term) =>
                  term.length >=
                    (mltQueryBody.query?.more_like_this?.min_term_freq || 2) && // use mlt min_term_freq
                  !stopWordsForFilter.includes(term),
              );
          };

          let originalTerms: string[] = [];
          if (originalRuleFieldValues.content) {
            originalTerms = getTermsFromString(originalRuleFieldValues.content);
          } else if (originalRuleFieldValues.title) {
            originalTerms = getTermsFromString(originalRuleFieldValues.title);
          } else if (originalRuleFieldValues.description) {
            originalTerms = getTermsFromString(
              originalRuleFieldValues.description,
            );
          }

          if (originalTerms.length > 0) {
            // Count how many of the *matched terms* from explanation are present in *original significant terms*
            // This is a more robust way than just matchedTerms.size / originalTerms.length
            // because matchedTerms can come from various fields based on MLT query behavior.
            let relevantMatchedCount = 0;
            matchedTerms.forEach((term) => {
              if (originalTerms.includes(term.toLowerCase())) {
                relevantMatchedCount++;
              }
            });
            termMatchPercentage =
              (relevantMatchedCount / originalTerms.length) * 100;
          } else {
            termMatchPercentage = 0; // No terms to match against
          }

          similarityPercentage = Math.min(
            100,
            Math.round((scorePercentage + termMatchPercentage) / 2),
          );
        }

        return {
          id: hit._id,
          title: hit._source.title,
          description: hit._source.description || '',
          rule_type: hit._source.rule_type as RuleType,
          severity: hit._source.severity || '',
          _score: hit._score ?? 0,
          similarity_percentage: similarityPercentage,
        };
      },
    );

    return {
      status: 'success',
      meta: {
        reference_rule: referenceRuleId,
        total: typeof hits.total === 'number' ? hits.total : hits.total.value,
      },
      data: results,
    };
  }
}
