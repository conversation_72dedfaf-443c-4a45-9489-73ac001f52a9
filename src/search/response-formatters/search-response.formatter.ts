import { Injectable } from '@nestjs/common';
import {
  OpenSearchResponse,
  RuleSource,
  OpenSearchTermsAggregationResult,
  OpenSearchNestedAggregationResult,
} from '../models/opensearch-types';
import {
  SearchResponseDto,
  SearchResultItemDto,
} from '../dtos/search-response.dto';
import {
  AggregationBucketDto,
  SearchAggregationsDto,
} from '../dtos/search-aggregations.dto';
import { CursorQueryBuildOptions } from '../pagination/pagination-strategy.interface';
import { RuleStatus, RuleType } from '../../rules/models/rule.model';

/**
 * Formats OpenSearch responses into SearchResponseDto for cursor-based pagination.
 */
@Injectable()
export class SearchResponseFormatter {
  /**
   * Formats the raw OpenSearch response into a SearchResponseDto.
   * @param response The raw OpenSearch response.
   * @param options The options used for the cursor-based query.
   * @returns A SearchResponseDto.
   */
  format(
    response: OpenSearchResponse<RuleSource>,
    options: CursorQueryBuildOptions,
  ): SearchResponseDto {
    const { hits, aggregations: rawAggregations } = response;
    const data: SearchResultItemDto[] = hits.hits.map((hit) => {
      const processedHighlights: Record<string, string[]> | undefined =
        hit.highlight
          ? Object.entries(hit.highlight).reduce(
              (acc, [key, snippets]) => {
                let targetKey: string;
                if (key.startsWith('title.value') || key === 'title') {
                  targetKey = 'title';
                } else if (
                  key.startsWith('description.') ||
                  key === 'description'
                ) {
                  targetKey = 'description';
                } else if (
                  key.startsWith('content.') ||
                  key === 'content' ||
                  key.startsWith('all_text.') ||
                  key === 'all_text'
                ) {
                  targetKey = 'content';
                } else {
                  targetKey = key; // Pass through other keys (e.g., metadata fields)
                }

                if (!acc[targetKey]) {
                  acc[targetKey] = [];
                }
                // Deduplicate snippets for the same targetKey before pushing
                // This can happen if, for example, a term is matched in both 'content' and 'content.edge_ngram'
                // and both produce identical snippets.
                snippets.forEach((snippet) => {
                  if (!acc[targetKey].includes(snippet)) {
                    acc[targetKey].push(snippet);
                  }
                });
                return acc;
              },
              {} as Record<string, string[]>,
            )
          : undefined;

      return {
        id: hit._id,
        title: hit._source.title,
        description: hit._source.description || '',
        content: hit._source.content || '',
        created_at: hit._source.created_at,
        updated_at: hit._source.updated_at,
        owner_id: hit._source.owner_id,
        rule_type: hit._source.rule_type as RuleType,
        status: hit._source.status as RuleStatus,
        version: hit._source.version ?? 1,
        ai_generated: hit._source.ai_generated,
        created_by: hit._source.created_by,
        contributor: hit._source.contributor,
        likes: hit._source.likes ?? 0,
        downloads: hit._source.downloads ?? 0,
        dislikes: hit._source.dislikes ?? 0,
        bookmarks: hit._source.bookmarks ?? 0,
        group_id: hit._source.group_id,
        group_name: hit._source.group_name,
        tags: hit._source.metadata?.tags ?? [],
        mitre_tactics: hit._source.metadata?.mitre_tactics ?? [],
        mitre_techniques: hit._source.metadata?.mitre_techniques ?? [],
        metadata: hit._source.metadata,
        test_cases: hit._source.test_cases ?? [],
        published_at: hit._source.published_at,
        is_bookmarked: hit._source.is_bookmarked,
        is_liked: hit._source.is_liked,
        is_disliked: hit._source.is_disliked,
        highlight: processedHighlights, // Use the processed highlights
        _score: hit._score || 0,
      };
    });

    let nextCursor: string | undefined = undefined;
    let prevCursor: string | undefined = undefined;

    const effectiveSize = options.size || 20;

    if (
      hits.hits.length > effectiveSize &&
      hits.hits[effectiveSize - 1]?.sort
    ) {
      nextCursor = Buffer.from(
        JSON.stringify(hits.hits[effectiveSize - 1].sort),
      ).toString('base64');
      if (data.length > effectiveSize) {
        data.splice(effectiveSize);
      }
    } else if (data.length > effectiveSize) {
      data.splice(effectiveSize);
    }

    if (options.cursor) {
      prevCursor = options.cursor;
    }

    let processedAggregations: SearchAggregationsDto | undefined = undefined;
    if (rawAggregations) {
      processedAggregations = {};
      const mapBuckets = (
        aggName: string,
      ): AggregationBucketDto[] | undefined => {
        const aggResult = rawAggregations[aggName];

        if (aggName === 'mitre_attack_ids') {
          // Handle nested aggregation for mitre_attack_ids
          const nestedResult = aggResult as OpenSearchNestedAggregationResult;
          // The actual terms aggregation is a sub-aggregation named 'ids'
          const termsSubAgg = nestedResult?.[
            'ids'
          ] as OpenSearchTermsAggregationResult;
          if (termsSubAgg?.buckets) {
            return termsSubAgg.buckets.map((bucket) => ({
              key: bucket.key,
              doc_count: bucket.doc_count,
            }));
          }
        } else if (aggResult && 'buckets' in aggResult) {
          // Handle standard terms aggregations
          const termsAgg = aggResult as OpenSearchTermsAggregationResult;
          if (termsAgg.buckets) {
            return termsAgg.buckets.map((bucket) => ({
              key: bucket.key,
              doc_count: bucket.doc_count,
            }));
          }
        }
        return undefined;
      };

      processedAggregations.contributors = mapBuckets('contributors');
      processedAggregations.platforms = mapBuckets('platforms');
      processedAggregations.categories = mapBuckets('categories');
      processedAggregations.product_services = mapBuckets('product_services');
      processedAggregations.rule_type = mapBuckets('rule_type');
      processedAggregations.mitre_attack_ids = mapBuckets('mitre_attack_ids');
    }

    return {
      status: 'success',
      meta: {
        total: typeof hits.total === 'number' ? hits.total : hits.total.value,
        size: data.length,
        next_cursor: nextCursor,
        prev_cursor: prevCursor,
      },
      data,
      aggregations: processedAggregations,
    };
  }
}
