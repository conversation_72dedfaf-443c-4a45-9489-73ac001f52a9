import { Injectable, Logger } from '@nestjs/common';
import { QueryUtilityService } from './query.utility';
import { SearchFiltersDto } from '../dtos/search-filters.dto';
import {
  OpenSearchQueryClause,
  OpenSearchSearchRequestBody,
  OpenSearchSortClause,
} from '../models/opensearch-types';

const DEFAULT_SIZE = 20;

@Injectable()
export class BasicSearchQueryBuilder {
  private readonly logger = new Logger(BasicSearchQueryBuilder.name);

  constructor(private readonly queryUtilityService: QueryUtilityService) {}

  build(
    queryInput?: string,
    filters?: SearchFiltersDto,
    options?: {
      size?: number;
      from?: number; // For offset pagination
      sort_config?: OpenSearchSortClause[];
      search_after?: (string | number | boolean)[];
      include_aggregations?: boolean;
    },
  ): OpenSearchSearchRequestBody {
    this.logger.debug(
      `Building basic search query with: query='${queryInput}', filters='${JSON.stringify(filters)}', options='${JSON.stringify(options)}'`,
    );

    let queryClause: OpenSearchQueryClause;

    if (!queryInput || queryInput.trim() === '' || queryInput === '*') {
      // Handle empty, undefined, or wildcard query
      queryClause = {
        bool: {
          must: [{ match_all: {} }],
          filter: this.queryUtilityService.buildFilters(filters),
        },
      };
    } else {
      // At this point, 'queryInput' is a non-empty string and not '*'
      const query: string = queryInput; // Explicitly type for this block

      const fieldQuery = this.queryUtilityService.parseFieldQuery(query);
      if (fieldQuery) {
        queryClause = {
          match: {
            [fieldQuery.field]: {
              query: fieldQuery.value,
              analyzer: 'synonym_analyzer', // Example from original
            },
          },
        };
      } else {
        // This structure should reflect the original complex logic from the file for this 'else' path
        queryClause = {
          bool: {
            should: [
              // New clause for exact term matching for highlighting
              {
                multi_match: {
                  query: query,
                  fields: ['title.value^3.5', 'description^2.5', 'content^1.5'],
                  type: 'best_fields',
                  operator: 'OR',
                  analyzer: 'simple_text_analyzer', // Ensures we match against index-time analysis
                  // No 'analyzer: synonym_analyzer' here to ensure exact matching
                  boost: 1.8, // Boost to ensure it contributes to highlighting
                },
              },
              // Cross-field matching with synonyms
              {
                multi_match: {
                  query: query,
                  fields: [
                    'title.value^4',
                    'description^3',
                    'content^2',
                    'all_text^1.5',
                  ],
                  type: 'cross_fields',
                  analyzer: 'synonym_analyzer',
                  operator: 'OR',
                  minimum_should_match: '2<50% 5<40%', // from original
                  tie_breaker: 0.3, // from original
                  boost: 2.0, // from original
                },
              },
              // Exact phrase matching
              {
                multi_match: {
                  query: query,
                  fields: [
                    'title.value.shingles^4', // from original
                    'description.shingles^3', // from original
                    'content.shingles^2', // from original
                  ],
                  type: 'phrase',
                  boost: 1.5, // from original
                  slop: 2, // from original
                },
              },
              // Synonym phrase matching
              {
                multi_match: {
                  query: query,
                  fields: [
                    'title.value^4',
                    'description^3',
                    'content^2',
                    'all_text^1.5',
                  ],
                  type: 'phrase',
                  analyzer: 'synonym_analyzer',
                  boost: 1.2, // from original
                  slop: 3, // from original
                },
              },
              // Edge N-gram matching for partial words
              {
                multi_match: {
                  query: query,
                  fields: [
                    'title.value.edge_ngram^0.5', // from original
                    'description.edge_ngram^0.4', // from original
                    'content.edge_ngram^0.3', // from original
                    'all_text.edge_ngram^0.2', // from original
                  ],
                  type: 'most_fields',
                  operator: 'OR',
                },
              },
              // Metadata field matching (keyword fields)
              {
                bool: {
                  should: [
                    {
                      terms: {
                        'metadata.tags': query.toLowerCase().split(/\s+/),
                      },
                    },
                    {
                      terms: {
                        'metadata.mitre_tactics': query
                          .toLowerCase()
                          .split(/\s+/),
                      },
                    },
                    {
                      terms: {
                        'metadata.mitre_techniques': query
                          .toLowerCase()
                          .split(/\s+/),
                      },
                    },
                    {
                      terms: {
                        'metadata.data_sources': query
                          .toLowerCase()
                          .split(/\s+/),
                      },
                    },
                    {
                      terms: {
                        'metadata.platforms': query.toLowerCase().split(/\s+/),
                      },
                    },
                  ],
                  minimum_should_match: 1,
                },
              },
              // Nested query for MITRE ATT&CK objects
              {
                nested: {
                  path: 'metadata.mitre_attack', // from original
                  query: {
                    bool: {
                      should: [
                        {
                          match: {
                            'metadata.mitre_attack.mitre_id': {
                              query: query,
                              boost: 3.0,
                            },
                          },
                        },
                        {
                          match: {
                            'metadata.mitre_attack.name': {
                              query: query,
                              analyzer: 'synonym_analyzer',
                              boost: 2.0,
                            },
                          },
                        },
                        {
                          match: {
                            'metadata.mitre_attack.parent_name': {
                              query: query,
                              analyzer: 'synonym_analyzer',
                              boost: 1.5,
                            },
                          },
                        },
                        // Added based on original structure, ensure these match
                        {
                          match: {
                            'metadata.mitre_attack.tactic': {
                              query: query,
                              analyzer: 'synonym_analyzer',
                              boost: 1.5,
                            },
                          },
                        },
                        {
                          match: {
                            'metadata.mitre_attack.software': {
                              query: query,
                              analyzer: 'synonym_analyzer',
                              boost: 1.0,
                            },
                          },
                        },
                        {
                          match: {
                            'metadata.mitre_attack.group': {
                              query: query,
                              analyzer: 'synonym_analyzer',
                              boost: 1.0,
                            },
                          },
                        },
                      ],
                      minimum_should_match: 1,
                    },
                  },
                },
              },
            ],
            minimum_should_match: 1, // from original
            ...(filters &&
              this.queryUtilityService.buildFilters(filters).length > 0 && {
                filter: this.queryUtilityService.buildFilters(filters),
              }),
          },
        };
      }
    }

    // Define aggregations
    const aggregationsObject = {
      contributors: {
        terms: { field: 'contributor.keyword', size: 10 },
      },
      platforms: {
        terms: { field: 'metadata.platforms', size: 10 },
      },
      categories: {
        terms: { field: 'metadata.categories.keyword', size: 10 },
      },
      product_services: {
        terms: { field: 'metadata.product_services.keyword', size: 10 },
      },
      rule_type: {
        terms: { field: 'rule_type', size: 10 },
      },
      mitre_attack_ids: {
        nested: {
          path: 'metadata.mitre_attack',
        },
        aggs: {
          ids: {
            terms: { field: 'metadata.mitre_attack.mitre_id', size: 10 },
          },
        },
      },
    };

    const baseQueryBody: OpenSearchSearchRequestBody = {
      query: {
        function_score: {
          query: queryClause,
          functions: [
            // from original
            {
              filter: { term: { stage: { value: 'production' } } },
              weight: 1.2,
            },
            {
              filter: { term: { validation_status: { value: 'validated' } } },
              weight: 1.1,
            },
            {
              gauss: {
                created_at: { origin: 'now', scale: '30d', decay: 0.5 },
              },
              weight: 0.1,
            },
          ],
          score_mode: 'sum', // from original
          boost_mode: 'multiply', // from original
        },
      },
      highlight: {
        // from original
        fields: {
          'title.value': {},
          description: {},
          content: { fragment_size: 150, number_of_fragments: 3 },
          'metadata.mitre_attack.name': {},
          'metadata.mitre_attack.mitre_id': {},
          'metadata.mitre_attack.parent_name': {},
          // Add edge_ngram fields for partial highlighting
          'title.value.edge_ngram': {},
          'description.edge_ngram': {},
          'content.edge_ngram': {},
          'all_text.edge_ngram': {},
        },
        pre_tags: ['<strong>'],
        post_tags: ['</strong>'],
      },
      _source: true,
      // Conditionally add aggregations
      // aggs: aggregations, // Old way
    };

    if (options?.include_aggregations) {
      baseQueryBody.aggs = aggregationsObject;
    }

    // Apply pagination and sorting from options
    if (options?.size !== undefined) {
      baseQueryBody.size = options.size; // SearchService passes the final size (e.g. requestedSize or requestedSize + 1)
    } else {
      // Default if not specified, typically for cursor-based where SearchService might not pass it if using defaults
      baseQueryBody.size = DEFAULT_SIZE + 1;
    }

    if (options?.from !== undefined) {
      baseQueryBody.from = options.from;
    }

    if (options?.sort_config) {
      baseQueryBody.sort = options.sort_config;
    }

    // search_after is for cursor pagination, 'from' is for offset. They are mutually exclusive in OpenSearch.
    if (options?.search_after && options?.from === undefined) {
      baseQueryBody.search_after = options.search_after;
    }

    return baseQueryBody;
  }
}
