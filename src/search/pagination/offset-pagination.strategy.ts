import { Injectable, Logger, NotImplementedException } from '@nestjs/common';
import { PagedSearchResponseDto } from '../dtos/page-search-options.dto';
import {
  HighlightQuery,
  OpenSearchQueryClause,
  OpenSearchResponse,
  OpenSearchSearchRequestBody,
  OpenSearchSortClause,
  RuleSource,
} from '../models/opensearch-types';
import {
  OffsetQueryBuildOptions,
  PaginationStrategy,
} from './pagination-strategy.interface';
import { SortField } from '../models/sort-field.enum';
import { SortOrder } from '../models/sort-order.enum';

@Injectable() // Corrected generic order
export class OffsetPaginationStrategy
  implements PaginationStrategy<PagedSearchResponseDto, OffsetQueryBuildOptions>
{
  private readonly logger = new Logger(OffsetPaginationStrategy.name);

  buildSearchBody(
    coreQuery: OpenSearchQueryClause | undefined,
    options: OffsetQueryBuildOptions,
  ): OpenSearchSearchRequestBody {
    const {
      page = 1,
      size = 20,
      sort_by = SortField.RELEVANCE,
      sort_order = SortOrder.DESC,
      highlightConfig,
      sourceFields: optionSourceFields,
      sortConfig,
      include_aggregations = false,
    } = options;

    const from = (page - 1) * size;
    let resolvedSort: OpenSearchSortClause[];

    if (sortConfig && sortConfig.length > 0) {
      this.logger.debug('Using provided sortConfig');
      resolvedSort = sortConfig;
    } else {
      this.logger.debug('Generating sortConfig using sort_by/sort_order');
      switch (sort_by) {
        case SortField.RELEVANCE:
          resolvedSort = [{ _score: SortOrder.DESC }, { id: SortOrder.ASC }];
          break;
        case SortField.UPDATED_AT:
          resolvedSort = [{ updated_at: sort_order }, { id: SortOrder.ASC }];
          break;
        case SortField.PUBLISHED_AT:
          resolvedSort = [{ published_at: sort_order }, { id: SortOrder.ASC }];
          break;
        case SortField.CREATED_AT:
          resolvedSort = [{ created_at: sort_order }, { id: SortOrder.ASC }];
          break;
        case SortField.METADATA_DATE:
          resolvedSort = [
            { 'metadata.date': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.METADATA_MODIFIED:
          resolvedSort = [
            { 'metadata.modified': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.TITLE:
          resolvedSort = [
            { 'title.value.keyword': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.SEVERITY:
          resolvedSort = [
            { 'metadata.severity': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.LIKES:
          resolvedSort = [{ likes: sort_order }, { id: SortOrder.ASC }];
          break;
        case SortField.DOWNLOADS:
          resolvedSort = [{ downloads: sort_order }, { id: SortOrder.ASC }];
          break;
        case SortField.DISLIKES:
          resolvedSort = [{ dislikes: sort_order }, { id: SortOrder.ASC }];
          break;
        case SortField.CONTRIBUTOR:
          resolvedSort = [
            { 'contributor.keyword': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.GROUP_NAME:
          resolvedSort = [
            { 'group_name.keyword': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.RULE_TYPE:
          resolvedSort = [{ rule_type: sort_order }, { id: SortOrder.ASC }];
          break;
        case SortField.AUTHOR:
          resolvedSort = [
            { 'metadata.author.keyword': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.MITRE_TACTIC:
          resolvedSort = [
            { 'metadata.mitre_tactic.keyword': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        case SortField.MITRE_TECHNIQUE:
          resolvedSort = [
            { 'metadata.mitre_technique.keyword': sort_order },
            { id: SortOrder.ASC },
          ];
          break;
        default:
          this.logger.warn(
            `Unrecognized sort_by field: ${String(sort_by)}, defaulting to relevance.`,
          );
          resolvedSort = [{ _score: SortOrder.DESC }, { id: SortOrder.ASC }];
          break;
      }
    }

    const searchBody: OpenSearchSearchRequestBody = {
      query: coreQuery,
      sort: resolvedSort,
      from,
      size,
    };

    if (include_aggregations) {
      searchBody.aggs = {
        contributors: { terms: { field: 'contributor.keyword', size: 10 } },
        platforms: { terms: { field: 'metadata.platforms', size: 10 } },
        categories: {
          terms: { field: 'metadata.categories.keyword', size: 10 },
        },
        product_services: {
          terms: { field: 'metadata.product_services.keyword', size: 10 },
        },
        rule_type: { terms: { field: 'rule_type', size: 10 } },
        mitre_attack_ids: {
          nested: { path: 'metadata.mitre_attack' },
          aggs: {
            ids: {
              terms: {
                field: 'metadata.mitre_attack.mitre_id',
                size: 10,
              },
            },
          },
        },
      };
    }

    // Assign _source only if optionSourceFields is explicitly provided, otherwise OpenSearch default applies (true)
    if (optionSourceFields !== undefined) {
      searchBody._source = optionSourceFields;
    } else {
      searchBody._source = true; // Default if not specified in options
    }

    if (highlightConfig) {
      searchBody.highlight = highlightConfig as HighlightQuery;
    }

    this.logger.debug(
      `OffsetPaginationStrategy search body: ${JSON.stringify(searchBody)}`,
    );
    return searchBody;
  }

  /**
   * This method is implemented to satisfy the PaginationStrategy interface.
   * However, the actual formatting logic has been moved to PagedSearchResponseFormatter.
   * This method should ideally not be called directly by SearchService after the refactor.
   * @throws NotImplementedException
   */
  formatResponse(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _openSearchResponse: OpenSearchResponse<RuleSource>,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _options: OffsetQueryBuildOptions,
  ): PagedSearchResponseDto {
    this.logger.warn(
      'formatResponse called on OffsetPaginationStrategy. This method is deprecated and should not be used. Formatting is handled by PagedSearchResponseFormatter.',
    );
    throw new NotImplementedException(
      'Formatting for offset pagination is handled by PagedSearchResponseFormatter.',
    );
  }
}
