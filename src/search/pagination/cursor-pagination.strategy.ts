import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { QueryUtilityService } from '../query-builders/query.utility'; // Corrected path
import {
  PaginationStrategy,
  CursorQueryBuildOptions,
  SearchResponseDto,
  RuleSource,
  OpenSearchResponse,
  OpenSearchHit,
  SearchCursor,
  SortField,
  SortOrder,
  PaginationUtility,
  PaginationDirection,
} from './pagination-strategy.interface';
import { OpenSearchQueryClause } from '../models/opensearch-types';
import { RuleStatus, RuleType } from '../../rules/models/rule.model'; // Import RuleStatus and RuleType

@Injectable()
export class CursorPaginationStrategy
  implements PaginationStrategy<SearchResponseDto, CursorQueryBuildOptions>
{
  private readonly logger = new Logger(CursorPaginationStrategy.name);

  constructor(private readonly queryUtilityService: QueryUtilityService) {}

  public buildSearchBody(
    coreQuery: OpenSearchQueryClause | undefined,
    options: CursorQueryBuildOptions,
  ): Record<string, any> {
    const {
      size = 20,
      sort_by = SortField.RELEVANCE,
      sort_order = SortOrder.DESC,
      cursor,
      direction = PaginationDirection.FORWARD,
      highlightConfig,
      sourceFields = true, // Default to true to fetch source
      include_aggregations = false, // Added include_aggregations from options
    } = options;

    let parsedCursor: SearchCursor | undefined;
    if (cursor) {
      parsedCursor = this.validateCursor(cursor, sort_by);
    }

    const sort = PaginationUtility.getSortConfig(
      sort_by,
      sort_order,
      direction,
    );
    const searchAfter = this.getSearchAfter(parsedCursor);

    const searchBody: Record<string, any> = {
      query: coreQuery,
      sort,
      size: size + 1, // Request one extra item for cursor logic
      _source: sourceFields,
    };

    if (include_aggregations) {
      const aggregationsObject = {
        contributors: { terms: { field: 'contributor.keyword', size: 10 } },
        platforms: { terms: { field: 'metadata.platforms', size: 10 } },
        categories: {
          terms: { field: 'metadata.categories.keyword', size: 10 },
        },
        product_services: {
          terms: { field: 'metadata.product_services.keyword', size: 10 },
        },
        rule_type: { terms: { field: 'rule_type', size: 10 } },
        mitre_attack_ids: {
          nested: { path: 'metadata.mitre_attack' },
          aggs: {
            ids: {
              terms: {
                field: 'metadata.mitre_attack.mitre_id',
                size: 10,
              },
            },
          },
        },
      };
      searchBody.aggs = aggregationsObject;
    }

    if (searchAfter) {
      searchBody.search_after = searchAfter;
    }

    if (highlightConfig) {
      searchBody.highlight = highlightConfig;
    }

    this.logger.debug(
      `CursorPaginationStrategy search body: ${JSON.stringify(searchBody)}`,
    );
    return searchBody;
  }

  public formatResponse(
    response: OpenSearchResponse<RuleSource>,
    options: CursorQueryBuildOptions,
  ): SearchResponseDto {
    const { hits } = response;
    if (!hits || !hits.total) {
      this.logger.warn(
        'Invalid OpenSearch response format in CursorPaginationStrategy:',
        response,
      );
      return { status: 'error', meta: { total: 0, size: 0 }, data: [] };
    }

    const total =
      typeof hits.total === 'number' ? hits.total : (hits.total.value ?? 0);
    const items = hits.hits || [];
    const {
      size = 20,
      sort_by = SortField.RELEVANCE,
      sort_order = SortOrder.DESC,
      direction = PaginationDirection.FORWARD,
      cursor: requestCursor, // The cursor used for *this* request
    } = options;

    const hasMore = items.length > size;
    const pageItems = hasMore ? items.slice(0, size) : items;

    const orderedItems =
      direction === PaginationDirection.BACKWARD
        ? pageItems.reverse()
        : pageItems;

    let nextCursor: string | undefined;
    let prevCursor: string | undefined;

    if (direction === PaginationDirection.FORWARD) {
      if (hasMore) {
        const lastItem = orderedItems[orderedItems.length - 1];
        if (lastItem) {
          nextCursor = this.generateCursor(lastItem, sort_by, sort_order);
        }
      }
      prevCursor = requestCursor;
    } else {
      // BACKWARD pagination
      nextCursor = requestCursor;
      if (hasMore) {
        const firstItem = orderedItems[0];
        if (firstItem) {
          prevCursor = this.generateCursor(firstItem, sort_by, sort_order);
        }
      }
    }

    const data = orderedItems.map((hit: OpenSearchHit<RuleSource>) => {
      const source = hit._source ?? {};
      return {
        id: hit._id,
        title: source.title,
        description: source.description,
        ai_generated: source.ai_generated,
        status: source.status as RuleStatus,
        rule_type: source.rule_type as RuleType,
        content: source.content ?? '',
        created_by: source.created_by ?? '',
        contributor: source.contributor ?? '',
        likes: source.likes ?? 0,
        downloads: source.downloads ?? 0,
        dislikes: source.dislikes ?? 0,
        bookmarks: source.bookmarks ?? 0,
        group_id: source.group_id,
        group_name: source.group_name,
        tags: source.tags ?? [],
        metadata: source.metadata,
        test_cases: source.test_cases ?? [],
        created_at: source.created_at ?? '',
        updated_at: source.updated_at ?? '',
        published_at: source.published_at,
        version: source.version ?? 1,
        is_bookmarked: source.is_bookmarked,
        is_liked: source.is_liked,
        is_disliked: source.is_disliked,
        _score: hit._score ?? 0,
        highlight: hit.highlight,
        _id: hit._id,
      };
    });

    return {
      status: 'success',
      meta: {
        total,
        size: orderedItems.length,
        next_cursor: nextCursor,
        prev_cursor: prevCursor,
      },
      data,
    };
  }

  private parseCursor(cursor?: string): SearchCursor | undefined {
    if (!cursor) return undefined;
    try {
      return JSON.parse(
        Buffer.from(cursor, 'base64').toString(),
      ) as SearchCursor;
    } catch (error) {
      this.logger.error('Failed to parse cursor:', error);
      throw new BadRequestException('Invalid cursor format');
    }
  }

  private getSearchAfter(
    cursor: SearchCursor | undefined,
  ): (string | number | boolean)[] | undefined {
    if (!cursor) return undefined;
    return cursor.sort_values;
  }

  private validateCursor(
    cursor: string,
    expectedSortField: SortField,
  ): SearchCursor {
    try {
      const decodedCursor = this.parseCursor(cursor);
      if (!decodedCursor) {
        throw new BadRequestException(
          'Invalid cursor format (parsed as undefined)',
        );
      }

      if (
        !decodedCursor.sort_values ||
        !Array.isArray(decodedCursor.sort_values)
      ) {
        throw new BadRequestException(
          'Invalid cursor: missing or invalid sort_values',
        );
      }
      if (!decodedCursor.id) {
        throw new BadRequestException('Invalid cursor: missing id');
      }
      if (
        !decodedCursor.sort_field ||
        !Object.values(SortField).includes(decodedCursor.sort_field)
      ) {
        throw new BadRequestException('Invalid cursor: invalid sort_field');
      }
      if (
        !decodedCursor.sort_order ||
        !Object.values(SortOrder).includes(decodedCursor.sort_order)
      ) {
        throw new BadRequestException('Invalid cursor: invalid sort_order');
      }

      if (decodedCursor.sort_field !== expectedSortField) {
        this.logger.warn(
          `Cursor sort field mismatch. Expected ${expectedSortField}, got ${decodedCursor.sort_field}`,
        );
        // Not throwing error for mismatch as per original SearchService logic, but logging it.
      }

      const firstSortValue = decodedCursor.sort_values[0];
      switch (decodedCursor.sort_field) {
        case SortField.CREATED_AT:
        case SortField.UPDATED_AT:
        case SortField.PUBLISHED_AT:
        case SortField.METADATA_DATE:
        case SortField.METADATA_MODIFIED:
          if (typeof firstSortValue === 'string') {
            if (!this.queryUtilityService.isValidDateString(firstSortValue)) {
              throw new BadRequestException(
                'Invalid cursor: invalid date in sort_values[0]',
              );
            }
          } else if (firstSortValue !== null && firstSortValue !== undefined) {
            throw new BadRequestException(
              'Invalid cursor: expected string, null or undefined for date sort_values[0]',
            );
          }
          break;
        case SortField.RELEVANCE:
          if (typeof firstSortValue !== 'number' && firstSortValue !== null) {
            this.logger.warn(
              `Cursor score type mismatch: ${typeof firstSortValue}, value: ${firstSortValue}`,
            );
          }
          break;
        case SortField.LIKES:
        case SortField.DOWNLOADS:
        case SortField.DISLIKES:
          if (typeof firstSortValue !== 'number') {
            throw new BadRequestException(
              `Invalid cursor: invalid number in sort_values[0] for ${decodedCursor.sort_field}`,
            );
          }
          break;
        case SortField.TITLE:
        case SortField.CONTRIBUTOR:
        case SortField.GROUP_NAME:
        case SortField.RULE_TYPE:
        case SortField.AUTHOR:
        case SortField.MITRE_TACTIC:
        case SortField.MITRE_TECHNIQUE:
          if (
            typeof firstSortValue !== 'string' &&
            firstSortValue !== null &&
            firstSortValue !== undefined
          ) {
            throw new BadRequestException(
              `Invalid cursor: invalid string, null or undefined in sort_values[0] for ${decodedCursor.sort_field}`,
            );
          }
          break;
      }
      return decodedCursor;
    } catch (error) {
      this.logger.error('Cursor validation failed:', error);
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(
        `Invalid cursor: ${error instanceof Error ? error.message : 'Unknown validation error'}`,
      );
    }
  }

  private generateCursor(
    lastItem: OpenSearchHit<RuleSource>,
    sortBy: SortField,
    sortOrder: SortOrder,
  ): string {
    const cursorData: SearchCursor = {
      sort_values: [],
      id: lastItem._id,
      sort_field: sortBy,
      sort_order: sortOrder,
    };

    if (lastItem.sort) {
      cursorData.sort_values = lastItem.sort;
    } else {
      this.logger.warn(
        `Missing sort values on hit ${lastItem._id}, generating fallback cursor values.`,
      );
      switch (sortBy) {
        case SortField.CREATED_AT:
          cursorData.sort_values = [lastItem._source.created_at, lastItem._id];
          break;
        case SortField.UPDATED_AT:
          cursorData.sort_values = [lastItem._source.updated_at, lastItem._id];
          break;
        case SortField.TITLE:
          cursorData.sort_values = [
            lastItem._source.title?.value || '',
            lastItem._id,
          ];
          break;
        case SortField.RELEVANCE:
          cursorData.sort_values = [lastItem._score ?? 0, lastItem._id];
          break;
        case SortField.LIKES:
          cursorData.sort_values = [lastItem._source.likes ?? 0, lastItem._id];
          break;
        case SortField.DOWNLOADS:
          cursorData.sort_values = [
            lastItem._source.downloads ?? 0,
            lastItem._id,
          ];
          break;
        case SortField.DISLIKES:
          cursorData.sort_values = [
            lastItem._source.dislikes ?? 0,
            lastItem._id,
          ];
          break;
        case SortField.PUBLISHED_AT:
          cursorData.sort_values = [
            lastItem._source.published_at ?? '',
            lastItem._id,
          ];
          break;
        case SortField.CONTRIBUTOR:
          cursorData.sort_values = [
            lastItem._source.contributor ?? '',
            lastItem._id,
          ];
          break;
        case SortField.GROUP_NAME:
          cursorData.sort_values = [
            lastItem._source.group_name ?? '',
            lastItem._id,
          ];
          break;
        case SortField.RULE_TYPE:
          cursorData.sort_values = [
            lastItem._source.rule_type ?? '',
            lastItem._id,
          ];
          break;
        case SortField.AUTHOR:
          cursorData.sort_values = [
            lastItem._source.metadata?.author ?? '',
            lastItem._id,
          ];
          break;
        case SortField.METADATA_DATE:
          cursorData.sort_values = [
            lastItem._source.metadata?.date ?? '',
            lastItem._id,
          ];
          break;
        case SortField.METADATA_MODIFIED:
          cursorData.sort_values = [
            lastItem._source.metadata?.modified ?? '',
            lastItem._id,
          ];
          break;
        case SortField.MITRE_TACTIC:
          cursorData.sort_values = [
            (Array.isArray(lastItem._source.metadata?.mitre_tactics) &&
              lastItem._source.metadata?.mitre_tactics[0]) ||
              '',
            lastItem._id,
          ];
          break;
        case SortField.MITRE_TECHNIQUE:
          cursorData.sort_values = [
            (Array.isArray(lastItem._source.metadata?.mitre_techniques) &&
              lastItem._source.metadata?.mitre_techniques[0]) ||
              '',
            lastItem._id,
          ];
          break;
        default:
          cursorData.sort_values = [lastItem._id];
          break;
      }
    }
    return Buffer.from(JSON.stringify(cursorData)).toString('base64');
  }
}
