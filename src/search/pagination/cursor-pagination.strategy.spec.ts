import { Test, TestingModule } from '@nestjs/testing';
import { CursorPaginationStrategy } from './cursor-pagination.strategy';
import { QueryUtilityService } from '../query-builders/query.utility';
import { SortField } from '../models/sort-field.enum';
import { SortOrder } from '../models/sort-order.enum';
import { OpenSearchHit, RuleSource } from '../models/opensearch-types';
import { SearchCursor } from '../models/search-types';
import { RuleType, RuleStatus } from '../../rules/models/rule.model';
import { ConfigModule } from '@nestjs/config'; // For OpenSearchConfigService if QueryUtilityService needs it transitively

// Mock for QueryUtilityService
const mockQueryUtilityService = {
  isValidDateString: jest.fn().mockReturnValue(true),
  // Add other methods if they are called by the strategy
};

// Helper to access private methods for testing (if needed for generateCursor)
function getPrivateMethod<T>(
  obj: Record<string, unknown>,
  methodName: string,
): T {
  const method = obj[methodName];
  if (typeof method !== 'function') {
    throw new Error(`Method ${methodName} not found or is not a function`);
  }
  return (method as (...args: unknown[]) => unknown).bind(obj) as T;
}

describe('CursorPaginationStrategy', () => {
  let strategy: CursorPaginationStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule], // QueryUtility might depend on OpenSearchConfigService which might need ConfigModule
      providers: [
        CursorPaginationStrategy,
        {
          provide: QueryUtilityService,
          useValue: mockQueryUtilityService,
        },
      ],
    }).compile();

    strategy = module.get<CursorPaginationStrategy>(CursorPaginationStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  // TODO: Add tests for buildSearchBody
  // TODO: Add tests for formatResponse
  // TODO: Add tests for parseCursor, validateCursor, getSearchAfter if they remain public or are critical internal helpers

  describe('generateCursor (private method test via public interface if possible, or helper)', () => {
    // Accessing private method for testing. If this is not preferred, test via public methods that use it.
    let generateCursorMethod: (
      item: OpenSearchHit<RuleSource>,
      sortField: SortField,
      sortOrder: SortOrder,
    ) => string;

    beforeEach(() => {
      generateCursorMethod = getPrivateMethod<
        (
          item: OpenSearchHit<RuleSource>,
          sortField: SortField,
          sortOrder: SortOrder,
        ) => string
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      >(strategy as any, 'generateCursor');
    });

    it('should generate cursor for CONTRIBUTOR field', () => {
      const mockItem: OpenSearchHit<RuleSource> = {
        _id: 'test-id',
        _index: 'mock-index',
        _score: 1,
        _source: {
          contributor: 'test-contributor',
          owner_id: 'mock-owner',
          rule_type: RuleType.SIGMA,
          status: RuleStatus.DRAFT,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          version: 1,
          content: 'Test content',
          created_by: 'mock-user',
          severity: 'MEDIUM',
        },
        sort: ['test-contributor', 'test-id'], // Ensure sort values are present as OpenSearch would provide
      };

      const cursor: string = generateCursorMethod(
        mockItem,
        SortField.CONTRIBUTOR,
        SortOrder.ASC,
      );
      const decodedCursor = JSON.parse(
        Buffer.from(cursor, 'base64').toString('utf8'),
      ) as SearchCursor;

      expect(decodedCursor.id).toEqual('test-id');
      expect(decodedCursor.sort_field).toEqual(SortField.CONTRIBUTOR);
      expect(decodedCursor.sort_order).toEqual(SortOrder.ASC);
      expect(decodedCursor.sort_values).toEqual([
        'test-contributor',
        'test-id',
      ]);
    });

    it('should generate cursor for GROUP_NAME field', () => {
      const mockItem: OpenSearchHit<RuleSource> = {
        _id: 'test-id',
        _index: 'mock-index',
        _score: 1,
        _source: {
          group_name: 'test-group',
          owner_id: 'mock-owner',
          rule_type: RuleType.SIGMA,
          status: RuleStatus.DRAFT,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          version: 1,
          content: 'Test content',
          created_by: 'mock-user',
          contributor: 'mock-contributor',
          severity: 'MEDIUM',
        },
        sort: ['test-group', 'test-id'],
      };

      const cursor: string = generateCursorMethod(
        mockItem,
        SortField.GROUP_NAME,
        SortOrder.DESC,
      );
      const decodedCursor = JSON.parse(
        Buffer.from(cursor, 'base64').toString('utf8'),
      ) as SearchCursor;

      expect(decodedCursor.id).toEqual('test-id');
      expect(decodedCursor.sort_field).toEqual(SortField.GROUP_NAME);
      expect(decodedCursor.sort_order).toEqual(SortOrder.DESC);
      expect(decodedCursor.sort_values).toEqual(['test-group', 'test-id']);
    });

    it('should generate cursor for RULE_TYPE field', () => {
      const mockItem: OpenSearchHit<RuleSource> = {
        _id: 'test-id',
        _index: 'mock-index',
        _score: 1,
        _source: {
          rule_type: RuleType.SIGMA,
          owner_id: 'mock-owner',
          status: RuleStatus.DRAFT,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          version: 1,
          content: 'Test content',
          created_by: 'mock-user',
          contributor: 'mock-contributor',
          severity: 'MEDIUM',
        },
        sort: [RuleType.SIGMA, 'test-id'],
      };

      const cursor: string = generateCursorMethod(
        mockItem,
        SortField.RULE_TYPE,
        SortOrder.ASC,
      );
      const decodedCursor = JSON.parse(
        Buffer.from(cursor, 'base64').toString('utf8'),
      ) as SearchCursor;

      expect(decodedCursor.id).toEqual('test-id');
      expect(decodedCursor.sort_field).toEqual(SortField.RULE_TYPE);
      expect(decodedCursor.sort_order).toEqual(SortOrder.ASC);
      expect(decodedCursor.sort_values).toEqual([RuleType.SIGMA, 'test-id']);
    });
  });
});
