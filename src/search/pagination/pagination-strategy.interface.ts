import {
  OpenSearchQueryClause,
  OpenSearchResponse,
  OpenSearchSortClause,
} from '../models/opensearch-types';
import { RuleSource } from '../models/opensearch-types';
import { SearchOptionsDto } from '../dtos/search-options.dto';
import {
  PageSearchOptionsDto,
  PagedSearchResponseDto,
} from '../dtos/page-search-options.dto';
import { SearchResponseDto } from '../dtos/search-response.dto';
import { SortField } from '../models/sort-field.enum';
import { SortOrder } from '../models/sort-order.enum';
import { PaginationDirection } from '../models/pagination-direction.enum';

/**
 * Common options for building a query, shared across pagination strategies.
 */
export interface BaseQueryBuilderOptions {
  highlightConfig?: Record<string, any>;
  sourceFields?:
    | boolean
    | string[]
    | { includes?: string[]; excludes?: string[] };
  sortConfig?: OpenSearchSortClause[];
}

/**
 * Specific options for cursor-based pagination query building.
 * Extends SearchOptionsDto which includes cursor, direction, size, sort_by, sort_order.
 */
export interface CursorQueryBuildOptions
  extends SearchOptionsDto,
    BaseQueryBuilderOptions {}

/**
 * Specific options for offset-based pagination query building.
 * Extends PageSearchOptionsDto which includes page, size, sort_by, sort_order.
 */
export interface OffsetQueryBuildOptions
  extends PageSearchOptionsDto,
    BaseQueryBuilderOptions {}

/**
 * Interface for a pagination strategy.
 * @template TResponseDto - The DTO type for the formatted search response.
 * @template TOptionsDto - The DTO type for options specific to this pagination strategy (e.g., SearchOptionsDto, PageSearchOptionsDto).
 * @template TQueryBuildParams - Parameters specific to building the search body for this strategy.
 */
export interface PaginationStrategy<
  TResponseDto,
  TOptionsDto extends BaseQueryBuilderOptions,
> {
  /**
   * Builds the OpenSearch search body specific to the pagination strategy.
   * @param coreQuery - The main query clause (e.g., from BasicSearchQueryBuilder or AdvancedSearchQueryBuilder).
   * @param options - Pagination and query construction options.
   * @returns The OpenSearch search body.
   */
  buildSearchBody(
    coreQuery: OpenSearchQueryClause | undefined,
    options: TOptionsDto,
  ): Record<string, any>;

  /**
   * Formats the raw OpenSearch response into the strategy-specific DTO.
   * @param openSearchResponse - The raw response from OpenSearch.
   * @param options - Pagination options used for the request.
   * @returns The formatted search response DTO.
   */
  formatResponse(
    openSearchResponse: OpenSearchResponse<RuleSource>,
    options: TOptionsDto, // These are the original DTOs like SearchOptionsDto or PageSearchOptionsDto
  ): TResponseDto;
}

/**
 * Utility for sort configuration.
 * Can be expanded or moved to a dedicated service later.
 */
export class PaginationUtility {
  public static getSortConfig(
    sortBy: SortField,
    sortOrder: SortOrder,
    direction: PaginationDirection,
  ): OpenSearchSortClause[] {
    const order = sortOrder.toLowerCase() as 'asc' | 'desc';
    const reverseOrder = order === 'asc' ? 'desc' : 'asc';

    // Adjust order for backward pagination
    const actualOrder =
      direction === PaginationDirection.BACKWARD ? reverseOrder : order;

    switch (sortBy) {
      case SortField.CREATED_AT:
        return [{ created_at: actualOrder }, { _id: actualOrder }];
      case SortField.UPDATED_AT:
        return [{ updated_at: actualOrder }, { _id: actualOrder }];
      case SortField.TITLE:
        return [{ 'title.value.keyword': actualOrder }, { _id: actualOrder }];
      case SortField.RELEVANCE:
        return [
          { _score: 'desc' },
          {
            _id:
              direction === PaginationDirection.BACKWARD ? reverseOrder : order,
          },
        ];
      case SortField.LIKES:
        return [{ likes: actualOrder }, { _id: actualOrder }];
      case SortField.DOWNLOADS:
        return [{ downloads: actualOrder }, { _id: actualOrder }];
      case SortField.DISLIKES:
        return [{ dislikes: actualOrder }, { _id: actualOrder }];
      case SortField.PUBLISHED_AT:
        return [{ published_at: actualOrder }, { _id: actualOrder }];
      case SortField.CONTRIBUTOR:
        return [{ 'contributor.keyword': actualOrder }, { _id: actualOrder }];
      case SortField.GROUP_NAME:
        return [{ 'group_name.keyword': actualOrder }, { _id: actualOrder }];
      case SortField.RULE_TYPE:
        return [{ rule_type: actualOrder }, { _id: actualOrder }];
      case SortField.AUTHOR:
        return [
          { 'metadata.author.keyword': actualOrder },
          { _id: actualOrder },
        ];
      case SortField.METADATA_DATE:
        return [{ 'metadata.date': actualOrder }, { _id: actualOrder }];
      case SortField.METADATA_MODIFIED:
        return [{ 'metadata.modified': actualOrder }, { _id: actualOrder }];
      case SortField.MITRE_TACTIC:
        return [
          { 'metadata.mitre_tactics': actualOrder },
          { _id: actualOrder },
        ];
      case SortField.MITRE_TECHNIQUE:
        return [
          { 'metadata.mitre_techniques': actualOrder },
          { _id: actualOrder },
        ];
      default:
        // Fallback to sorting by ID if an unknown sort field is provided,
        // though ideally this should be validated earlier.
        return [{ _id: actualOrder }];
    }
  }
}

// Re-exporting types that might be commonly used with pagination strategies
export {
  SearchOptionsDto,
  PageSearchOptionsDto,
  SearchResponseDto,
  PagedSearchResponseDto,
};
export { SortField, SortOrder, PaginationDirection };
export {
  RuleSource,
  OpenSearchResponse,
  OpenSearchQueryClause,
  OpenSearchHit,
} from '../models/opensearch-types';
export { SearchCursor } from '../models/search-types';
