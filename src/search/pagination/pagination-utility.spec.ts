import { PaginationUtility } from './pagination-strategy.interface';
import { SortField } from '../models/sort-field.enum';
import { SortOrder } from '../models/sort-order.enum';
import { PaginationDirection } from '../models/pagination-direction.enum';
import { OpenSearchSortClause } from '../models/opensearch-types';

describe('PaginationUtility', () => {
  describe('getSortConfig', () => {
    it('should generate correct sort configuration for CONTRIBUTOR field', () => {
      const ascConfig: OpenSearchSortClause[] = PaginationUtility.getSortConfig(
        SortField.CONTRIBUTOR,
        SortOrder.ASC,
        PaginationDirection.FORWARD,
      );
      expect(ascConfig).toEqual([
        { 'contributor.keyword': 'asc' },
        { _id: 'asc' },
      ]);

      const descConfig: OpenSearchSortClause[] =
        PaginationUtility.getSortConfig(
          SortField.CONTRIBUTOR,
          SortOrder.DESC,
          PaginationDirection.FORWARD,
        );
      expect(descConfig).toEqual([
        { 'contributor.keyword': 'desc' },
        { _id: 'desc' },
      ]);
    });

    it('should generate correct sort configuration for GROUP_NAME field', () => {
      const ascConfig: OpenSearchSortClause[] = PaginationUtility.getSortConfig(
        SortField.GROUP_NAME,
        SortOrder.ASC,
        PaginationDirection.FORWARD,
      );
      expect(ascConfig).toEqual([
        { 'group_name.keyword': 'asc' },
        { _id: 'asc' },
      ]);

      const descConfig: OpenSearchSortClause[] =
        PaginationUtility.getSortConfig(
          SortField.GROUP_NAME,
          SortOrder.DESC,
          PaginationDirection.FORWARD,
        );
      expect(descConfig).toEqual([
        { 'group_name.keyword': 'desc' },
        { _id: 'desc' },
      ]);
    });

    it('should generate correct sort configuration for RULE_TYPE field', () => {
      const ascConfig: OpenSearchSortClause[] = PaginationUtility.getSortConfig(
        SortField.RULE_TYPE,
        SortOrder.ASC,
        PaginationDirection.FORWARD,
      );
      expect(ascConfig).toEqual([{ rule_type: 'asc' }, { _id: 'asc' }]);

      const descConfig: OpenSearchSortClause[] =
        PaginationUtility.getSortConfig(
          SortField.RULE_TYPE,
          SortOrder.DESC,
          PaginationDirection.FORWARD,
        );
      expect(descConfig).toEqual([{ rule_type: 'desc' }, { _id: 'desc' }]);
    });

    // Consider adding tests for other SortFields and PaginationDirection.BACKWARD
  });
});
