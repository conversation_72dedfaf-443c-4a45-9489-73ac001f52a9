export interface OpenSearchExplanation {
  value: number;
  description: string;
  details: OpenSearchExplanation[];
}

export interface OpenSearchHit<TSource> {
  _id: string;
  _index: string;
  _score: number | null;
  _source: TSource;
  highlight?: Record<string, string[]>;
  sort?: (string | number | boolean)[];
  explanation?: OpenSearchExplanation;
}

export interface OpenSearchHits<TSource> {
  total: { value: number; relation: string } | number;
  max_score: number | null;
  hits: OpenSearchHit<TSource>[];
}

export interface OpenSearchSuggestOption<TSource = RuleSource> {
  text: string;
  _index: string;
  _id: string;
  _score: number;
  _source: TSource;
  contexts?: Record<string, (string | { id: string; category: string })[]>;
}

export interface OpenSearchSuggestResult<TSource = RuleSource> {
  text: string;
  offset: number;
  length: number;
  options: OpenSearchSuggestOption<TSource>[];
}

export interface OpenSearchSuggestResponse<TSource = RuleSource> {
  [suggesterName: string]: OpenSearchSuggestResult<TSource>[];
}

export interface OpenSearchResponse<TSource> {
  took: number;
  timed_out: boolean;
  _shards: {
    total: number;
    successful: number;
    skipped: number;
    failed: number;
  };
  hits: OpenSearchHits<TSource>;
  suggest?: OpenSearchSuggestResponse<TSource>;
  aggregations?: Record<string, OpenSearchSingleAggregationResult>;
}

export interface OpenSearchGetResponse<TSource> {
  _index: string;
  _id: string;
  _version?: number;
  _seq_no?: number;
  _primary_term?: number;
  found: boolean;
  _source: TSource;
}

// Import the actual RuleMetadata from the rules module to ensure compatibility
import { RuleMetadata } from '../../rules/models/rule-metadata.model';
export { RuleMetadata };

export interface RuleSource {
  id?: string;
  owner_id: string;
  title?: {
    value: string;
    suggest?: string;
    shingles?: string;
    edge_ngram?: string;
    keyword?: string;
  };
  description?: string;
  ai_generated?: {
    description: boolean;
    title?: boolean;
    content?: boolean;
    tags?: boolean;
  };
  content?: string;
  rule_type: string;
  status: string;
  stage?: string;
  severity?: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
  created_by: string;
  contributor: string;
  group_id?: string;
  group_name?: string;
  likes?: number;
  downloads?: number;
  dislikes?: number;
  bookmarks?: number;
  metadata?: RuleMetadata;
  tags?: string[];
  test_cases?: TestCase[];
  version?: number;
  is_bookmarked?: boolean;
  is_liked?: boolean;
  is_disliked?: boolean;
  all_text?: string;
}

export interface TestCase {
  input: any;
  expected_output: any;
  description?: string;
}

export interface HighlightQuery {
  fields: Record<string, HighlightFieldConfig>;
  pre_tags?: string[];
  post_tags?: string[];
}

export interface HighlightFieldConfig {
  fragment_size?: number;
  number_of_fragments?: number;
}

export interface OpenSearchSearchRequestBody {
  query?: OpenSearchQueryClause;
  sort?: OpenSearchSortClause[];
  size?: number;
  search_after?: (string | number | boolean)[];
  from?: number;
  _source?: boolean | string[] | { includes?: string[]; excludes?: string[] };
  highlight?: HighlightQuery;
  suggest?: Record<
    string,
    {
      prefix: string;
      completion: {
        field: string;
        size: number;
        contexts?: Record<
          string,
          (string | { id: string; category: string })[]
        >;
      };
    }
  >;
  aggs?: Record<string, OpenSearchAggregationDefinition>;
  explain?: boolean;
  min_score?: number;
}

// Re-export the query clause interface that will be defined in the service file
export interface OpenSearchQueryClause {
  bool?: OpenSearchBoolQuery;
  match_all?: Record<string, unknown>;
  match_none?: Record<string, unknown>;
  function_score?: OpenSearchFunctionScoreQuery;
  more_like_this?: OpenSearchMoreLikeThisQuery;
  multi_match?: OpenSearchMultiMatchQuery;
  match?: Record<string, OpenSearchMatchQuery | string | undefined>;
  term?: Record<string, OpenSearchTermQuery>;
  terms?: Record<string, string[] | number[] | undefined>;
  range?: Record<string, OpenSearchRangeQuery>;
  nested?: OpenSearchNestedQuery;
  prefix?: Record<string, OpenSearchPrefixQuery>;
  match_phrase?: Record<string, OpenSearchMatchPhraseQuery>;
}

export interface OpenSearchBoolQuery {
  must?: OpenSearchQueryClause[];
  should?: OpenSearchQueryClause[];
  must_not?: OpenSearchQueryClause[];
  filter?: OpenSearchQueryClause[];
  minimum_should_match?: number | string;
}

export interface OpenSearchFunctionScoreQuery {
  query: OpenSearchQueryClause;
  functions: OpenSearchFunction[];
  score_mode?: string;
  boost_mode?: string;
}

export interface OpenSearchFunction {
  filter?: OpenSearchQueryClause;
  weight?: number;
  gauss?: Record<string, OpenSearchGaussFunction>;
}

export interface OpenSearchGaussFunction {
  origin: string;
  scale: string;
  decay: number;
}

export interface OpenSearchMoreLikeThisQuery {
  fields: string[];
  like: (OpenSearchLikeDocument | OpenSearchLikeText)[];
  unlike?: (OpenSearchLikeDocument | OpenSearchLikeText)[];
  min_term_freq?: number;
  max_query_terms?: number;
  minimum_should_match?: string;
  min_doc_freq?: number;
  max_doc_freq?: number;
  min_word_length?: number;
  max_word_length?: number;
  stop_words?: string[];
  boost_terms?: number;
  analyzer?: string;
}

export interface OpenSearchLikeDocument {
  _index: string;
  _id: string;
}

export interface OpenSearchLikeText {
  doc: Record<string, unknown>;
}

export interface OpenSearchMultiMatchQuery {
  query: string;
  fields: string[];
  type?: string;
  analyzer?: string;
  operator?: string;
  minimum_should_match?: string;
  tie_breaker?: number;
  boost?: number;
  slop?: number;
}

export interface OpenSearchMatchQuery {
  query?: string;
  analyzer?: string;
  operator?: string;
  boost?: number;
  [key: string]: any; // Allow additional properties like _index
}

export interface OpenSearchTermQuery {
  value: string | number | boolean;
  boost?: number;
}

export interface OpenSearchRangeQuery {
  gte?: string | number;
  lte?: string | number;
  gt?: string | number;
  lt?: string | number;
}

export interface OpenSearchNestedQuery {
  path: string;
  query: OpenSearchQueryClause;
  boost?: number;
}

export interface OpenSearchPrefixQuery {
  value: string;
  boost?: number;
}

export interface OpenSearchMatchPhraseQuery {
  query: string;
  analyzer?: string;
  boost?: number;
  slop?: number;
}

export type OpenSearchSortClause =
  | Record<string, 'asc' | 'desc'>
  | Record<
      string,
      {
        type?: string;
        order: 'asc' | 'desc';
        script?: {
          lang: string;
          source: string;
          params?: Record<string, any>;
        };
      }
    >
  | {
      _script: {
        type: string;
        order: string;
        script: {
          lang: string;
          source: string;
          params?: Record<string, any>;
        };
      };
    };

// Define a base for aggregation definitions
export interface OpenSearchAggregationDefinition {
  terms?: OpenSearchTermsAggregation;
  nested?: OpenSearchNestedAggregation;
  aggs?: Record<string, OpenSearchAggregationDefinition>; // For sub-aggregations within a nested aggregation
  // Add other aggregation types as needed (e.g., date_histogram, sum, avg, etc.)
}

export interface OpenSearchTermsAggregation {
  field: string;
  size?: number;
  order?: Record<string, 'asc' | 'desc'> | Record<string, 'asc' | 'desc'>[];
  missing?: string | number;
  // Add other terms aggregation options as needed
}

export interface OpenSearchNestedAggregation {
  path: string;
  // Typically used with a sub-aggregation, so 'aggs' would be defined in OpenSearchAggregationDefinition
}

// --- AGGREGATION RESULT TYPES --- START ---
export interface OpenSearchAggregationBucket {
  key: string | number;
  doc_count: number;
  [subAggName: string]: any; // For potential sub-aggregations within buckets
}

export interface OpenSearchTermsAggregationResult {
  doc_count_error_upper_bound?: number;
  sum_other_doc_count?: number;
  buckets: OpenSearchAggregationBucket[];
}

export interface OpenSearchNestedAggregationResult {
  doc_count: number;
  [subAggName: string]: OpenSearchSingleAggregationResult | number; // Allows accessing sub-aggregations by name
}

// Add other specific aggregation result types as needed, e.g., for stats, date_histogram, etc.
// Example for a generic value metric like sum, avg, max, min, count
export interface OpenSearchValueMetricAggregationResult {
  value: number;
  value_as_string?: string;
}

export type OpenSearchSingleAggregationResult =
  | OpenSearchTermsAggregationResult
  | OpenSearchNestedAggregationResult
  | OpenSearchValueMetricAggregationResult
  | { [key: string]: any }; // Fallback for any other or custom aggregation types
// --- AGGREGATION RESULT TYPES --- END ---
