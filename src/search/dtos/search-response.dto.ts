import { ApiProperty } from '@nestjs/swagger';
import { Rule } from '../../rules/models/rule.model';
import { SearchAggregationsDto } from './search-aggregations.dto';

/**
 * DTO for search result metadata
 */
export class SearchMetadataDto {
  @ApiProperty({
    description: 'Total number of matching results',
    example: 37,
  })
  total: number;

  @ApiProperty({
    description: 'Next page cursor',
    example:
      'eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ==',
    nullable: true,
  })
  next_cursor?: string;

  @ApiProperty({
    description: 'Previous page cursor',
    example:
      'eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xM1QwOTowMTowMFoiLCJpZCI6InJ1bGUxMjIifQ==',
    nullable: true,
  })
  prev_cursor?: string;

  @ApiProperty({
    description: 'Number of results in this response',
    example: 20,
  })
  size: number;

  @ApiProperty({
    description: 'Current page number (1-based)',
    example: 1,
    nullable: true,
  })
  current_page?: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 4,
    nullable: true,
  })
  total_pages?: number;

  @ApiProperty({
    description: 'Number of results per page',
    example: 20,
    nullable: true,
  })
  page_size?: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
    nullable: true,
  })
  has_next_page?: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
    nullable: true,
  })
  has_prev_page?: boolean;
}

/**
 * DTO for search result highlights
 */
export class SearchHighlightDto {
  @ApiProperty({
    description: 'Title highlights',
    type: [String],
    example: ['<strong>Lateral</strong> Movement Detection'],
  })
  title?: string[];

  @ApiProperty({
    description: 'Description highlights',
    type: [String],
    example: ['Detects <strong>lateral</strong> movement using WMI'],
  })
  description?: string[];

  @ApiProperty({
    description: 'Content highlights',
    type: [String],
    example: ['function detectLateralMovement() { ... }'],
  })
  content?: string[];
}

/**
 * DTO for search result item
 */
export class SearchResultItemDto extends Rule {
  @ApiProperty({
    description: 'Search relevance score',
    example: 0.92,
  })
  _score: number;

  @ApiProperty({
    description: 'Search result highlights',
    type: SearchHighlightDto,
  })
  highlight?: SearchHighlightDto;
}

/**
 * DTO for search response
 */
export class SearchResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'Search metadata',
    type: SearchMetadataDto,
  })
  meta: SearchMetadataDto;

  @ApiProperty({
    description: 'Search results',
    type: [SearchResultItemDto],
  })
  data: SearchResultItemDto[];

  @ApiProperty({
    description: 'Search aggregations',
    type: SearchAggregationsDto,
    required: false,
  })
  aggregations?: SearchAggregationsDto;
}

/**
 * DTO for search suggestions response
 */
export class SearchSuggestionsResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'Search suggestions',
    type: [String],
    example: [
      'lateral movement detection',
      'lateral movement via powershell',
      'lateral movement techniques',
    ],
  })
  data: string[];
}

/**
 * DTO for search metadata response
 */
export class SearchMetadataResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'Search metadata',
    example: {
      searchable_fields: ['title', 'description', 'content'],
      filters: {
        rule_type: ['detection', 'hunting', 'compliance'],
        stage: ['development', 'testing', 'production'],
        status: ['draft', 'active', 'deprecated', 'in_review'],
      },
      sort_options: ['relevance', 'created_at', 'updated_at', 'title'],
    },
  })
  data: {
    searchable_fields: string[];
    filters: Record<string, string[]>;
    sort_options: string[];
  };
}
