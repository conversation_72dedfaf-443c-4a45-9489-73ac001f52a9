import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  Min,
  Max,
  IsInt,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SortField } from '../models/sort-field.enum';
import { SortOrder } from '../models/sort-order.enum';
import { PaginationDirection } from '../models/pagination-direction.enum';
import { PaginationMode } from '../models/pagination-mode.enum';

/**
 * DTO for cursor-based pagination
 */
export class CursorPaginationDto {
  @ApiPropertyOptional({
    description: 'Cursor for pagination',
    example:
      'eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ==',
  })
  @IsOptional()
  @IsString()
  cursor?: string;

  @ApiPropertyOptional({
    description: 'Direction of pagination',
    enum: PaginationDirection,
    default: PaginationDirection.FORWARD,
    example: PaginationDirection.FORWARD,
  })
  @IsOptional()
  @IsEnum(PaginationDirection)
  direction?: PaginationDirection = PaginationDirection.FORWARD;

  @ApiPropertyOptional({
    description: 'Pagination mode (cursor or offset)',
    enum: PaginationMode,
    default: PaginationMode.OFFSET,
    example: PaginationMode.OFFSET,
  })
  @IsOptional()
  @IsEnum(PaginationMode)
  pagination_mode?: PaginationMode = PaginationMode.OFFSET;

  @ApiPropertyOptional({
    description: 'Number of results to return (1-100)',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Size must be an integer' })
  @Min(1, { message: 'Size must be at least 1' })
  @Max(100, { message: 'Size cannot exceed 100' })
  size?: number = 20;

  @ApiPropertyOptional({
    description: 'Page number (1-based, for direct page navigation)',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number;
}

/**
 * DTO for search options
 */
export class SearchOptionsDto extends CursorPaginationDto {
  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: SortField,
    default: SortField.RELEVANCE,
    example: SortField.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(SortField)
  sort_by?: SortField = SortField.RELEVANCE;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    default: SortOrder.DESC,
    example: SortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sort_order?: SortOrder = SortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Comma-separated list of fields to include in response',
    example: 'title,description,rule_type',
  })
  @IsOptional()
  @IsString()
  include_fields?: string;

  @ApiPropertyOptional({
    description: 'Whether to include aggregations in the response',
    type: Boolean,
    default: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  include_aggregations?: boolean = false;
}
