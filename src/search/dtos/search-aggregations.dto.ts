import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for a single aggregation bucket
 */
export class AggregationBucketDto {
  @ApiProperty({ description: 'The value of the bucket', example: 'Windows' })
  key: string | number;

  @ApiProperty({
    description: 'Number of documents in this bucket',
    example: 42,
  })
  doc_count: number;
}

/**
 * DTO for search aggregations
 */
export class SearchAggregationsDto {
  @ApiProperty({
    type: [AggregationBucketDto],
    description: 'Top contributors with counts',
    required: false,
  })
  contributors?: AggregationBucketDto[];

  @ApiProperty({
    type: [AggregationBucketDto],
    description: 'Top platforms with counts',
    required: false,
  })
  platforms?: AggregationBucketDto[];

  @ApiProperty({
    type: [AggregationBucketDto],
    description: 'Top categories with counts',
    required: false,
  })
  categories?: AggregationBucketDto[];

  @ApiProperty({
    type: [AggregationBucketDto],
    description: 'Top product_services with counts',
    required: false,
  })
  product_services?: AggregationBucketDto[];

  @ApiProperty({
    type: [AggregationBucketDto],
    description: 'Top rule_type with counts',
    required: false,
  })
  rule_type?: AggregationBucketDto[];

  @ApiProperty({
    type: [AggregationBucketDto],
    description: 'Top metadata.mitre_attack.mitre_id with counts',
    required: false,
  })
  mitre_attack_ids?: AggregationBucketDto[];
}
