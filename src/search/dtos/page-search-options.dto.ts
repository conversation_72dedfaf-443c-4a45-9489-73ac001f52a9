import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, Min, IsInt, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { SearchOptionsDto } from './search-options.dto';
import { PaginationMode } from '../models/pagination-mode.enum';
import { SearchAggregationsDto } from './search-aggregations.dto';

/**
 * DTO for page-based search options
 * Extends the standard SearchOptionsDto with page-specific options
 */
export class PageSearchOptionsDto extends SearchOptionsDto {
  @ApiPropertyOptional({
    description: 'Pagination mode (cursor or offset)',
    enum: PaginationMode,
    default: PaginationMode.OFFSET,
    example: PaginationMode.OFFSET,
  })
  @IsOptional()
  @IsEnum(PaginationMode)
  pagination_mode?: PaginationMode = PaginationMode.OFFSET;

  @ApiPropertyOptional({
    description: 'Page number (1-based). Used only for offset pagination.',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({
    description:
      'Cursor for pagination (base64 encoded string). Used only for cursor pagination.',
    example:
      'eyJzb3J0X3ZhbHVlcyI6WzE2NzgwOTYwMDAwMDAsIjQyIl0sImlkIjoiNDIiLCJzb3J0X2ZpZWxkIjoiY3JlYXRlZF9hdCIsInNvcnRfb3JkZXIiOiJkZXNjIn0=',
  })
  @IsOptional()
  @IsString()
  cursor?: string;
}

/**
 * DTO for page-based search options
 * Extends the standard SearchOptionsDto with page-specific options
 */
export class PageCustomSearchOptionsDto extends PageSearchOptionsDto {
  @ApiPropertyOptional({
    description: 'Page number (1-based). Used only for offset pagination.',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  query?: number = 1;

  @ApiPropertyOptional({
    description:
      'Cursor for pagination (base64 encoded string). Used only for cursor pagination.',
    example:
      'eyJzb3J0X3ZhbHVlcyI6WzE2NzgwOTYwMDAwMDAsIjQyIl0sImlkIjoiNDIiLCJzb3J0X2ZpZWxkIjoiY3JlYXRlZF9hdCIsInNvcnRfb3JkZXIiOiJkZXNjIn0=',
  })
  @IsOptional()
  @IsString()
  cursor?: string;
}

/**
 * Response DTO for paged search results
 */
export class PagedSearchResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'Response metadata',
    example: {
      total: 100,
      size: 20,
      current_page: 3,
      total_pages: 5,
      page_size: 20,
      has_next_page: true,
      has_prev_page: true,
      next_cursor:
        'eyJzb3J0X3ZhbHVlcyI6WzEuNSwicnVsZTEyMyJdLCJpZCI6InJ1bGUxMjMifQ==',
      prev_cursor:
        'eyJzb3J0X3ZhbHVlcyI6WzIuNSwicnVsZTEyMyJdLCJpZCI6InJ1bGUxMjMifQ==',
    },
  })
  meta: {
    total: number;
    size: number;
    current_page?: number;
    total_pages?: number;
    page_size?: number;
    has_next_page: boolean;
    has_prev_page: boolean;
    next_cursor?: string;
    prev_cursor?: string;
  };

  @ApiProperty({
    description: 'Search results',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        title: { type: 'string' },
        description: { type: 'string' },
        // Other rule properties
      },
    },
  })
  data: any[];

  @ApiProperty({
    description: 'Search aggregations',
    type: SearchAggregationsDto,
    required: false,
  })
  aggregations?: SearchAggregationsDto;
}
