import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional, <PERSON>, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { RuleType, RuleTitleObject } from '../../rules/models/rule.model';

/**
 * DTO for similar rules query
 */
export class SimilarRulesQueryDto {
  @ApiPropertyOptional({
    description: 'Minimum similarity score (0.0-1.0)',
    minimum: 0,
    maximum: 1,
    default: 0.7,
    example: 0.6,
  })
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  @IsNumber()
  @Min(0)
  @Max(1)
  min_similarity?: number = 0.7;

  @ApiPropertyOptional({
    description: 'Number of similar rules to return',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 5,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  @Max(100)
  size?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter similar rules by group ID',
    example: 'windows',
  })
  @IsOptional()
  @IsString()
  owner_id?: string;
}

/**
 * DTO for similar rule result
 */
export class SimilarRuleDto {
  @ApiProperty({
    description: 'Rule ID',
    example: 'rule-123',
  })
  id: string;

  @ApiProperty({
    description: 'Rule title',
    example: { value: 'Detect Suspicious Process Creation' },
    type: () => RuleTitleObject,
  })
  title: RuleTitleObject | undefined;

  @ApiProperty({
    description: 'Rule description',
    example: 'Detects suspicious process creation patterns',
  })
  description: string;

  @ApiProperty({
    description: 'Rule type',
    enum: RuleType,
    example: RuleType.SIGMA,
  })
  rule_type: RuleType;

  @ApiProperty({
    description: 'Rule severity',
    example: 'HIGH',
  })
  severity: string;

  @ApiProperty({
    description: 'Raw similarity score',
    example: 0.92,
  })
  _score: number;

  @ApiProperty({
    description: 'Normalized similarity percentage (0-100)',
    example: 85,
  })
  similarity_percentage: number;
}

/**
 * DTO for similar rules response
 */
export class SimilarRulesResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'Similar rules metadata',
    example: {
      reference_rule: 'rule-123',
      total: 5,
    },
  })
  meta: {
    reference_rule: string;
    total: number;
  };

  @ApiProperty({
    description: 'Similar rules',
    type: [SimilarRuleDto],
  })
  data: SimilarRuleDto[];
}
