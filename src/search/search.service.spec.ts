/* eslint-disable @typescript-eslint/unbound-method */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { Test, TestingModule } from '@nestjs/testing';
import { SearchService } from './search.service';
import { RuleSearchRepository } from '../opensearch/repositories/rule.repository';
import { OpenSearchService } from '../opensearch/opensearch.service';
import { OpenSearchConfigService } from '../opensearch/config/opensearch.config';
import { ConfigModule } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import { QueryUtilityService } from './query-builders/query.utility';
import { BasicSearchQueryBuilder } from './query-builders/basic-search.query-builder';
import { AdvancedSearchQueryBuilder } from './query-builders/advanced-search.query-builder';
import { SimilarRulesQueryBuilder } from './query-builders/similar-rules.query-builder';
import { SuggestQueryBuilder } from './query-builders/suggest.query-builder';
import {
  OpenSearchQueryClause,
  OpenSearchSearchRequestBody,
  OpenSearchSortClause,
  RuleSource,
  OpenSearchResponse,
  // OpenSearchHit, // Removed OpenSearchHit
} from './models/opensearch-types';
import { RuleType, RuleStatus } from '../rules/models/rule.model';
import { CursorPaginationStrategy } from './pagination/cursor-pagination.strategy';
import { OffsetPaginationStrategy } from './pagination/offset-pagination.strategy';
import { SortOrder } from './models/sort-order.enum'; // Added import for SortOrder

// Import the actual formatter classes for type hinting and DI tokens
import {
  SearchResponseFormatter,
  PagedSearchResponseFormatter,
  SimilarRulesResponseFormatter,
  SuggestionsResponseFormatter,
} from './response-formatters';
import { SearchOptionsDto } from './dtos/search-options.dto';
import { PageSearchOptionsDto } from './dtos/page-search-options.dto';
import { SimilarRulesQueryDto } from './dtos/similar-rules.dto';
import { PaginationMode } from './models/pagination-mode.enum';
import { AdvancedSearchQueryDto } from './dtos/search-query.dto';

// Mocks for new services
const mockSearchResponseFormatter = {
  format: jest.fn(),
};
const mockPagedSearchResponseFormatter = {
  formatOffsetResponse: jest.fn(),
  formatCursorResponse: jest.fn(),
};
const mockSimilarRulesResponseFormatter = {
  format: jest.fn(),
};
const mockSuggestionsResponseFormatter = {
  format: jest.fn(),
};

// Refined mockQueryUtilityService
const mockQueryUtilityService = {
  isMitreTechniqueIdPattern: jest.fn(),
  extractPotentialIdentifiers: jest.fn().mockReturnValue([]),
  parseFieldQuery: jest.fn().mockReturnValue(null),
  preprocessQuery: jest.fn((query: string): string => query), // Added type for query
  buildFilters: jest.fn(
    (filters: Record<string, unknown>): OpenSearchQueryClause[] => {
      const osFilters: OpenSearchQueryClause[] = [];
      const dateFieldsMap: Record<string, string> = {
        created_after: 'created_at',
        created_before: 'created_at',
        published_after: 'published_at',
        published_before: 'published_at',
        updated_after: 'updated_at',
        updated_before: 'updated_at',
      };

      for (const key in filters) {
        if (dateFieldsMap[key] && filters[key]) {
          const field = dateFieldsMap[key];
          const operator = key.endsWith('_after') ? 'gte' : 'lte';
          // Check if a range filter for this field already exists
          const existingFilter = osFilters.find(
            (f) => f.range && f.range[field],
          );
          if (existingFilter?.range && existingFilter.range[field]) {
            // type guard
            (existingFilter.range[field] as Record<string, unknown>)[operator] =
              filters[key];
          } else {
            osFilters.push({
              range: {
                [field]: {
                  [operator]: filters[key],
                },
              },
            });
          }
        }
      }
      // Add other filter type handling here if needed by tests
      return osFilters;
    },
  ),
  isValidDateString: jest.fn().mockReturnValue(true),
};

// Updated mockBasicSearchQueryBuilder
const mockBasicSearchQueryBuilder = {
  build: jest.fn(
    (
      queryInput?: string,
      filters?: Record<string, unknown>,
      options?: {
        // This 'options' is the builderOptions from SearchService
        size?: number;
        from?: number;
        sort_config?: OpenSearchSortClause[];
        search_after?: (string | number | boolean)[];
      },
    ): OpenSearchSearchRequestBody => {
      const queryClause: OpenSearchQueryClause = queryInput
        ? { multi_match: { query: queryInput, fields: ['*'] } } // Simplified query part for tests
        : { match_all: {} };

      const builtFilters = filters
        ? mockQueryUtilityService.buildFilters(filters)
        : [];

      const body: OpenSearchSearchRequestBody = {
        // Explicitly typing the return for clarity
        query: {
          function_score: {
            query: {
              bool: {
                must: [queryClause],
                filter: builtFilters,
              },
            },
            functions: [], // Default, tests can override if they check this
            score_mode: 'sum',
            boost_mode: 'multiply',
          },
        },
        highlight: {
          // Default highlight structure often expected
          fields: {
            content: { fragment_size: 150, number_of_fragments: 3 },
            description: {},
            'metadata.mitre_attack.mitre_id': {},
            'metadata.mitre_attack.name': {},
            'metadata.mitre_attack.parent_name': {},
            'metadata.mitre_tactics': {},
            'metadata.mitre_techniques': {},
            'metadata.tags': {},
            'title.value': {},
          },
          pre_tags: ['<strong>'],
          post_tags: ['</strong>'],
        },
        _source: true, // Builder sets this
      };

      if (options?.size !== undefined) {
        body.size = options.size;
      }
      if (options?.from !== undefined) {
        body.from = options.from;
      }
      if (options?.sort_config) {
        body.sort = options.sort_config;
      }
      if (options?.search_after && options?.from === undefined) {
        // search_after and from are mutually exclusive
        body.search_after = options.search_after;
      }
      return body;
    },
  ),
};

const mockAdvancedSearchQueryBuilder = {
  build: jest.fn().mockReturnValue({ match_all: {} } as OpenSearchQueryClause), // Return a basic query clause
};

const mockSimilarRulesQueryBuilder = {
  build: jest.fn().mockReturnValue({ query: { more_like_this: {} } }), // Return basic MLT structure
};

const mockSuggestQueryBuilder = {
  build: jest.fn().mockReturnValue({ suggest: {} }), // Return basic suggest structure
};

describe('SearchService', () => {
  let service: SearchService;
  let openSearchService: jest.Mocked<OpenSearchService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot()],
      providers: [
        SearchService,
        {
          provide: RuleSearchRepository,
          useValue: {
            searchRules: jest.fn(),
            findSimilarRules: jest.fn(),
            getAutocompleteSuggestions: jest.fn(),
          },
        },
        { provide: OpenSearchConfigService, useClass: OpenSearchConfigService },
        {
          provide: OpenSearchService,
          useValue: {
            search: jest.fn().mockImplementation(() =>
              Promise.resolve({
                took: 5,
                timed_out: false,
                _shards: { total: 1, successful: 1, skipped: 0, failed: 0 },
                hits: {
                  total: { value: 100, relation: 'eq' },
                  max_score: 100,
                  hits: Array(20)
                    .fill({})
                    .map((_, i) => ({
                      _id: `rule${i}`,
                      _index: `rule-index-${i}`,
                      _source: {
                        title: { value: `Rule ${i}` },
                        created_at: `2023-01-${i + 1}`,
                        updated_at: `2023-01-${i + 1}T12:00:00Z`,
                        owner_id: `owner-${i}`,
                        rule_type: 'SIGMA' as RuleType,
                        status: 'DRAFT' as RuleStatus,
                        version: 1,
                        content: `Content for rule ${i}`,
                        created_by: `user-${i}`,
                        contributor: `contrib-${i}`,
                        severity: 'LOW',
                      },
                      _score: 100 - i,
                    })),
                },
              } as OpenSearchResponse<RuleSource>),
            ),
            get: jest.fn(),
          },
        },
        {
          provide: QueryUtilityService,
          useValue: mockQueryUtilityService,
        },
        {
          provide: BasicSearchQueryBuilder,
          useValue: mockBasicSearchQueryBuilder,
        },
        {
          provide: AdvancedSearchQueryBuilder,
          useValue: mockAdvancedSearchQueryBuilder,
        },
        {
          provide: SimilarRulesQueryBuilder,
          useValue: mockSimilarRulesQueryBuilder,
        },
        {
          provide: SuggestQueryBuilder,
          useValue: mockSuggestQueryBuilder,
        },
        CursorPaginationStrategy,
        OffsetPaginationStrategy,
        // Provide the mocked formatters
        {
          provide: SearchResponseFormatter,
          useValue: mockSearchResponseFormatter,
        },
        {
          provide: PagedSearchResponseFormatter,
          useValue: mockPagedSearchResponseFormatter,
        },
        {
          provide: SimilarRulesResponseFormatter,
          useValue: mockSimilarRulesResponseFormatter,
        },
        {
          provide: SuggestionsResponseFormatter,
          useValue: mockSuggestionsResponseFormatter,
        },
      ],
    }).compile();

    service = module.get<SearchService>(SearchService);
    openSearchService = module.get(OpenSearchService);

    // Mock internal methods that cause test failures
    // jest.spyOn(service, 'validateCursor').mockImplementation(async () => {});
    // jest.spyOn(service, 'formatSearchResponse').mockImplementation(async (res) => res as any);
    // jest
    //   .spyOn(service as any, 'getCachedCursor')
    //   .mockImplementation(() => null);
    // jest
    //   .spyOn(service as any, 'findClosestCachedPage')
    //   .mockImplementation(() => null);
  });

  afterAll(() => {
    // Clean up resources after all tests
    // service.clearResources(); // Removed call to deleted method
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('search', () => {
    it('should call cursorPaginationStrategy and searchResponseFormatter, then return its result', async () => {
      const mockQuery = 'test query';
      const mockFilters = {};
      const mockOptions: SearchOptionsDto = { page: 1, size: 10 }; // page is actually not used by cursor strategy's buildSearchBody

      const mockBuiltSearchBody: OpenSearchSearchRequestBody = {
        query: { match_all: {} },
        size: 11,
      };
      const mockOpenSearchResponse: OpenSearchResponse<RuleSource> = {
        took: 5,
        timed_out: false,
        _shards: { total: 1, successful: 1, skipped: 0, failed: 0 },
        hits: { total: { value: 1, relation: 'eq' }, max_score: 1, hits: [] },
      };
      const mockFormattedResponse = {
        status: 'success',
        meta: { total: 0, size: 0 },
        data: [],
      };

      jest.spyOn(mockBasicSearchQueryBuilder, 'build').mockReturnValue({
        query: { match_all: {} },
        size: 11,
        highlight: { fields: {} },
        _source: true,
      });
      const cursorStrategySpy = jest
        .spyOn(CursorPaginationStrategy.prototype, 'buildSearchBody')
        .mockReturnValue(mockBuiltSearchBody);
      openSearchService.search.mockResolvedValue(mockOpenSearchResponse);
      mockSearchResponseFormatter.format.mockReturnValue(mockFormattedResponse);

      const result = await service.search(mockQuery, mockFilters, mockOptions);

      expect(mockBasicSearchQueryBuilder.build).toHaveBeenCalledWith(
        mockQuery,
        mockFilters,
        expect.objectContaining({ size: (mockOptions.size || 20) + 1 }),
      );
      const expectedCoreQuery = mockBasicSearchQueryBuilder.build().query;
      const expectedCursorBuildOptions = expect.objectContaining({
        ...mockOptions,
        highlightConfig: expect.any(Object),
        sourceFields: true,
      });

      expect(cursorStrategySpy).toHaveBeenCalledWith(
        expectedCoreQuery,
        expectedCursorBuildOptions,
      );
      expect(openSearchService.search).toHaveBeenCalledWith(
        expect.any(String),
        mockBuiltSearchBody,
      );
      expect(mockSearchResponseFormatter.format).toHaveBeenCalledWith(
        mockOpenSearchResponse,
        expectedCursorBuildOptions,
      );
      expect(result).toBe(mockFormattedResponse);

      cursorStrategySpy.mockRestore();
    });

    // Remove old tests for 'search' method that manually constructed response or tested internal formatting
    // Add more tests for different options if necessary
  });

  describe('pagedSearch', () => {
    it('should call offsetPaginationStrategy and pagedSearchResponseFormatter, then return its result', async () => {
      const mockQuery = 'paged test';
      const mockFilters = { rule_types: [RuleType.SIGMA] };
      const mockPageOptions: PageSearchOptionsDto = { page: 2, size: 5 };

      const mockBuiltSearchBody: OpenSearchSearchRequestBody = {
        query: { bool: {} },
        size: 5,
        from: 5,
      };
      const mockOpenSearchResponse: OpenSearchResponse<RuleSource> = {
        took: 3,
        timed_out: false,
        _shards: { total: 1, successful: 1, skipped: 0, failed: 0 },
        hits: { total: { value: 10, relation: 'eq' }, max_score: 1, hits: [] },
      };
      const mockFormattedResponse = {
        status: 'success',
        meta: {
          total: 10,
          size: 0,
          current_page: 2,
          total_pages: 2,
          page_size: 5,
          has_next_page: false,
          has_prev_page: true,
        },
        data: [],
      };

      jest.spyOn(mockBasicSearchQueryBuilder, 'build').mockReturnValue({
        query: { bool: {} },
        highlight: { fields: {} },
        _source: true,
      });
      const offsetStrategySpy = jest
        .spyOn(OffsetPaginationStrategy.prototype, 'buildSearchBody')
        .mockReturnValue(mockBuiltSearchBody);
      openSearchService.search.mockResolvedValue(mockOpenSearchResponse);
      mockPagedSearchResponseFormatter.formatOffsetResponse.mockReturnValue(
        mockFormattedResponse,
      );

      const result = await service.pagedSearch(
        mockQuery,
        mockFilters,
        mockPageOptions,
      );

      expect(mockBasicSearchQueryBuilder.build).toHaveBeenCalledWith(
        mockQuery,
        mockFilters,
        expect.objectContaining({
          size: mockPageOptions.size,
          from: ((mockPageOptions.page || 1) - 1) * (mockPageOptions.size || 0),
        }),
      );
      const expectedCoreQuery = mockBasicSearchQueryBuilder.build().query;
      const expectedOffsetBuildOptions = expect.objectContaining({
        ...mockPageOptions,
        highlightConfig: expect.any(Object),
        sourceFields: true,
      });

      expect(offsetStrategySpy).toHaveBeenCalledWith(
        expectedCoreQuery,
        expectedOffsetBuildOptions,
      );
      expect(openSearchService.search).toHaveBeenCalledWith(
        expect.any(String),
        mockBuiltSearchBody,
      );
      expect(
        mockPagedSearchResponseFormatter.formatOffsetResponse,
      ).toHaveBeenCalledWith(
        mockOpenSearchResponse,
        expectedOffsetBuildOptions,
      );
      expect(result).toBe(mockFormattedResponse);
      offsetStrategySpy.mockRestore();
    });
  });

  describe('advancedSearch', () => {
    it('OFFSET mode: should use offset strategy and paged formatter', async () => {
      const mockQueryDto: AdvancedSearchQueryDto = {
        query_text: 'adv offset',
        options: { pagination_mode: PaginationMode.OFFSET, page: 1, size: 10 },
      };
      const mockCoreQuery: OpenSearchQueryClause = {
        term: { 'title.value': { value: 'adv offset' } },
      };
      const mockBuiltSearchBody: OpenSearchSearchRequestBody = {
        query: mockCoreQuery,
        size: 10,
        from: 0,
      };
      const mockOpenSearchResponse: OpenSearchResponse<RuleSource> = {
        hits: { total: { value: 5, relation: 'eq' }, hits: [] },
      } as any;
      const mockFormattedResponse = {
        status: 'success',
        meta: {
          total: 5,
          size: 0,
          current_page: 1,
          total_pages: 1,
          page_size: 10,
        },
        data: [],
      } as any;

      jest
        .spyOn(mockAdvancedSearchQueryBuilder, 'build')
        .mockReturnValue(mockCoreQuery);
      const offsetStrategySpy = jest
        .spyOn(OffsetPaginationStrategy.prototype, 'buildSearchBody')
        .mockReturnValue(mockBuiltSearchBody);
      openSearchService.search.mockResolvedValue(mockOpenSearchResponse);
      mockPagedSearchResponseFormatter.formatOffsetResponse.mockReturnValue(
        mockFormattedResponse,
      );

      const result = await service.advancedSearch(mockQueryDto);

      expect(mockAdvancedSearchQueryBuilder.build).toHaveBeenCalledWith(
        mockQueryDto,
      );
      const expectedOffsetBuildOptions = expect.objectContaining({
        ...mockQueryDto.options,
        highlightConfig: expect.any(Object),
        sourceFields: true,
      });
      expect(offsetStrategySpy).toHaveBeenCalledWith(
        mockCoreQuery,
        expectedOffsetBuildOptions,
      );
      expect(openSearchService.search).toHaveBeenCalledWith(
        expect.any(String),
        mockBuiltSearchBody,
      );
      expect(
        mockPagedSearchResponseFormatter.formatOffsetResponse,
      ).toHaveBeenCalledWith(
        mockOpenSearchResponse,
        expectedOffsetBuildOptions,
      );
      expect(result).toBe(mockFormattedResponse);
      offsetStrategySpy.mockRestore();
    });

    it('CURSOR mode: should use cursor strategy, search formatter, and paged cursor formatter', async () => {
      const mockQueryDto: AdvancedSearchQueryDto = {
        query_text: 'adv cursor',
        options: {
          pagination_mode: PaginationMode.CURSOR,
          size: 15,
          cursor: 'abc',
        },
      };
      const mockCoreQuery: OpenSearchQueryClause = {
        term: { description: { value: 'adv cursor' } },
      };
      const mockBuiltSearchBody: OpenSearchSearchRequestBody = {
        query: mockCoreQuery,
        size: 16,
      }; // size + 1 for cursor
      const mockOpenSearchResponse: OpenSearchResponse<RuleSource> = {
        hits: { total: { value: 20, relation: 'eq' }, hits: [] },
      } as any;
      const mockIntermediateFormattedResponse = {
        status: 'success',
        meta: { total: 20, size: 0, next_cursor: 'def' },
        data: [],
      } as any;
      const mockFinalPagedResponse = {
        status: 'success',
        meta: {
          total: 20,
          size: 0,
          current_page: 1,
          total_pages: 2,
          page_size: 15,
          next_cursor: 'def',
        },
        data: [],
      } as any;

      jest
        .spyOn(mockAdvancedSearchQueryBuilder, 'build')
        .mockReturnValue(mockCoreQuery);
      const cursorStrategySpy = jest
        .spyOn(CursorPaginationStrategy.prototype, 'buildSearchBody')
        .mockReturnValue(mockBuiltSearchBody);
      openSearchService.search.mockResolvedValue(mockOpenSearchResponse);
      mockSearchResponseFormatter.format.mockReturnValue(
        mockIntermediateFormattedResponse,
      );
      mockPagedSearchResponseFormatter.formatCursorResponse.mockReturnValue(
        mockFinalPagedResponse,
      );

      const result = await service.advancedSearch(mockQueryDto);

      expect(mockAdvancedSearchQueryBuilder.build).toHaveBeenCalledWith(
        mockQueryDto,
      );
      const expectedCursorBuildOptions = expect.objectContaining({
        ...mockQueryDto.options,
        highlightConfig: expect.any(Object),
        sourceFields: true,
      });
      expect(cursorStrategySpy).toHaveBeenCalledWith(
        mockCoreQuery,
        expectedCursorBuildOptions,
      );
      expect(openSearchService.search).toHaveBeenCalledWith(
        expect.any(String),
        mockBuiltSearchBody,
      );
      expect(mockSearchResponseFormatter.format).toHaveBeenCalledWith(
        mockOpenSearchResponse,
        expectedCursorBuildOptions,
      );
      expect(
        mockPagedSearchResponseFormatter.formatCursorResponse,
      ).toHaveBeenCalledWith(mockIntermediateFormattedResponse, {
        page: 1,
        size: mockQueryDto.options?.size || 20,
      });
      expect(result).toBe(mockFinalPagedResponse);
      cursorStrategySpy.mockRestore();
    });
  });

  describe('pagedCustomSortSearch', () => {
    it('should use offset strategy with custom sort and paged formatter', async () => {
      const mockSort: OpenSearchSortClause[] = [{ created_at: SortOrder.ASC }];
      const mockQuery = 'custom sort test';
      const mockPageOptions: PageSearchOptionsDto = { page: 1, size: 10 };
      const mockCoreQuery: OpenSearchQueryClause = { match_all: {} }; // from basic builder
      const mockBuiltSearchBody: OpenSearchSearchRequestBody = {
        query: mockCoreQuery,
        size: 10,
        from: 0,
        sort: mockSort,
      };
      const mockOpenSearchResponse: OpenSearchResponse<RuleSource> = {
        hits: { total: { value: 3, relation: 'eq' }, hits: [] },
      } as any;
      const mockFormattedResponse = {
        status: 'success',
        meta: { total: 3, page_size: 10, current_page: 1 },
        data: [],
      } as any;

      jest.spyOn(mockBasicSearchQueryBuilder, 'build').mockReturnValue({
        query: mockCoreQuery,
        highlight: { fields: {} },
        _source: true,
      });
      const offsetStrategySpy = jest
        .spyOn(OffsetPaginationStrategy.prototype, 'buildSearchBody')
        .mockReturnValue(mockBuiltSearchBody);
      openSearchService.search.mockResolvedValue(mockOpenSearchResponse);
      mockPagedSearchResponseFormatter.formatOffsetResponse.mockReturnValue(
        mockFormattedResponse,
      );

      const result = await service.pagedCustomSortSearch(
        mockSort,
        mockQuery,
        {},
        mockPageOptions,
      );

      expect(mockBasicSearchQueryBuilder.build).toHaveBeenCalledWith(
        mockQuery,
        {},
        expect.objectContaining({
          size: mockPageOptions.size,
          from: 0,
          sort_config: mockSort,
        }),
      );
      const expectedOffsetBuildOptions = expect.objectContaining({
        ...mockPageOptions,
        sortConfig: mockSort,
        highlightConfig: expect.any(Object),
        sourceFields: true,
      });
      expect(offsetStrategySpy).toHaveBeenCalledWith(
        mockCoreQuery,
        expectedOffsetBuildOptions,
      );
      expect(openSearchService.search).toHaveBeenCalledWith(
        expect.any(String),
        mockBuiltSearchBody,
      );
      expect(
        mockPagedSearchResponseFormatter.formatOffsetResponse,
      ).toHaveBeenCalledWith(
        mockOpenSearchResponse,
        expectedOffsetBuildOptions,
      );
      expect(result).toBe(mockFormattedResponse);
      offsetStrategySpy.mockRestore();
    });
  });

  describe('findSimilarRules', () => {
    it('should fetch rule, call query builder, search, and formatter', async () => {
      const mockRuleId = 'rule123';
      const mockOptions: SimilarRulesQueryDto = {
        size: 5,
      };
      const mockRuleSource: RuleSource = {
        title: { value: 'Test Rule' },
        content: 'Some content here',
        rule_type: RuleType.SIGMA,
        status: RuleStatus.DRAFT,
        owner_id: 'owner1',
        created_at: 'date',
        updated_at: 'date',
        created_by: 'mock_user',
        contributor: 'mock_contributor',
        severity: 'HIGH',
      };
      const mockGetResponse = {
        found: true,
        _source: mockRuleSource,
        _id: mockRuleId,
      } as any;

      // This is what similarRulesQueryBuilder.build is expected to return by SearchService
      // SearchService then passes this to the formatter.
      // The formatter SimilarRulesResponseFormatter defines this arg as OpenSearchSearchRequestBody
      const mockSimilarQueryBody = {
        query: {
          more_like_this: {
            fields: ['title', 'content'],
            like: 'Some content here',
          },
        },
      }; // Simplified

      const mockOpenSearchResponse = {
        hits: { total: { value: 1, relation: 'eq' }, hits: [] },
      } as any;
      const mockFormattedResponse = {
        status: 'success',
        meta: { reference_rule: mockRuleId, total: 1 },
        data: [],
      } as any;

      openSearchService.get.mockResolvedValue(mockGetResponse);
      jest
        .spyOn(mockSimilarRulesQueryBuilder, 'build')
        .mockReturnValue(mockSimilarQueryBody as any); // Cast to any to satisfy build, actual type is OpenSearchSearchRequestBody
      openSearchService.search.mockResolvedValue(mockOpenSearchResponse);
      mockSimilarRulesResponseFormatter.format.mockReturnValue(
        mockFormattedResponse,
      );

      const result = await service.findSimilarRules(mockRuleId, mockOptions);

      expect(openSearchService.get).toHaveBeenCalledWith(
        expect.any(String),
        mockRuleId,
      );
      const expectedFieldValues = {
        content: mockRuleSource.content,
        title: mockRuleSource.title?.value,
        description: mockRuleSource.description,
        mitre_techniques: mockRuleSource.metadata?.mitre_techniques || [],
        mitre_tactics: mockRuleSource.metadata?.mitre_tactics || [],
        tags: mockRuleSource.metadata?.tags || [],
      };
      expect(mockSimilarRulesQueryBuilder.build).toHaveBeenCalledWith(
        mockRuleId,
        expectedFieldValues,
        mockOptions,
        expect.any(String),
      );
      expect(openSearchService.search).toHaveBeenCalledWith(
        expect.any(String),
        mockSimilarQueryBody,
      );
      // The mockSimilarQueryBody is passed to the formatter.
      // Its type must be compatible with OpenSearchSearchRequestBody for SimilarRulesResponseFormatter.format
      expect(mockSimilarRulesResponseFormatter.format).toHaveBeenCalledWith(
        mockOpenSearchResponse,
        expectedFieldValues,
        mockRuleId,
        mockSimilarQueryBody as any,
      );
      expect(result).toBe(mockFormattedResponse);
    });
  });

  describe('getSuggestions', () => {
    it('should call suggest builder, search, and formatter', async () => {
      const mockPrefix = 'sug';
      const mockSize = 7;
      const mockSuggestBody = { suggest: { rule_title_suggestions: {} } }; // Simplified
      const mockOpenSearchResponse = {
        suggest: { rule_title_suggestions: [{ options: [] }] },
      } as any;
      const mockFormattedResponse = {
        status: 'success',
        data: ['suggestion1'],
      } as any;

      jest
        .spyOn(mockSuggestQueryBuilder, 'build')
        .mockReturnValue(mockSuggestBody);
      openSearchService.search.mockResolvedValue(mockOpenSearchResponse);
      mockSuggestionsResponseFormatter.format.mockReturnValue(
        mockFormattedResponse,
      );

      const result = await service.getSuggestions(mockPrefix, mockSize);

      expect(mockSuggestQueryBuilder.build).toHaveBeenCalledWith(
        mockPrefix,
        mockSize,
        undefined,
      ); // Assuming no visibleGroupIds
      expect(openSearchService.search).toHaveBeenCalledWith(
        expect.any(String),
        mockSuggestBody,
      );
      expect(mockSuggestionsResponseFormatter.format).toHaveBeenCalledWith(
        mockOpenSearchResponse,
      );
      expect(result).toBe(mockFormattedResponse);
    });
  });

  describe('search with date filters', () => {
    it('should throw BadRequestException for invalid date format in SearchService.search', async () => {
      mockQueryUtilityService.isValidDateString.mockImplementation(
        (dateStr) => dateStr !== 'invalid-date',
      );
      const filters = { created_after: 'invalid-date' };
      await expect(service.search('test', filters, {})).rejects.toThrow(
        BadRequestException,
      );
      expect(mockQueryUtilityService.isValidDateString).toHaveBeenCalledWith(
        'invalid-date',
      );
    });
  });
});
