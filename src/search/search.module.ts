import { Module } from '@nestjs/common';
import { SearchController } from './search.controller';
import { SearchService } from './search.service';
import { OpenSearchModule } from '../opensearch/opensearch.module';
import { OpenSearchRepositoriesModule } from '../opensearch/repositories/repositories.module';
import { FgaModule } from '../auth/fga/fga.module';
import { QueryUtilityService } from './query-builders/query.utility';
import { BasicSearchQueryBuilder } from './query-builders/basic-search.query-builder';
import { AdvancedSearchQueryBuilder } from './query-builders/advanced-search.query-builder';
import { SimilarRulesQueryBuilder } from './query-builders/similar-rules.query-builder';
import { SuggestQueryBuilder } from './query-builders/suggest.query-builder';
import { CursorPaginationStrategy } from './pagination/cursor-pagination.strategy';
import { OffsetPaginationStrategy } from './pagination/offset-pagination.strategy';
import {
  SearchResponseFormatter,
  PagedSearchResponseFormatter,
  SimilarRulesResponseFormatter,
  SuggestionsResponseFormatter,
} from './response-formatters';

/**
 * Module for search functionality
 */
@Module({
  imports: [OpenSearchModule, OpenSearchRepositoriesModule, FgaModule],
  controllers: [SearchController],
  providers: [
    SearchService,
    QueryUtilityService,
    BasicSearchQueryBuilder,
    AdvancedSearchQueryBuilder,
    SimilarRulesQueryBuilder,
    SuggestQueryBuilder,
    CursorPaginationStrategy,
    OffsetPaginationStrategy,
    SearchResponseFormatter,
    PagedSearchResponseFormatter,
    SimilarRulesResponseFormatter,
    SuggestionsResponseFormatter,
  ],
  exports: [SearchService],
})
export class SearchModule {}
