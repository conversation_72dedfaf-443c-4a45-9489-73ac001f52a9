import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MiddlewareConsumer } from '@nestjs/common';
import { LoggingMiddleware } from './middleware/logging.middleware';
import { FgaService } from './auth/fga/fga.service';
import { AiGenerationService } from './ai-generation/ai-generation.service';
import { RuleArchiveModule } from './rule-archive/rule-archive.module';
import { RuleArchiveService } from './rule-archive/rule-archive.service';
import { MitreSearchService } from './mitre/services/mitre-search.service';
import { MitreDataIngestionService } from './mitre/services/mitre-data-ingestion.service';
import { MitreDataMigrationService } from './mitre/services/mitre-data-migration.service';

// Mock the configValidationSchema and configFactory
jest.mock('./config/config.schema', () => ({
  configValidationSchema: {
    validate: jest.fn().mockReturnValue({ value: {} }),
  },
}));

jest.mock('./config/config.factory', () => ({
  configFactory: jest.fn().mockReturnValue({}),
}));

// Create a mock module for RuleArchiveModule
const mockRuleArchiveModule = {
  module: RuleArchiveModule,
  providers: [
    {
      provide: RuleArchiveService,
      useValue: {
        generateUploadUrl: jest.fn(),
        updateUploadStatus: jest.fn(),
      },
    },
  ],
  exports: [RuleArchiveService],
};

describe('AppModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    })
      // Override the RuleArchiveModule
      .overrideModule(RuleArchiveModule)
      .useModule(mockRuleArchiveModule)
      // Override ConfigService ONLY for Auth0 values needed by Auth0Strategy
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key: string) => {
          // Provide mock values for Auth0 strategy
          if (key === 'AUTH0_DOMAIN') return 'mock-domain.auth0.com';
          if (key === 'AUTH0_AUDIENCE') return 'mock-audience';
          if (key === 'AUTH0_ISSUER_URL')
            return 'https://mock-domain.auth0.com/';
          // Provide mock value for RulesService constructor
          if (key === 'RULES_DOWNLOAD_S3_BUCKET_NAME')
            return 'mock-rules-download-bucket';
          // Mock OPENAI_API_KEY for AiGenerationService
          if (key === 'OPENAI_API_KEY') return 'mock-openai-api-key';
          // Add AWS_REGION
          if (key === 'AWS_REGION') return 'test-aws-region';
          // Add S3_PUBLIC_BUCKET_NAME
          if (key === 'S3_PUBLIC_BUCKET_NAME') return 'test-s3-public-bucket';
          // Other non-FGA keys might be needed here eventually
          if (key === 'VERTEX_MODEL_NAME') return 'mock-vertex-model-name';
          if (key === 'VERTEX_LOCATION') return 'mock-vertex-location';
          if (key === 'VERTEX_PROJECT_ID') return 'mock-vertex-project-id';
          if (key === 'GOOGLE_APPLICATION_CREDENTIALS')
            return 'mock-google-application-credentials';
          if (key === 'GOOGLE_AUTH_DATA') return 'mock-google-auth-data';
          return undefined;
        }),
        getOrThrow: jest.fn((key: string) => {
          // Only provide non-FGA values needed by getOrThrow
          const value = {
            AUTH0_DOMAIN: 'mock-domain.auth0.com',
            AUTH0_AUDIENCE: 'mock-audience',
            AUTH0_ISSUER_URL: 'https://mock-domain.auth0.com/',
            RULES_DOWNLOAD_S3_BUCKET_NAME: 'mock-rules-download-bucket',
            OPENAI_API_KEY: 'mock-openai-api-key',
            AWS_REGION: 'test-aws-region',
            S3_PUBLIC_BUCKET_NAME: 'test-s3-public-bucket', // Add S3_PUBLIC_BUCKET_NAME here
            VERTEX_MODEL_NAME: 'mock-vertex-model-name',
            VERTEX_LOCATION: 'mock-vertex-location',
            VERTEX_PROJECT_ID: 'mock-vertex-project-id',
            GOOGLE_APPLICATION_CREDENTIALS:
              'mock-google-application-credentials',
            GOOGLE_AUTH_DATA: 'mock-google-auth-data',
          }[key];
          if (value === undefined) {
            // Keep the error for genuinely missing keys, but ignore FGA keys
            if (!key.startsWith('FGA_')) {
              throw new Error(`Missing configuration key: ${key}`);
            }
            return undefined; // Return undefined for FGA keys to avoid errors here
          }
          return value;
        }),
      })
      // Directly mock FgaService to bypass its problematic constructor
      .overrideProvider(FgaService)
      .useValue({
        // Provide mock implementations for any FgaService methods
        // that *might* be called directly or indirectly by AppModule/Controller/Service tests.
        // For now, an empty mock might suffice if FgaService isn't actually used.
        checkUserAuthorization: jest.fn().mockResolvedValue(true),
        // Add other methods used by guards/decorators applied globally if necessary
      })
      // Mock AiGenerationService to avoid OPENAI_API_KEY errors
      .overrideProvider(AiGenerationService)
      .useValue({
        getStatus: jest.fn().mockReturnValue({
          status: 'running',
          openai: 'v1.48.0',
          'systemtwosecurity/llm-kit-node': '0.1.3',
        }),
        generateRuleTitle: jest.fn().mockResolvedValue('AI-Generated Title'),
        generateRuleDescription: jest
          .fn()
          .mockResolvedValue('This is a rule description'),
      })
      // Mock MitreSearchService to avoid circular dependency
      .overrideProvider(MitreSearchService)
      .useValue({
        getObjectByUniqueMitreId: jest.fn(),
        getObjectByName: jest.fn(),
        getObjectByUniqueName: jest.fn(),
        searchMitreData: jest.fn(),
        executeOpenSearchQuery: jest.fn(),
        onApplicationBootstrap: jest.fn(),
      })
      // Mock MitreDataIngestionService
      .overrideProvider(MitreDataIngestionService)
      .useValue({
        ingestMitreData: jest.fn(),
      })
      // Mock MitreDataMigrationService
      .overrideProvider(MitreDataMigrationService)
      .useValue({
        migrateRules: jest.fn(),
      })
      .compile();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide AppController', () => {
    const controller = module.get<AppController>(AppController);
    expect(controller).toBeDefined();
    expect(controller).toBeInstanceOf(AppController);
  });

  it('should provide AppService', () => {
    const service = module.get<AppService>(AppService);
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(AppService);
  });

  describe('configure middleware', () => {
    it('should apply LoggingMiddleware to all routes', () => {
      const appModule = new AppModule();

      // Create a properly typed mock
      const mockConsumer = {
        apply: jest.fn().mockReturnThis(),
        forRoutes: jest.fn().mockReturnThis(),
      };

      appModule.configure(mockConsumer as unknown as MiddlewareConsumer);

      expect(mockConsumer.apply).toHaveBeenCalledWith(LoggingMiddleware);
      expect(mockConsumer.forRoutes).toHaveBeenCalledWith('*');
    });
  });
});
