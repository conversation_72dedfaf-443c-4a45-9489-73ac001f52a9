import { Test, TestingModule } from '@nestjs/testing';
import { EnrichmentHelper } from './enrichment-helper';
import { UserGroupsService } from '../auth/services/user-groups.service';
import { BookmarksService } from '../bookmarks/bookmarks.service';
import { Rule, RuleStatus, RuleType } from '../rules/models/rule.model';
import { BadRequestException } from '@nestjs/common';
import { UserResponse } from '../auth/interfaces/user-response.interface';
import { GroupResponse } from '../auth/interfaces/group-response.interface';
import { GroupType } from '../auth/interfaces/group-response.interface';
import { RuleInteractionsService } from '../rule-interactions/rule-interactions.service';
import { RuleInteraction } from '../rule-interactions/models/rule-interaction.model';
import { RuleInteractionType } from '../rule-interactions/models/rule-interaction.model';

describe('EnrichmentHelper', () => {
  let helper: EnrichmentHelper;
  let mockUserGroupsService: jest.Mocked<UserGroupsService>;
  let mockBookmarksService: jest.Mocked<BookmarksService>;
  let mockRuleInteractionsService: jest.Mocked<RuleInteractionsService>;

  const mockUser: UserResponse = {
    id: 'user-id-1',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    username: 'test-user',
    display_picture_url: 'https://example.com/test-user.png',
  };

  const mockGroup: GroupResponse = {
    id: 'group-id-1',
    name: 'Test Group',
    description: 'A test group',
    type: GroupType.PUBLIC,
  };

  const mockRules: Rule[] = [
    {
      id: 'rule-id-1',
      title: { value: 'Test Rule 1' },
      description: 'Test Description 1',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 1',
      created_by: 'user-id-1',
      group_id: 'group-id-1',
      status: RuleStatus.PUBLISHED,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-1',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      bookmarks: 0,
    },
    {
      id: 'rule-id-2',
      title: { value: 'Test Rule 2' },
      description: 'Test Description 2',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 2',
      created_by: 'user-id-1',
      status: RuleStatus.DRAFT,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-2',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      bookmarks: 0,
    },
  ];

  const mockInteractions: RuleInteraction[] = [
    {
      user_id: 'user-id-1',
      rule_id: 'rule-id-1',
      type: RuleInteractionType.LIKE,
      created_at: '2021-01-01',
    },
    {
      user_id: 'user-id-1',
      rule_id: 'rule-id-2',
      type: RuleInteractionType.DISLIKE,
      created_at: '2021-01-01',
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EnrichmentHelper,
        {
          provide: UserGroupsService,
          useFactory: () => ({
            getBulkUserDetails: jest.fn(),
            getBulkGroupDetails: jest.fn(),
          }),
        },
        {
          provide: BookmarksService,
          useFactory: () => ({
            getBookmarkMapForRules: jest.fn(),
          }),
        },
        {
          provide: RuleInteractionsService,
          useFactory: () => ({
            getUserInteractionsWithRules: jest.fn(),
          }),
        },
      ],
    }).compile();

    helper = module.get<EnrichmentHelper>(EnrichmentHelper);
    mockUserGroupsService = module.get(UserGroupsService);
    mockBookmarksService = module.get(BookmarksService);
    mockRuleInteractionsService = module.get(RuleInteractionsService);
  });

  it('should be defined', () => {
    expect(helper).toBeDefined();
  });

  describe('enrichRulesWithDetails', () => {
    it('should enrich rules with user and group details and bookmark information', async () => {
      // Arrange
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);
      mockUserGroupsService.getBulkGroupDetails.mockResolvedValue([mockGroup]);
      mockBookmarksService.getBookmarkMapForRules.mockResolvedValue(
        new Map([
          ['rule-id-1', true],
          ['rule-id-2', false],
        ]),
      );
      mockRuleInteractionsService.getUserInteractionsWithRules.mockResolvedValue(
        mockInteractions,
      );

      // Act
      const result = await helper.enrichRulesWithDetails(
        mockRules,
        'Bearer test-token',
        'user-id-1',
      );

      // Assert
      expect(mockUserGroupsService.getBulkUserDetails).toHaveBeenCalledWith(
        ['user-id-1'],
        'Bearer test-token',
      );
      expect(mockUserGroupsService.getBulkGroupDetails).toHaveBeenCalledWith(
        ['group-id-1'],
        'Bearer test-token',
      );
      expect(mockBookmarksService.getBookmarkMapForRules).toHaveBeenCalledWith(
        'user-id-1',
        ['rule-id-1', 'rule-id-2'],
      );
      expect(result.length).toBe(2);
      expect(result[0]).toHaveProperty('created_by_user', mockUser);
      expect(result[0]).toHaveProperty('group', mockGroup);
      expect(result[0]).toHaveProperty('is_bookmarked', true);
      expect(result[1]).toHaveProperty('created_by_user', mockUser);
      expect(result[1]).toHaveProperty('group', undefined);
      expect(result[1]).toHaveProperty('is_bookmarked', false);
      expect(result[0]).toHaveProperty('is_liked', true);
      expect(result[1]).toHaveProperty('is_liked', false);
      expect(result[0]).toHaveProperty('is_disliked', false);
      expect(result[1]).toHaveProperty('is_disliked', true);
    });

    it('should skip bookmark checking when ignoreBookmarks is true', async () => {
      // Arrange
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);
      mockUserGroupsService.getBulkGroupDetails.mockResolvedValue([mockGroup]);
      mockRuleInteractionsService.getUserInteractionsWithRules.mockResolvedValue(
        mockInteractions,
      );

      // Act
      const result = await helper.enrichRulesWithDetails(
        mockRules,
        'Bearer test-token',
        'user-id-1',
        true, // ignoreBookmarks
      );

      // Assert
      expect(
        mockBookmarksService.getBookmarkMapForRules,
      ).not.toHaveBeenCalled();
      expect(result[0]).toHaveProperty('is_bookmarked', false);
      expect(result[1]).toHaveProperty('is_bookmarked', false);
    });

    it('should return empty array when no rules are provided', async () => {
      // Act
      const result = await helper.enrichRulesWithDetails(
        [],
        'Bearer test-token',
        'user-id-1',
      );

      // Assert
      expect(result).toEqual([]);
      expect(mockUserGroupsService.getBulkUserDetails).not.toHaveBeenCalled();
      expect(mockUserGroupsService.getBulkGroupDetails).not.toHaveBeenCalled();
      expect(
        mockBookmarksService.getBookmarkMapForRules,
      ).not.toHaveBeenCalled();
    });

    it('should enrich rules with interaction status', async () => {
      // Arrange
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);
      mockUserGroupsService.getBulkGroupDetails.mockResolvedValue([mockGroup]);
      mockBookmarksService.getBookmarkMapForRules.mockResolvedValue(
        new Map([['rule-id-1', true]]),
      );
      mockRuleInteractionsService.getUserInteractionsWithRules.mockResolvedValue(
        [
          {
            user_id: 'user-id-1',
            rule_id: 'rule-id-1',
            type: RuleInteractionType.LIKE,
            created_at: '2021-01-01',
          },
        ],
      );

      // Act
      const result = await helper.enrichRulesWithDetails(
        mockRules,
        'Bearer test-token',
        'user-id-1',
      );

      // Assert
      expect(
        mockRuleInteractionsService.getUserInteractionsWithRules,
      ).toHaveBeenCalledWith(
        'user-id-1',
        ['rule-id-1', 'rule-id-2'],
        [RuleInteractionType.LIKE, RuleInteractionType.DISLIKE],
      );
      expect(result[0].is_liked).toBe(true);
      expect(result[0].is_disliked).toBe(false);
      expect(result[1].is_liked).toBe(false);
      expect(result[1].is_disliked).toBe(false);
    });

    it('should skip interaction enrichment when ignoreInteractions is true', async () => {
      // Arrange
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);
      mockUserGroupsService.getBulkGroupDetails.mockResolvedValue([mockGroup]);
      mockBookmarksService.getBookmarkMapForRules.mockResolvedValue(
        new Map([['rule-id-1', true]]),
      );

      // Act
      const result = await helper.enrichRulesWithDetails(
        mockRules,
        'Bearer test-token',
        'user-id-1',
        false, // ignoreBookmarks
        true, // ignoreInteractions
      );

      // Assert
      expect(
        mockRuleInteractionsService.getUserInteractionsWithRules,
      ).not.toHaveBeenCalled();
      expect(result[0].is_liked).toBe(false);
      expect(result[0].is_disliked).toBe(false);
      expect(result[1].is_liked).toBe(false);
      expect(result[1].is_disliked).toBe(false);
    });
  });

  describe('fetchGroupDetails', () => {
    it('should fetch group details', async () => {
      // Arrange
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);
      mockUserGroupsService.getBulkGroupDetails.mockResolvedValue([mockGroup]);

      // Act
      const result = await helper.fetchGroupDetails(
        'group-id-1',
        'Bearer test-token',
      );

      // Assert
      expect(mockUserGroupsService.getBulkGroupDetails).toHaveBeenCalledWith(
        ['group-id-1'],
        'Bearer test-token',
      );
      expect(result).toEqual(mockGroup);
    });

    it('should handle null group ID', async () => {
      // Arrange
      mockUserGroupsService.getBulkUserDetails.mockResolvedValue([mockUser]);

      // Act
      const result = await helper.fetchGroupDetails(null, 'Bearer test-token');

      // Assert
      expect(mockUserGroupsService.getBulkGroupDetails).not.toHaveBeenCalled();
      expect(result).toEqual(null);
    });

    it('should handle group not found', async () => {
      mockUserGroupsService.getBulkGroupDetails.mockResolvedValue([mockGroup]);

      // Act
      const result = await helper.fetchGroupDetails(
        'group-id-1',
        'Bearer test-token',
      );

      // Assert
      expect(result).toEqual(mockGroup);
    });
  });

  describe('validateUserAndGroupExist', () => {
    it('should not throw when user and group exist', () => {
      // Act & Assert
      expect(() => {
        helper.validateUserAndGroupExist(mockUser, mockGroup, 'group-id-1');
      }).not.toThrow();
    });

    it('should throw BadRequestException when user does not exist', () => {
      // Act & Assert
      expect(() => {
        helper.validateUserAndGroupExist(null, mockGroup, 'group-id-1');
      }).toThrow(BadRequestException);
    });

    it('should throw BadRequestException when group does not exist but is required', () => {
      // Act & Assert
      expect(() => {
        helper.validateUserAndGroupExist(mockUser, null, 'group-id-1');
      }).toThrow(BadRequestException);
    });

    it('should not throw when group is null and not required', () => {
      // Act & Assert
      expect(() => {
        helper.validateUserAndGroupExist(mockUser, null, null);
      }).not.toThrow();
    });
  });

  describe('validateRuleStatusAndGroup', () => {
    it('should not throw when PUBLISHED rule has a group_id', () => {
      // Act & Assert
      expect(() => {
        helper.validateRuleStatusAndGroup(RuleStatus.PUBLISHED, 'group-id-1');
      }).not.toThrow();
    });

    it('should throw BadRequestException when PUBLISHED rule has no group_id', () => {
      // Act & Assert
      expect(() => {
        helper.validateRuleStatusAndGroup(RuleStatus.PUBLISHED, null);
      }).toThrow(BadRequestException);
    });

    it('should not throw when DRAFT rule has no group_id', () => {
      // Act & Assert
      expect(() => {
        helper.validateRuleStatusAndGroup(RuleStatus.DRAFT, null);
      }).not.toThrow();
    });

    it('should throw BadRequestException when DRAFT rule has a group_id', () => {
      // Act & Assert
      expect(() => {
        helper.validateRuleStatusAndGroup(RuleStatus.DRAFT, 'group-id-1');
      }).toThrow(BadRequestException);
    });
  });

  describe('enrichSingleRuleWithDetails', () => {
    it('should enrich a rule with user and group details', () => {
      // Act
      const result = helper.enrichSingleRuleWithDetails(
        mockRules[0],
        mockUser,
        mockGroup,
      );

      // Assert
      expect(result).toEqual({
        ...mockRules[0],
        created_by_user: mockUser,
        group: mockGroup,
      });
    });

    it('should handle null group_id', () => {
      // Act
      const result = helper.enrichSingleRuleWithDetails(
        mockRules[1],
        mockUser,
        null,
      );

      // Assert
      expect(result).toEqual({
        ...mockRules[1],
        created_by_user: mockUser,
        group: undefined,
      });
    });
  });
});
