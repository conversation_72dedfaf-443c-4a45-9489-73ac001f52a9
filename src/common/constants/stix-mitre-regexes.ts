export const STIX_ID_REGEX =
  /^[a-z0-9][a-z0-9-]+[a-z0-9]--[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

export const MITRE_ID_REGEX = /^T\d{4}(\.\d{3})?$/;

export const MITRE_API_IDENTIFIER_REGEX: RegExp =
  /^([a-z0-9][a-z0-9-]+[a-z0-9]--[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}|G\d{4}|T\d{4}(\.\d{3})?|C\d{4}|TA\d{4}|S\d{4}|M\d{4}|DS\d{4}(\.DC\d{4})?)$/i;

export const MITRE_TEXTUAL_IDENTIFIER_REGEX: RegExp =
  /^[a-zA-Z]+([a-zA-Z\s-]*[a-zA-Z])?$/;

// Regex for extracting MITRE IDs from content text - fixed to match official formats
export const MITRE_CONTENT_EXTRACTION_REGEX: RegExp =
  /\b(T\d{4}(?:\.\d{3})?|TA\d{4}|S\d{4}|G\d{4}|M\d{4}|C\d{4}|DS\d{4}(?:\.\d{3})?)\b/gi;

// Regex for extracting technique IDs from attack tags (e.g., attack.t1003)
export const ATTACK_TECHNIQUE_TAG_REGEX: RegExp =
  /attack\.([tT]\d{4}(?:\.\d{3})?)/gi;

// Regex for extracting tactic names from attack tags (e.g., attack.defense-evasion)
export const ATTACK_TACTIC_TAG_REGEX: RegExp = /attack\.([a-z-]+)/gi;
