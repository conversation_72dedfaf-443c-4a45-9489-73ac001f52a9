import { Rule, RuleStatus, RuleType } from '../../rules/models/rule.model';
import { RuleMetadata } from '../../rules/models/rule-metadata.model';

export interface OpenSearchResponse {
  data: OpenSearchRuleResult[];
  meta: {
    total: number;
    size: number;
    current_page: number;
    total_pages: number;
    page_size: number;
    has_next_page: boolean;
    has_prev_page: boolean;
  };
  status: string;
}

/**
 * Interface for Elasticsearch title field with suggest context
 */
interface ElasticsearchTitle {
  value: string;
  suggest: {
    input: string;
    contexts: {
      rule_type: string[];
      owner_id: string[];
    };
  };
}

/**
 * Interface for Elasticsearch rule search result
 */
export interface OpenSearchRuleResult {
  id: string;
  title: ElasticsearchTitle;
  description: string;
  content: string;
  contributor: string;
  group_name?: string;
  likes: number;
  downloads: number;
  dislikes: number;
  rule_type: string;
  version: number;
  status: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  owner_id?: string;
  metadata?: RuleMetadata;
  all_text?: string;
  _score?: number | null;
  ai_generated?: {
    description: boolean;
    title?: boolean;
    content?: boolean;
    tags?: boolean;
  };
  created_by_user?: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    display_picture_url: string | null;
  };
  is_bookmarked?: boolean;
  is_liked?: boolean;
  is_disliked?: boolean;
}

export function mapOpenSearchRuleToRule(esRule: OpenSearchRuleResult): Rule {
  const rule = new Rule();
  rule.id = esRule.id;

  if (esRule.title) {
    rule.title = {
      value: esRule.title.value,
      suggest:
        typeof esRule.title.suggest === 'object' &&
        esRule.title.suggest !== null
          ? (esRule.title.suggest as { input: string }).input
          : typeof esRule.title.suggest === 'string'
            ? esRule.title.suggest
            : undefined,
    };
  } else {
    rule.title = undefined;
  }

  rule.description = esRule.description;
  rule.status = esRule.status as RuleStatus;
  rule.rule_type = esRule.rule_type as RuleType;
  rule.content = esRule.content;
  rule.contributor = esRule.contributor;
  rule.created_by = esRule.created_by;
  rule.likes = esRule.likes;
  rule.downloads = esRule.downloads;
  rule.dislikes = esRule.dislikes;
  rule.version = esRule.version;
  rule.created_at = esRule.created_at;
  rule.updated_at = esRule.updated_at;
  rule.published_at = esRule.updated_at;
  rule.group_id = esRule.owner_id;
  rule.group_name = esRule.group_name;
  rule.tags = []; // TODO: Add tags
  rule.metadata = esRule.metadata || ({} as RuleMetadata);
  rule.ai_generated = {
    description: esRule.ai_generated?.description || false,
    title: esRule.ai_generated?.title || false,
    content: esRule.ai_generated?.content || false,
    tags: esRule.ai_generated?.tags || false,
  };
  rule.is_bookmarked = esRule.is_bookmarked;
  rule.is_liked = esRule.is_liked;
  rule.is_disliked = esRule.is_disliked;
  rule.test_cases = []; // TODO: Add test cases
  return rule;
}
