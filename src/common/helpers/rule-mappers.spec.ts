import { mapOpenSearchRuleToRule, OpenSearchRuleResult } from './rule-mappers';
import { Rule, RuleStatus, RuleType } from '../../rules/models/rule.model';

describe('Rule Mappers', () => {
  describe('mapOpenSearchRuleToRule', () => {
    it('should correctly map OpenSearchRuleResult to Rule with ai_generated field', () => {
      // Arrange
      const input: OpenSearchRuleResult = {
        id: '275a0343-7737-42f0-ac7c-a2549ad82085',
        title: {
          value: 'James DRAFT test 9',
          suggest: {
            input: 'James DRAFT test 9',
            contexts: {
              rule_type: ['SIGMA'],
              owner_id: ['04f3a87b-37fa-4cee-beab-6f2cf68bef49'],
            },
          },
        },
        description:
          'This rule detects multiple failed login attempts from the same IP address',
        content: 'function(data) { return data.failedAttempts > 5; }',
        contributor: 'jimmy',
        group_name: 'James Public Group',
        likes: 4,
        downloads: 4,
        dislikes: 1,
        rule_type: 'SIGMA',
        version: 1,
        status: 'PUBLISHED',
        created_by: '31bb3cdf-faeb-44ec-bd72-625bf2b946bb',
        created_at: '2025-03-25T19:50:23.556Z',
        updated_at: '2025-03-25T20:40:30.500Z',
        owner_id: '04f3a87b-37fa-4cee-beab-6f2cf68bef49',
        metadata: {
          severity: 'high',
          author: 'security-team',
        },
        ai_generated: {
          description: true,
          title: false,
          content: true,
          tags: true,
        },
        all_text:
          'James DRAFT test 9 This rule detects multiple failed login attempts from the same IP address function(data) { return data.failedAttempts > 5; } contributor:jimmy',
        _score: null,
        created_by_user: {
          id: '31bb3cdf-faeb-44ec-bd72-625bf2b946bb',
          username: 'jimmy',
          first_name: 'James',
          last_name: 'Muir',
          display_picture_url: null,
        },
        is_bookmarked: true,
        is_liked: false,
        is_disliked: true,
      };

      const expected: Rule = {
        id: '275a0343-7737-42f0-ac7c-a2549ad82085',
        title: {
          value: input.title.value,
          suggest: input.title.suggest.input,
        },
        description:
          'This rule detects multiple failed login attempts from the same IP address',
        rule_type: 'SIGMA' as RuleType,
        content: 'function(data) { return data.failedAttempts > 5; }',
        tags: [],
        metadata: {
          severity: 'high',
          author: 'security-team',
        },
        ai_generated: {
          description: true,
          title: false,
          content: true,
          tags: true,
        },
        test_cases: [],
        created_at: '2025-03-25T19:50:23.556Z',
        updated_at: '2025-03-25T20:40:30.500Z',
        published_at: '2025-03-25T20:40:30.500Z',
        group_id: '04f3a87b-37fa-4cee-beab-6f2cf68bef49',
        group_name: 'James Public Group',
        status: 'PUBLISHED' as RuleStatus,
        created_by: '31bb3cdf-faeb-44ec-bd72-625bf2b946bb',
        version: 1,
        contributor: 'jimmy',
        likes: 4,
        downloads: 4,
        dislikes: 1,
        is_bookmarked: true,
        is_liked: false,
        is_disliked: true,
      } as Rule;

      // Act
      const result = mapOpenSearchRuleToRule(input);

      // Assert
      expect(result).toEqual(expected);
    });

    it('should set default ai_generated.description=false when field is missing', () => {
      // Arrange
      const input: OpenSearchRuleResult = {
        id: '275a0343-7737-42f0-ac7c-a2549ad82085',
        title: {
          value: 'James DRAFT test 9',
          suggest: {
            input: 'James DRAFT test 9',
            contexts: {
              rule_type: ['SIGMA'],
              owner_id: ['04f3a87b-37fa-4cee-beab-6f2cf68bef49'],
            },
          },
        },
        description: 'Test description',
        content: 'Test content',
        contributor: 'jimmy',
        group_name: 'Test Group',
        likes: 0,
        downloads: 0,
        dislikes: 0,
        rule_type: 'SIGMA',
        version: 1,
        status: 'DRAFT',
        created_by: '31bb3cdf-faeb-44ec-bd72-625bf2b946bb',
        created_at: '2025-03-25T19:50:23.556Z',
        updated_at: '2025-03-25T20:40:30.500Z',
        owner_id: '04f3a87b-37fa-4cee-beab-6f2cf68bef49',
        // ai_generated field is intentionally omitted
      };

      // Act
      const result = mapOpenSearchRuleToRule(input);

      // Assert
      expect(result.ai_generated).toBeDefined();
      expect(result.ai_generated?.description).toBe(false);
      expect(result.ai_generated?.title).toBe(false);
      expect(result.ai_generated?.content).toBe(false);
      expect(result.ai_generated?.tags).toBe(false);
    });

    it('should set default ai_generated.title=false when not specified', () => {
      // Arrange
      const input: OpenSearchRuleResult = {
        id: '275a0343-7737-42f0-ac7c-a2549ad82085',
        title: {
          value: 'Test Title',
          suggest: {
            input: 'Test Title',
            contexts: {
              rule_type: ['SIGMA'],
              owner_id: ['test-owner'],
            },
          },
        },
        description: 'Test description',
        content: 'Test content',
        contributor: 'test-user',
        rule_type: 'SIGMA',
        version: 1,
        status: 'DRAFT',
        created_by: 'test-creator',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        likes: 0,
        downloads: 0,
        dislikes: 0,
        owner_id: 'test-owner',
        ai_generated: {
          description: true,
          title: false,
          content: true,
          tags: true,
        },
      };

      // Act
      const result = mapOpenSearchRuleToRule(input);

      // Assert
      expect(result.ai_generated).toBeDefined();
      expect(result.ai_generated?.description).toBe(true);
      expect(result.ai_generated?.title).toBe(false);
      expect(result.ai_generated?.content).toBe(true);
      expect(result.ai_generated?.tags).toBe(true);
    });

    it('should set default ai_generated.content=false when not specified', () => {
      // Arrange
      const input: OpenSearchRuleResult = {
        id: '275a0343-7737-42f0-ac7c-a2549ad82085',
        title: {
          value: 'Test Title',
          suggest: {
            input: 'Test Title',
            contexts: {
              rule_type: ['SIGMA'],
              owner_id: ['test-owner'],
            },
          },
        },
        description: 'Test description',
        content: 'Test content',
        contributor: 'test-user',
        rule_type: 'SIGMA',
        version: 1,
        status: 'DRAFT',
        created_by: 'test-creator',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        likes: 0,
        downloads: 0,
        dislikes: 0,
        owner_id: 'test-owner',
        ai_generated: {
          description: true,
          title: true,
          // content field intentionally omitted
          tags: true,
        },
      };

      // Act
      const result = mapOpenSearchRuleToRule(input);

      // Assert
      expect(result.ai_generated).toBeDefined();
      expect(result.ai_generated?.description).toBe(true);
      expect(result.ai_generated?.title).toBe(true);
      expect(result.ai_generated?.content).toBe(false);
      expect(result.ai_generated?.tags).toBe(true);
    });

    it('should set default ai_generated.tags=false when not specified', () => {
      // Arrange
      const input: OpenSearchRuleResult = {
        id: '275a0343-7737-42f0-ac7c-a2549ad82085',
        title: {
          value: 'Test Title',
          suggest: {
            input: 'Test Title',
            contexts: {
              rule_type: ['SIGMA'],
              owner_id: ['test-owner'],
            },
          },
        },
        description: 'Test description',
        content: 'Test content',
        contributor: 'test-user',
        rule_type: 'SIGMA',
        version: 1,
        status: 'DRAFT',
        created_by: 'test-creator',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        likes: 0,
        downloads: 0,
        dislikes: 0,
        owner_id: 'test-owner',
        ai_generated: {
          description: true,
          title: true,
          content: true,
          // tags field intentionally omitted
        },
      };

      // Act
      const result = mapOpenSearchRuleToRule(input);

      // Assert
      expect(result.ai_generated).toBeDefined();
      expect(result.ai_generated?.description).toBe(true);
      expect(result.ai_generated?.title).toBe(true);
      expect(result.ai_generated?.content).toBe(true);
      expect(result.ai_generated?.tags).toBe(false);
    });
  });
});
