import { Test, TestingModule } from '@nestjs/testing';
import { BookmarksService } from './bookmarks.service';
import { BookmarkRepository } from './repositories/bookmark.repository';
import { CounterChange, RulesService } from '../rules/rules.service';
import { NotFoundException } from '@nestjs/common';
import { Rule, RuleStatus, RuleType } from '../rules/models/rule.model';
import { Logger } from '@nestjs/common';
import { RuleInteractionType } from '../rule-interactions/models/rule-interaction.model';

describe('BookmarksService', () => {
  let service: BookmarksService;
  let bookmarkRepository: jest.Mocked<BookmarkRepository>;
  let rulesService: jest.Mocked<RulesService>;

  const mockRule: Rule = {
    id: 'test-id',
    title: { value: 'Test Rule' },
    status: RuleStatus.PUBLISHED,
    group_id: 'test-group',
    description: 'Test Description',
    rule_type: RuleType.SIGMA,
    content: 'Test Content',
    created_by: 'test-user',
    created_at: '2021-01-01',
    updated_at: '2021-01-01',
    group_name: 'test-group',
    tags: [],
    metadata: { severity: 'high' },
    test_cases: [],
    version: 1,
    contributor: 'test-contributor',
    likes: 0,
    downloads: 0,
    dislikes: 0,
  };

  const mockBookmark = {
    user_id: 'user-id-1',
    rule_id: 'rule-id-1',
    group_id: 'group-id-1',
    created_at: Date.now(),
  };

  beforeEach(async () => {
    const mockBookmarkRepo = {
      findOne: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findByUserId: jest.fn(),
      checkUserBookmarks: jest.fn(),
      deleteAllForRule: jest.fn(),
    };

    const mockRulesService = {
      findOne: jest.fn(),
      updateCounts: jest.fn().mockResolvedValue(Promise.resolve()),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BookmarksService,
        {
          provide: BookmarkRepository,
          useValue: mockBookmarkRepo,
        },
        {
          provide: RulesService,
          useValue: mockRulesService,
        },
      ],
    }).compile();

    service = module.get<BookmarksService>(BookmarksService);
    bookmarkRepository = module.get(BookmarkRepository);
    rulesService = module.get(RulesService);

    // Mock the logger to prevent console output during tests
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
  });

  describe('bookmarkRule', () => {
    it('should bookmark a rule successfully', async () => {
      // Arrange
      rulesService.findOne.mockResolvedValue({
        ...mockRule,
        status: RuleStatus.PUBLISHED,
        group_id: 'group-id-1',
      });
      bookmarkRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await service.bookmarkRule('user-id-1', 'rule-id-1');

      // Assert
      expect(rulesService.findOne).toHaveBeenCalledWith('rule-id-1');
      expect(bookmarkRepository.findOne).toHaveBeenCalledWith({
        user_id: 'user-id-1',
        rule_id: 'rule-id-1',
      });
      expect(bookmarkRepository.create).toHaveBeenCalledWith({
        user_id: 'user-id-1',
        rule_id: 'rule-id-1',
        group_id: 'group-id-1',
        created_at: expect.any(Number),
      });
      expect(result).toBe(true);
      expect(rulesService.updateCounts).toHaveBeenCalledWith(
        'rule-id-1',
        RuleInteractionType.BOOKMARK,
        CounterChange.INCREMENT,
      );
    });

    it('should return false if bookmark already exists', async () => {
      // Arrange
      rulesService.findOne.mockResolvedValue({
        ...mockRule,
        status: RuleStatus.PUBLISHED,
        group_id: 'group-id-1',
      });
      bookmarkRepository.findOne.mockResolvedValue(mockBookmark);

      // Act
      const result = await service.bookmarkRule('user-id-1', 'rule-id-1');

      // Assert
      expect(bookmarkRepository.create).not.toHaveBeenCalled();
      expect(result).toBe(false);
      expect(rulesService.updateCounts).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if rule does not exist', async () => {
      // Arrange
      rulesService.findOne.mockResolvedValue(null as unknown as Rule);

      // Act & Assert
      await expect(
        service.bookmarkRule('user-id-1', 'rule-id-1'),
      ).rejects.toThrow(NotFoundException);
      expect(bookmarkRepository.findOne).not.toHaveBeenCalled();
      expect(bookmarkRepository.create).not.toHaveBeenCalled();
      expect(rulesService.updateCounts).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if rule is not published', async () => {
      // Arrange
      rulesService.findOne.mockResolvedValue({
        ...mockRule,
        status: RuleStatus.DRAFT,
      });

      // Act & Assert
      await expect(
        service.bookmarkRule('user-id-1', 'rule-id-1'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if rule has no group_id', async () => {
      // Arrange
      rulesService.findOne.mockResolvedValue({
        ...mockRule,
        status: RuleStatus.PUBLISHED,
        group_id: undefined,
      });

      // Act & Assert
      await expect(
        service.bookmarkRule('user-id-1', 'rule-id-1'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('unbookmarkRule', () => {
    it('should unbookmark a rule successfully', async () => {
      // Arrange
      bookmarkRepository.findOne.mockResolvedValue(mockBookmark);
      bookmarkRepository.delete.mockResolvedValue(mockBookmark);

      // Act
      const result = await service.unbookmarkRule('user-id-1', 'rule-id-1');

      // Assert
      expect(bookmarkRepository.delete).toHaveBeenCalledWith({
        user_id: 'user-id-1',
        rule_id: 'rule-id-1',
      });
      expect(result).toBe(true);
      expect(rulesService.updateCounts).toHaveBeenCalledWith(
        'rule-id-1',
        RuleInteractionType.BOOKMARK,
        CounterChange.DECREMENT,
      );
    });

    it('should return false if bookmark does not exist', async () => {
      // Arrange
      bookmarkRepository.delete.mockResolvedValue(null);

      // Act
      const result = await service.unbookmarkRule('user-id-1', 'rule-id-1');

      // Assert
      expect(result).toBe(false);
      expect(rulesService.updateCounts).not.toHaveBeenCalled();
    });
  });

  describe('addBookmarkInfoToRules', () => {
    it('should add bookmark info to rules', async () => {
      // Arrange
      const rules = [
        { id: 'rule-id-1', title: { value: 'Rule 1' } },
        { id: 'rule-id-2', title: { value: 'Rule 2' } },
      ];

      const bookmarkMap = new Map([['rule-id-1', true]]);

      bookmarkRepository.checkUserBookmarks.mockResolvedValue(bookmarkMap);

      // Act
      const result = await service.addBookmarkInfoToRules(
        rules as Rule[],
        'user-id-1',
      );

      // Assert
      expect(bookmarkRepository.checkUserBookmarks).toHaveBeenCalledWith(
        'user-id-1',
        ['rule-id-1', 'rule-id-2'],
      );
      expect(result).toEqual([
        { id: 'rule-id-1', title: { value: 'Rule 1' }, is_bookmarked: true },
        { id: 'rule-id-2', title: { value: 'Rule 2' }, is_bookmarked: false },
      ]);
    });

    it('should return empty array if no rules provided', async () => {
      // Act
      const result = await service.addBookmarkInfoToRules([], 'user-id-1');

      // Assert
      expect(bookmarkRepository.checkUserBookmarks).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });

  describe('getUserBookmarkedRuleIds', () => {
    it('should return bookmarked rule IDs with pagination', async () => {
      // Arrange
      const bookmarks = {
        items: [
          {
            rule_id: 'rule-id-1',
            user_id: 'user-id-1',
            group_id: 'group-id-1',
            created_at: Date.now(),
          },
          {
            rule_id: 'rule-id-2',
            user_id: 'user-id-1',
            group_id: 'group-id-1',
            created_at: Date.now(),
          },
        ],
        total: 2,
      };

      bookmarkRepository.findByUserId.mockResolvedValue(bookmarks);

      // Act
      const result = await service.getUserBookmarkedRuleIds(
        'user-id-1',
        ['group-id-1'],
        10,
        100,
      );

      // Assert
      expect(bookmarkRepository.findByUserId).toHaveBeenCalledWith(
        'user-id-1',
        ['group-id-1'],
        10,
        100,
      );
      expect(result).toEqual({
        ruleIds: ['rule-id-1', 'rule-id-2'],
        total: 2,
      });
    });

    it('should handle page-based pagination', async () => {
      // Arrange
      const bookmarks = {
        items: [
          {
            rule_id: 'rule-id-3',
            user_id: 'user-id-1',
            group_id: 'group-id-1',
            created_at: Date.now(),
          },
        ],
        total: 1,
      };

      bookmarkRepository.findByUserId.mockResolvedValue(bookmarks);

      // Act
      const result = await service.getUserBookmarkedRuleIds(
        'user-id-1',
        [],
        5,
        100,
      );

      // Assert
      expect(bookmarkRepository.findByUserId).toHaveBeenCalledWith(
        'user-id-1',
        [],
        5,
        100,
      );
      expect(result).toEqual({
        ruleIds: ['rule-id-3'],
        total: 1,
      });
    });

    it('should handle repository errors', async () => {
      // Arrange
      const error = new Error('Repository error');
      bookmarkRepository.findByUserId.mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.getUserBookmarkedRuleIds('user-id-1'),
      ).rejects.toThrow(error);
    });
  });

  describe('getBookmarkMapForRules', () => {
    it('should return a map of rule IDs to bookmark status', async () => {
      // Arrange
      const userId = 'user-id-1';
      const ruleIds = ['rule-id-1', 'rule-id-2', 'rule-id-3'];
      const bookmarkMap = new Map([
        ['rule-id-1', true],
        ['rule-id-3', true],
      ]);

      bookmarkRepository.checkUserBookmarks.mockResolvedValue(bookmarkMap);

      // Act
      const result = await service.getBookmarkMapForRules(userId, ruleIds);

      // Assert
      expect(bookmarkRepository.checkUserBookmarks).toHaveBeenCalledWith(
        userId,
        ruleIds,
      );
      expect(result).toBe(bookmarkMap);
      expect(result.get('rule-id-1')).toBe(true);
      expect(result.get('rule-id-2')).toBeUndefined();
      expect(result.get('rule-id-3')).toBe(true);
    });

    it('should return empty map when no rule IDs are provided', async () => {
      // Act
      const result = await service.getBookmarkMapForRules('user-id-1', []);

      // Assert
      expect(bookmarkRepository.checkUserBookmarks).not.toHaveBeenCalled();
      expect(result).toBeInstanceOf(Map);
      expect(result.size).toBe(0);
    });

    it('should handle repository errors', async () => {
      // Arrange
      const error = new Error('Repository error');
      bookmarkRepository.checkUserBookmarks.mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.getBookmarkMapForRules('user-id-1', ['rule-id-1']),
      ).rejects.toThrow(error);
      expect(bookmarkRepository.checkUserBookmarks).toHaveBeenCalledWith(
        'user-id-1',
        ['rule-id-1'],
      );
    });
  });

  describe('deleteBookmarksForRule', () => {
    it('should delete all bookmarks for a rule', async () => {
      // Arrange
      bookmarkRepository.deleteAllForRule.mockResolvedValue(5);

      // Act
      const result = await service.deleteBookmarksForRule('rule-id-1');

      // Assert
      expect(bookmarkRepository.deleteAllForRule).toHaveBeenCalledWith(
        'rule-id-1',
      );
      expect(result).toBe(5);
    });

    it('should handle errors when deleting bookmarks', async () => {
      // Arrange
      const error = new Error('Database error');
      bookmarkRepository.deleteAllForRule.mockRejectedValue(error);

      // Act & Assert
      await expect(service.deleteBookmarksForRule('rule-id-1')).rejects.toThrow(
        error,
      );
      expect(bookmarkRepository.deleteAllForRule).toHaveBeenCalledWith(
        'rule-id-1',
      );
    });
  });
});
